/*
 * Function: ?MoveBreak@CCharacter@@QEAAXM@Z
 * Address: 0x1401730B0
 */

void __fastcall CCharacter::MoveBreak(CCharacter *this, float fSpeed)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  int v6; // [sp+24h] [bp-14h]@4
  int v7; // [sp+28h] [bp-10h]@4
  float v8; // [sp+2Ch] [bp-Ch]@9
  CCharacter *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = v9->m_nTotalObjIndex % 10;
  v6 = unk_141470B78;
  v7 = 0;
  if ( unk_141470B78 != v5 )
  {
    if ( v6 <= v5 )
      v7 = 10 - (v5 - v6);
    else
      v7 = v6 - v5;
    if ( v7 > 0 )
    {
      v8 = (float)v7 / 10.0;
      CCharacter::Move(v9, fSpeed * v8);
    }
  }
}
