/*
 * Function: ??0?$_Vector_val@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@IEAA@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@1@@Z
 * Address: 0x1403D14C0
 */

void __fastcall std::_Vector_val<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Vector_val<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(std::_Vector_val<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_val<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v5; // [sp+30h] [bp+8h]@1
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *__formal; // [sp+38h] [bp+10h]@1

  __formal = (std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *)_Al;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Container_base::_Container_base((std::_Container_base *)&v5->_Myfirstiter);
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::allocator<GUILD_BATTLE::CGuildBattleRewardItem>(
    &v5->_Alval,
    __formal);
}
