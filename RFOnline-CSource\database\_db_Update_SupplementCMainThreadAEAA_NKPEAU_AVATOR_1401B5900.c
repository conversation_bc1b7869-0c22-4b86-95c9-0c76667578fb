/*
 * Function: ?_db_Update_Supplement@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEADH@Z
 * Address: 0x1401B5900
 */

char __fastcall CMainThread::_db_Update_Supplement(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szSupplementQuery, int nSize)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v8; // eax@32
  int v9; // eax@54
  int v10; // eax@55
  size_t v11; // rax@57
  __int64 v13; // [sp+0h] [bp-148h]@1
  unsigned __int64 v14; // [sp+20h] [bp-128h]@21
  unsigned int v15; // [sp+28h] [bp-120h]@54
  unsigned int v16; // [sp+30h] [bp-118h]@54
  unsigned int v17; // [sp+38h] [bp-110h]@54
  unsigned int v18; // [sp+40h] [bp-108h]@54
  unsigned __int64 v19; // [sp+48h] [bp-100h]@54
  unsigned __int64 v20; // [sp+50h] [bp-F8h]@55
  char DstBuf; // [sp+70h] [bp-D8h]@4
  char v22; // [sp+71h] [bp-D7h]@4
  int v23; // [sp+F4h] [bp-54h]@4
  unsigned int j; // [sp+F8h] [bp-50h]@22
  unsigned int v25; // [sp+FCh] [bp-4Ch]@27
  unsigned int v26; // [sp+100h] [bp-48h]@40
  unsigned int v27; // [sp+104h] [bp-44h]@40
  unsigned int v28; // [sp+108h] [bp-40h]@49
  unsigned int v29; // [sp+118h] [bp-30h]@7
  int v30; // [sp+11Ch] [bp-2Ch]@32
  __int64 v31; // [sp+120h] [bp-28h]@54
  __int64 v32; // [sp+128h] [bp-20h]@55
  unsigned int v33; // [sp+130h] [bp-18h]@55
  unsigned __int64 v34; // [sp+138h] [bp-10h]@4
  unsigned int v35; // [sp+158h] [bp+10h]@1
  _AVATOR_DATA *v36; // [sp+160h] [bp+18h]@1
  _AVATOR_DATA *v37; // [sp+168h] [bp+20h]@1

  v37 = pOldData;
  v36 = pNewData;
  v35 = dwSerial;
  v6 = &v13;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v34 = (unsigned __int64)&v13 ^ _security_cookie;
  DstBuf = 0;
  memset(&v22, 0, 0x7Fui64);
  sprintf_s(szSupplementQuery, nSize, "UPDATE tbl_supplement Set ");
  v23 = strlen_0(szSupplementQuery);
  if ( v37->dbSupplement.dPvpPointLeak != v36->dbSupplement.dPvpPointLeak )
  {
    sprintf_s(&DstBuf, 0x80ui64, "PvpPointLeak=%f,", v36->dbSupplement.dPvpPointLeak);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  if ( v37->dbSupplement.bLastAttBuff != v36->dbSupplement.bLastAttBuff )
  {
    v29 = v36->dbSupplement.bLastAttBuff != 0;
    sprintf_s(&DstBuf, 0x80ui64, "LastAttBuff=%d,", v29);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  if ( v37->dbSupplement.dwBufPotionEndTime != v36->dbSupplement.dwBufPotionEndTime )
  {
    sprintf_s(&DstBuf, 0x80ui64, "BufEndTime = %u,", v36->dbSupplement.dwBufPotionEndTime);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  if ( v37->dbSupplement.dwRaceBuffClear != v36->dbSupplement.dwRaceBuffClear )
  {
    sprintf_s(&DstBuf, 0x80ui64, "RaceBuffClear = %u,", v36->dbSupplement.dwRaceBuffClear);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  if ( v37->dbSupplement.dwAccumPlayTime != v36->dbSupplement.dwAccumPlayTime )
  {
    sprintf_s(&DstBuf, 0x80ui64, " AccumPlayTime = %d,", v36->dbSupplement.dwAccumPlayTime);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  if ( v37->dbSupplement.dwLastResetDate != v36->dbSupplement.dwLastResetDate )
  {
    sprintf_s(&DstBuf, 0x80ui64, " ResetAccumPlayTime = %d,", v36->dbSupplement.dwLastResetDate);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  if ( v37->dbSupplement.byVoted != v36->dbSupplement.byVoted )
  {
    sprintf_s(&DstBuf, 0x80ui64, " IsVoted = %d,", v36->dbSupplement.byVoted);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  if ( v37->dbSupplement.VoteEnable != v36->dbSupplement.VoteEnable )
  {
    sprintf_s(&DstBuf, 0x80ui64, " VoteEnable = %d,", v36->dbSupplement.VoteEnable);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  if ( v37->dbSupplement.wScanerCnt != v36->dbSupplement.wScanerCnt )
  {
    LODWORD(v14) = v36->dbSupplement.wScanerCnt;
    sprintf_s(&DstBuf, 0x80ui64, " ScanerCnt = %d%d,", v36->dbSupplement.dwScanerGetDate);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  for ( j = 0; (signed int)j < 3; ++j )
  {
    if ( v37->dbSupplement.dwActionPoint[j] != v36->dbSupplement.dwActionPoint[j] )
    {
      LODWORD(v14) = v36->dbSupplement.dwActionPoint[j];
      sprintf_s(&DstBuf, 0x80ui64, " ActionPoint_%d = %d,", j);
      strcat_s(szSupplementQuery, nSize, &DstBuf);
    }
  }
  v25 = 6;
  if ( _EMBELLKEY::IsFilled(&v36->dbEquip.m_EmbellishList[6].Key) )
  {
    if ( _EMBELLKEY::IsFilled((_EMBELLKEY *)((char *)&v37->dbEquip + 27 * (signed int)v25)) )
    {
      v30 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v36->dbEquip + 27 * (signed int)v25));
      v8 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v37->dbEquip + 27 * (signed int)v25));
      if ( v30 != v8 )
      {
        LODWORD(v14) = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v36->dbEquip + 27 * (signed int)v25));
        sprintf_s(&DstBuf, 0x80ui64, "EK%d=%d,", v25);
        strcat_s(szSupplementQuery, nSize, &DstBuf);
      }
      if ( v36->dbEquip.m_EmbellishList[v25].wAmount != v37->dbEquip.m_EmbellishList[v25].wAmount )
      {
        if ( (signed int)v36->dbEquip.m_EmbellishList[v25].wAmount < 0xFFFF )
        {
          LODWORD(v14) = v36->dbEquip.m_EmbellishList[v25].wAmount;
          sprintf_s(&DstBuf, 0x80ui64, "ED%d=%d,", v25);
        }
        else
        {
          sprintf_s(&DstBuf, 0x80ui64, "ED%d=-1,", v25);
        }
        strcat_s(szSupplementQuery, nSize, &DstBuf);
      }
      if ( v36->dbEquip.m_EmbellishList[v25].byCsMethod )
      {
        v26 = 0;
        v27 = 0;
        if ( v36->dbEquip.m_EmbellishList[v25].byCsMethod == 1 )
        {
          v26 = v36->dbEquip.m_EmbellishList[v25].dwT - Time;
          v27 = v37->dbEquip.m_EmbellishList[v25].dwT - Time;
        }
        else if ( v36->dbEquip.m_EmbellishList[j].byCsMethod == 2 )
        {
          v26 = v36->dbEquip.m_EmbellishList[v25].dwT;
          v27 = v37->dbEquip.m_EmbellishList[v25].dwT;
        }
        if ( v26 != v27 )
        {
          LODWORD(v14) = v26;
          sprintf_s(&DstBuf, 0x80ui64, "ET%d=%d,", v25);
          strcat_s(szSupplementQuery, nSize, &DstBuf);
        }
      }
      if ( v36->dbEquip.m_EmbellishList[v25].lnUID != v37->dbEquip.m_EmbellishList[v25].lnUID )
      {
        v14 = v36->dbEquip.m_EmbellishList[v25].lnUID;
        sprintf_s(&DstBuf, 0x80ui64, "ES%d=%I64d,", v25);
        strcat_s(szSupplementQuery, nSize, &DstBuf);
      }
    }
    else
    {
      v28 = 0;
      if ( v36->dbEquip.m_EmbellishList[v25].byCsMethod == 1 )
      {
        v28 = v36->dbEquip.m_EmbellishList[v25].dwT - Time;
      }
      else if ( v36->dbEquip.m_EmbellishList[v25].byCsMethod == 2 )
      {
        v28 = v36->dbEquip.m_EmbellishList[v25].dwT;
      }
      if ( (signed int)v36->dbEquip.m_EmbellishList[v25].wAmount < 0xFFFF )
      {
        v32 = 27i64 * (signed int)v25;
        v33 = v36->dbEquip.m_EmbellishList[v25].wAmount;
        v10 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v36->dbEquip + 27 * (signed int)v25));
        v20 = v36->dbEquip.m_EmbellishList[(unsigned __int64)v32 / 0x1B].lnUID;
        LODWORD(v19) = v25;
        v18 = v28;
        v17 = v25;
        v16 = v33;
        v15 = v25;
        LODWORD(v14) = v10;
        sprintf_s(&DstBuf, 0x80ui64, "EK%d=%d,ED%d=%d,ET%d=%d,ES%d=%I64d,", v25);
      }
      else
      {
        v31 = 27i64 * (signed int)v25;
        v9 = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v36->dbEquip + 27 * (signed int)v25));
        v19 = v36->dbEquip.m_EmbellishList[(unsigned __int64)v31 / 0x1B].lnUID;
        v18 = v25;
        v17 = v28;
        v16 = v25;
        v15 = v25;
        LODWORD(v14) = v9;
        sprintf_s(&DstBuf, 0x80ui64, "EK%d=%d,ED%d=-1,ET%d=%d,ES%d=%I64d,", v25);
      }
      strcat_s(szSupplementQuery, nSize, &DstBuf);
    }
  }
  else if ( _EMBELLKEY::IsFilled((_EMBELLKEY *)((char *)&v37->dbEquip + 27 * (signed int)v25)) )
  {
    LODWORD(v14) = _EMBELLKEY::CovDBKey((_EMBELLKEY *)((char *)&v36->dbEquip + 27 * (signed int)v25));
    sprintf_s(&DstBuf, 0x80ui64, "EK%d=%d,", v25);
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  v11 = strlen_0(szSupplementQuery);
  if ( v11 <= v23 )
  {
    memset_0(szSupplementQuery, 0, v23);
  }
  else
  {
    sprintf_s(&DstBuf, 0x80ui64, "WHERE Serial = %d", v35);
    szSupplementQuery[strlen_0(szSupplementQuery) - 1] = 32;
    strcat_s(szSupplementQuery, nSize, &DstBuf);
  }
  return 1;
}
