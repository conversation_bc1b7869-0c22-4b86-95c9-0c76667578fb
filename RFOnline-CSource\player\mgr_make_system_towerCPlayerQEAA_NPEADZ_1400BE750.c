/*
 * Function: ?mgr_make_system_tower@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BE750
 */

char __fastcall CPlayer::mgr_make_system_tower(CPlayer *this, char *pszTowerCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-3D8h]@1
  DWORD nSize; // [sp+20h] [bp-3B8h]@17
  LPCSTR lpFileName; // [sp+28h] [bp-3B0h]@17
  char wszTran; // [sp+40h] [bp-398h]@4
  _base_fld *v9; // [sp+C8h] [bp-310h]@4
  int v10; // [sp+D0h] [bp-308h]@8
  int j; // [sp+D4h] [bp-304h]@8
  LPCSTR lpAppName; // [sp+E8h] [bp-2F0h]@15
  const char *v13; // [sp+F0h] [bp-2E8h]@15
  const char *v14; // [sp+F8h] [bp-2E0h]@15
  const char *v15; // [sp+118h] [bp-2C0h]@15
  const char *v16; // [sp+120h] [bp-2B8h]@15
  const char *v17; // [sp+128h] [bp-2B0h]@15
  int nIniIndex; // [sp+134h] [bp-2A4h]@15
  char ReturnedString; // [sp+150h] [bp-288h]@17
  char Dest; // [sp+1F0h] [bp-1E8h]@17
  char KeyName; // [sp+290h] [bp-148h]@24
  char String; // [sp+330h] [bp-A8h]@24
  unsigned __int64 v23; // [sp+3C0h] [bp-18h]@4
  CPlayer *v24; // [sp+3E0h] [bp+8h]@1
  const char *lpStr; // [sp+3E8h] [bp+10h]@1

  lpStr = pszTowerCode;
  v24 = this;
  v2 = &v5;
  for ( i = 244i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v23 = (unsigned __int64)&v5 ^ _security_cookie;
  M2W(pszTowerCode, &wszTran, 0x80u);
  v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 25, lpStr);
  if ( v9 )
  {
    if ( v24->m_pCurMap->m_pMapSet->m_nMapType )
    {
      result = 0;
    }
    else
    {
      v10 = -1;
      for ( j = 0; j < 3; ++j )
      {
        if ( v9[3].m_strCode[2 * j + 52] == 49 )
        {
          v10 = j;
          break;
        }
      }
      if ( v10 == -1 )
      {
        result = 0;
      }
      else
      {
        lpAppName = "BELLATO";
        v13 = "CORA";
        v14 = "ACCRETIA";
        v15 = "BELLATO";
        v16 = "CORA";
        v17 = "ACCRETIA";
        nIniIndex = -1;
        for ( j = 0; j < 50; ++j )
        {
          sprintf(&Dest, "Map%d", (unsigned int)j);
          lpFileName = ".\\Script\\SystemGuardTower.ini";
          nSize = 128;
          GetPrivateProfileStringA(
            (&lpAppName)[8 * v10],
            &Dest,
            "NULL",
            &ReturnedString,
            0x80u,
            ".\\Script\\SystemGuardTower.ini");
          if ( !strcmp_0(&ReturnedString, "NULL") )
          {
            nIniIndex = j;
            break;
          }
        }
        if ( nIniIndex == -1 )
        {
          result = 0;
        }
        else
        {
          LODWORD(lpFileName) = nIniIndex;
          LOBYTE(nSize) = v10;
          if ( CreateSystemTower(v24->m_pCurMap, v24->m_wMapLayerIndex, v24->m_fCurPos, v9->m_dwIndex, v10, nIniIndex) )
          {
            sprintf(&KeyName, "Map%d", (unsigned int)nIniIndex);
            WritePrivateProfileStringA(
              (&lpAppName)[8 * v10],
              &KeyName,
              v24->m_pCurMap->m_pMapSet->m_strCode,
              ".\\Script\\SystemGuardTower.ini");
            sprintf(&KeyName, "Pos%d_x", (unsigned int)nIniIndex);
            _itoa((signed int)ffloor(v24->m_fCurPos[0]), &String, 10);
            WritePrivateProfileStringA((&lpAppName)[8 * v10], &KeyName, &String, ".\\Script\\SystemGuardTower.ini");
            sprintf(&KeyName, "Pos%d_y", (unsigned int)nIniIndex);
            _itoa((signed int)ffloor(v24->m_fCurPos[1]), &String, 10);
            WritePrivateProfileStringA((&lpAppName)[8 * v10], &KeyName, &String, ".\\Script\\SystemGuardTower.ini");
            sprintf(&KeyName, "Pos%d_z", (unsigned int)nIniIndex);
            _itoa((signed int)ffloor(v24->m_fCurPos[2]), &String, 10);
            WritePrivateProfileStringA((&lpAppName)[8 * v10], &KeyName, &String, ".\\Script\\SystemGuardTower.ini");
            sprintf(&KeyName, "Code%d", (unsigned int)nIniIndex);
            WritePrivateProfileStringA(
              (&lpAppName)[8 * v10],
              &KeyName,
              v9->m_strCode,
              ".\\Script\\SystemGuardTower.ini");
            result = 1;
          }
          else
          {
            result = 0;
          }
        }
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
