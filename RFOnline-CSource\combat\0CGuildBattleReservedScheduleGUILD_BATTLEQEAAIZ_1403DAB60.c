/*
 * Function: ??0CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAA@I@Z
 * Address: 0x1403DAB60
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::CGuildBattleReservedSchedule(GUILD_BATTLE::CGuildBattleReservedSchedule *this, unsigned int uiScheduleListID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedSchedule *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->m_uiScheduleListID = uiScheduleListID;
  GUILD_BATTLE::CGuildBattleReservedSchedule::Clear(v5);
}
