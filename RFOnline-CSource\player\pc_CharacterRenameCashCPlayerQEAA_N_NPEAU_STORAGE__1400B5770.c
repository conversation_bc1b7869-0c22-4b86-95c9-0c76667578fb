/*
 * Function: ?pc_CharacterRenameCash@CPlayer@@QEAA_N_NPEAU_STORAGE_POS_INDIV@@PEBD@Z
 * Address: 0x1400B5770
 */

char __fastcall CPlayer::pc_CharacterRenameCash(CPlayer *this, bool b<PERSON>hange, _STORAGE_POS_INDIV *pItem, const char *strCharacterName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v7; // eax@20
  char *v8; // rax@26
  __int64 v9; // [sp+0h] [bp-F8h]@1
  char *pQryData; // [sp+20h] [bp-D8h]@26
  int nSize; // [sp+28h] [bp-D0h]@26
  char Dst; // [sp+34h] [bp-C4h]@7
  _STORAGE_LIST::_db_con *ppItem; // [sp+58h] [bp-A0h]@11
  char v14; // [sp+64h] [bp-94h]@11
  int nCashType; // [sp+68h] [bp-90h]@15
  _qry_case_character_rename v16; // [sp+78h] [bp-80h]@20
  _PotionItem_fld *pfB; // [sp+B8h] [bp-40h]@25
  unsigned int nCurTime; // [sp+C0h] [bp-38h]@25
  _base_fld *v19; // [sp+C8h] [bp-30h]@27
  int v20; // [sp+D8h] [bp-20h]@26
  int v21; // [sp+DCh] [bp-1Ch]@26
  unsigned __int64 v22; // [sp+E0h] [bp-18h]@4
  CPlayer *pUsePlayer; // [sp+100h] [bp+8h]@1
  bool v24; // [sp+108h] [bp+10h]@1
  char *strCharacterNamea; // [sp+118h] [bp+20h]@1

  strCharacterNamea = (char *)strCharacterName;
  v24 = bChange;
  pUsePlayer = this;
  v4 = &v9;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v22 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( pItem )
  {
    if ( bChange )
      memcpy_s(&Dst, 4ui64, &pUsePlayer->m_ReNamePotionUseInfo, 4ui64);
    else
      memcpy_s(&Dst, 4ui64, pItem, 4ui64);
    if ( Dst )
    {
      result = 0;
    }
    else
    {
      ppItem = 0i64;
      v14 = CPlayer::pc_RenameItemNConditionCheck(pUsePlayer, (_STORAGE_POS_INDIV *)&Dst, &ppItem);
      if ( v14 )
      {
        CPlayer::SendMsg_CharacterRenameCashResult(pUsePlayer, v24, v14);
        result = 0;
      }
      else if ( ppItem )
      {
        nCashType = GetUsePcCashType(ppItem->m_byTableCode, ppItem->m_wItemIndex);
        if ( CPlayer::IsUsableAccountType(pUsePlayer, nCashType) )
        {
          if ( v24 )
          {
            if ( pUsePlayer->m_ReNamePotionUseInfo.wszChangeName[0] )
            {
              pfB = 0i64;
              nCurTime = timeGetTime();
              pfB = (_PotionItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 13, ppItem->m_wItemIndex);
              if ( pfB )
              {
                v19 = CRecordData::GetRecord(&stru_184A6FC18, pfB->m_strEffCode);
                if ( v19 && !v14 && v19[13].m_dwIndex == 47 )
                {
                  _STORAGE_LIST::_storage_con::lock((_STORAGE_LIST::_storage_con *)&ppItem->m_bLoad, 1);
                  v14 = CPotionMgr::UsePotion(&g_PotionMgr, pUsePlayer, (CCharacter *)&pUsePlayer->vfptr, pfB, nCurTime);
                  if ( v14 )
                  {
                    _RENAME_POTION_USE_INFO::Init(&pUsePlayer->m_ReNamePotionUseInfo);
                    CPlayer::SendMsg_CharacterRenameCashResult(pUsePlayer, 1, v14);
                    result = 0;
                  }
                  else
                  {
                    result = 1;
                  }
                }
                else
                {
                  _RENAME_POTION_USE_INFO::Init(&pUsePlayer->m_ReNamePotionUseInfo);
                  CPlayer::SendMsg_CharacterRenameCashResult(pUsePlayer, 1, 9);
                  result = 0;
                }
              }
              else
              {
                _RENAME_POTION_USE_INFO::Init(&pUsePlayer->m_ReNamePotionUseInfo);
                v20 = ppItem->m_wItemIndex;
                v21 = ppItem->m_byTableCode;
                v8 = CPlayerDB::GetCharNameA(&pUsePlayer->m_Param);
                nSize = v20;
                LODWORD(pQryData) = v21;
                CLogFile::Write(
                  &stru_1799C8E78,
                  "CPlayer::pc_CharacterRenameCash(...) : User %s(%u) Item Table(%u) Index(%u) Not Exist!",
                  v8,
                  pUsePlayer->m_dwObjSerial);
                v14 = 48;
                result = 0;
              }
            }
            else
            {
              _RENAME_POTION_USE_INFO::Init(&pUsePlayer->m_ReNamePotionUseInfo);
              CPlayer::SendMsg_CharacterRenameCashResult(pUsePlayer, 1, 48);
              result = 0;
            }
          }
          else
          {
            v14 = CPlayer::pc_CharacterRenameCheck(pUsePlayer, strCharacterNamea);
            if ( v14 )
            {
              CPlayer::SendMsg_CharacterRenameCashResult(pUsePlayer, 0, v14);
            }
            else
            {
              _STORAGE_LIST::_storage_con::lock((_STORAGE_LIST::_storage_con *)&ppItem->m_bLoad, 1);
              _qry_case_character_rename::_qry_case_character_rename(&v16);
              v16.dwCharSerial = pUsePlayer->m_pUserDB->m_dwSerial;
              v16.dwAlreadySerial = -1;
              memcpy_s(&v16.ItemInfo, 4ui64, &Dst, 4ui64);
              strcpy_s(v16.wszCharName, 0x11ui64, strCharacterNamea);
              v7 = _qry_case_character_rename::size(&v16);
              if ( !CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -111, (char *)&v16, v7) )
                CPlayer::SendMsg_CharacterRenameCashResult(pUsePlayer, 0, 10);
            }
            result = 1;
          }
        }
        else
        {
          CPlayer::SendMsg_PremiumCashItemUse(pUsePlayer, 0xFFFFu);
          result = 1;
        }
      }
      else
      {
        CPlayer::SendMsg_CharacterRenameCashResult(pUsePlayer, v24, 1);
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
