/*
 * Function: ?pc_ChatRaceRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x140091E70
 */

void __fastcall CPlayer::pc_ChatRaceRequest(CPlayer *this, char *pwszChatData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@9
  char *v5; // rax@10
  CChatStealSystem *v6; // rax@10
  int v7; // eax@15
  __int64 v8; // [sp+0h] [bp-1B8h]@1
  _announ_message_receipt_udp Dst; // [sp+40h] [bp-178h]@10
  char pbyType; // [sp+174h] [bp-44h]@10
  char v11; // [sp+175h] [bp-43h]@10
  int v12; // [sp+184h] [bp-34h]@10
  int j; // [sp+188h] [bp-30h]@10
  CPlayer *v14; // [sp+190h] [bp-28h]@13
  int v15; // [sp+1A0h] [bp-18h]@7
  int v16; // [sp+1A4h] [bp-14h]@15
  unsigned __int64 v17; // [sp+1A8h] [bp-10h]@4
  CPlayer *pPlayer; // [sp+1C0h] [bp+8h]@1
  const char *Str; // [sp+1C8h] [bp+10h]@1

  Str = pwszChatData;
  pPlayer = this;
  v2 = &v8;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( pPlayer->m_pUserDB )
  {
    if ( !pPlayer->m_pUserDB->m_bChatLock && !CPlayer::IsPunished(pPlayer, 0, 1) )
    {
      v15 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
      v4 = CPvpUserAndGuildRankingSystem::Instance();
      if ( CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(v4, v15, pPlayer->m_dwObjSerial) )
      {
        _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
        Dst.byMessageType = 4;
        Dst.bySenderRace = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
        Dst.dwSenderSerial = pPlayer->m_dwObjSerial;
        v5 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
        strcpy_0(Dst.wszSenderName, v5);
        Dst.bySize = strlen_0(Str);
        memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
        Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
        Dst.byPvpGrade = -1;
        pbyType = 2;
        v11 = 11;
        v12 = _announ_message_receipt_udp::size(&Dst);
        v6 = CChatStealSystem::Instance();
        CChatStealSystem::StealChatMsg(v6, pPlayer, Dst.byMessageType, (char *)Str);
        for ( j = 0; j < 2532; ++j )
        {
          v14 = &g_Player + j;
          if ( v14->m_bLive )
          {
            if ( v14->m_byUserDgr >= 2
              || (v16 = CPlayerDB::GetRaceCode(&v14->m_Param), v7 = CPlayerDB::GetRaceCode(&pPlayer->m_Param), v16 == v7) )
            {
              CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v12);
            }
          }
        }
      }
    }
  }
}
