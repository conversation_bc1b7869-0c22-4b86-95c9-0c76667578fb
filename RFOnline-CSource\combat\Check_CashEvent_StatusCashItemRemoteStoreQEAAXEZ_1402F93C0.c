/*
 * Function: ?Check_CashEvent_Status@CashItemRemoteStore@@QEAAXE@Z
 * Address: 0x1402F93C0
 */

void __fastcall CashItemRemoteStore::Check_CashEvent_Status(CashItemRemoteStore *this, char byEventType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  char v5; // [sp+30h] [bp-38h]@4
  __time32_t Time; // [sp+44h] [bp-24h]@4
  bool v7; // [sp+54h] [bp-14h]@8
  int v8; // [sp+58h] [bp-10h]@4
  CashItemRemoteStore *v9; // [sp+70h] [bp+8h]@1
  char v10; // [sp+78h] [bp+10h]@1

  v10 = byEventType;
  v9 = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = CashItemRemoteStore::Get_CashEvent_Status(v9, byEventType);
  _time32(&Time);
  v8 = (unsigned __int8)v5;
  switch ( v5 )
  {
    case 0:
      if ( v9->m_cash_event[(unsigned __int8)v10].m_ini.m_bUseCashEvent
        && v9->m_cash_event[(unsigned __int8)v10].m_ini.m_bRepeat )
      {
        v7 = CashItemRemoteStore::ChangeEventTime(v9, v10);
        if ( v9->m_cash_event[(unsigned __int8)v10].m_ini.m_NextEventTime[1] > Time )
        {
          if ( v7 )
            CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 1);
          else
            CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 5);
        }
        else
        {
          CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 5);
        }
      }
      else
      {
        CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 5);
      }
      break;
    case 1:
      if ( v9->m_cash_event[(unsigned __int8)v10].m_ini.m_bRepeat
        && v9->m_cash_event[(unsigned __int8)v10].m_ini.m_EventTime[2] <= Time )
      {
        CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 5);
      }
      else if ( Time >= v9->m_cash_event[(unsigned __int8)v10].m_ini.m_EventTime[0] )
      {
        CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 2);
        if ( v10 == 2 )
        {
          CashItemRemoteStore::Set_LimitedSale_DCK(v9, v10, 1);
          CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -99, 0i64, 0);
          CashItemRemoteStore::Set_DB_LimitedSale_Event(v9);
        }
        CashItemRemoteStore::Inform_CashEvent_Status_All(v9, v10, 2, &v9->m_cash_event[(unsigned __int8)v10].m_ini);
      }
      break;
    case 2:
      if ( v10 == 2 && v9->m_lim_event.DCK != 1 )
      {
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -99, 0i64, 0);
        CashItemRemoteStore::Set_LimitedSale_Event(v9);
      }
      if ( v9->m_cash_event[(unsigned __int8)v10].m_ini.m_EventTime[1] - Time <= v9->m_cash_event[(unsigned __int8)v10].m_event_inform_before[0] )
      {
        CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 3);
        CashItemRemoteStore::Inform_CashEvent_Status_All(v9, v10, 3, &v9->m_cash_event[(unsigned __int8)v10].m_ini);
      }
      break;
    case 3:
      if ( v10 == 2 && v9->m_lim_event.DCK != 1 )
      {
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -99, 0i64, 0);
        CashItemRemoteStore::Set_LimitedSale_Event(v9);
      }
      if ( v9->m_cash_event[(unsigned __int8)v10].m_ini.m_EventTime[1] - Time <= v9->m_cash_event[(unsigned __int8)v10].m_event_inform_before[1] )
      {
        CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 4);
        CashItemRemoteStore::Inform_CashEvent_Status_All(v9, v10, 4, &v9->m_cash_event[(unsigned __int8)v10].m_ini);
      }
      break;
    case 4:
      if ( v10 == 2 && v9->m_lim_event.DCK != 1 )
      {
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -99, 0i64, 0);
        CashItemRemoteStore::Set_LimitedSale_Event(v9);
      }
      if ( Time >= v9->m_cash_event[(unsigned __int8)v10].m_ini.m_EventTime[1] )
      {
        CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 5);
        if ( v10 == 2 )
        {
          CashItemRemoteStore::Set_LimitedSale_DCK(v9, v10, 0);
          CashItemRemoteStore::Set_DB_LimitedSale_Event(v9);
        }
        CashItemRemoteStore::Inform_CashEvent_Status_All(v9, v10, 5, &v9->m_cash_event[(unsigned __int8)v10].m_ini);
      }
      break;
    case 5:
      if ( v9->m_cash_event[(unsigned __int8)v10].m_ini.m_bUseCashEvent )
      {
        if ( v9->m_cash_event[(unsigned __int8)v10].m_ini.m_bRepeat )
        {
          if ( v9->m_cash_event[(unsigned __int8)v10].m_ini.m_EventTime[2] > Time )
          {
            if ( CashItemRemoteStore::SetNextEventTime(v9, v10) )
              CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 0);
            else
              CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 7);
          }
          else
          {
            v9->m_cash_event[(unsigned __int8)v10].m_ini.m_bUseCashEvent = 0;
            v9->m_cash_event[(unsigned __int8)v10].m_ini.m_bRepeat = 0;
            CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 7);
          }
        }
        else
        {
          CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 7);
        }
      }
      else
      {
        CashItemRemoteStore::Set_CashEvent_Status(v9, v10, 7);
      }
      break;
    default:
      return;
  }
}
