/*
 * Function: ?_db_Load_General@CMainThread@@AEAAEKEPEAU_AVATOR_DATA@@@Z
 * Address: 0x1401A6870
 */

char __fastcall CMainThread::_db_Load_General(CMainThread *this, unsigned int dwSerial, char byRaceCode, _AVATOR_DATA *pCon)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int16 v7; // ax@12
  __int64 v8; // [sp+0h] [bp-BF8h]@1
  unsigned int k; // [sp+20h] [bp-BD8h]@4
  char Dst; // [sp+40h] [bp-BB8h]@4
  char v11; // [sp+41h] [bp-BB7h]@4
  char v12; // [sp+42h] [bp-BB6h]@12
  char v13; // [sp+43h] [bp-BB5h]@12
  _worlddb_character_general_info pCharacterData; // [sp+E0h] [bp-B18h]@4
  char v15; // [sp+AE4h] [bp-114h]@4
  char Dest[212]; // [sp+B00h] [bp-F8h]@8
  int v17; // [sp+BD4h] [bp-24h]@9
  int v18; // [sp+BD8h] [bp-20h]@10
  int j; // [sp+BDCh] [bp-1Ch]@10
  unsigned __int64 v20; // [sp+BE8h] [bp-10h]@4
  CMainThread *v21; // [sp+C00h] [bp+8h]@1
  unsigned int dwCharacterSerial; // [sp+C08h] [bp+10h]@1
  char v23; // [sp+C10h] [bp+18h]@1
  _AVATOR_DATA *v24; // [sp+C18h] [bp+20h]@1

  v24 = pCon;
  v23 = byRaceCode;
  dwCharacterSerial = dwSerial;
  v21 = this;
  v4 = &v8;
  for ( i = 764i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v20 = (unsigned __int64)&v8 ^ _security_cookie;
  k = 0;
  Dst = 0;
  memset(&v11, 0, 0x7Fui64);
  memset_0(&Dst, 0, 0x80ui64);
  memset_0(&pCharacterData, 0, 0xA00ui64);
  v15 = CRFWorldDatabase::Select_CharacterGeneralInfo(v21->m_pWorldDB, dwCharacterSerial, &pCharacterData);
  if ( v15 == 1 )
  {
    result = 24;
  }
  else if ( v15 == 2 )
  {
    result = 37;
  }
  else
  {
    v24->dbAvator.m_dwHP = pCharacterData.dwHP;
    v24->dbAvator.m_dwFP = pCharacterData.dwFP;
    v24->dbAvator.m_dwSP = pCharacterData.dwSP;
    v24->dbAvator.m_dwDP = pCharacterData.dwDP;
    v24->dbAvator.m_dExp = pCharacterData.dExp;
    v24->dbAvator.m_dLossExp = pCharacterData.dLoseExp;
    v24->dbAvator.m_byBagNum = pCharacterData.byBagNum;
    v24->dbAvator.m_byMapCode = pCharacterData.byMapCode;
    v24->dbAvator.m_fStartPos[0] = pCharacterData.fStartPos[0];
    v24->dbAvator.m_fStartPos[1] = pCharacterData.fStartPos[1];
    v24->dbAvator.m_fStartPos[2] = pCharacterData.fStartPos[2];
    v24->dbAvator.m_dwTotalPlayMin = pCharacterData.dwTotalPlayMin;
    strcpy_0(Dest, pCharacterData.szLeftResList);
    _CUTTING_DB_BASE::Init(&v24->dbCutting);
    if ( Dest[0] != 42 )
    {
      v17 = strlen_0(Dest);
      if ( !(v17 % 5) )
      {
        v24->dbCutting.m_byLeftNum = v17 / 5;
        v18 = 0;
        for ( j = 0; j < v24->dbCutting.m_byLeftNum; ++j )
        {
          memcpy_0(&Dst, &Dest[v18], 2ui64);
          v12 = 0;
          v18 += 2;
          v24->dbCutting.m_List[j].Key.byTableCode = 18;
          v7 = atoi(&Dst);
          v24->dbCutting.m_List[j].Key.wItemIndex = v7;
          memcpy_0(&Dst, &Dest[v18], 3ui64);
          v13 = 0;
          v18 += 3;
          v24->dbCutting.m_List[j].dwDur = atoi(&Dst);
        }
        v24->dbCutting.m_bOldDataLoad = 1;
      }
    }
    for ( k = 0; (signed int)k < 7; ++k )
    {
      _EMBELLKEY::LoadDBKey((_EMBELLKEY *)((char *)&v24->dbEquip + 27 * (signed int)k), pCharacterData.lEK[k]);
      v24->dbEquip.m_EmbellishList[k].wAmount = pCharacterData.wED[k];
      v24->dbEquip.m_EmbellishList[k].dwT = pCharacterData.dwET[k];
      v24->dbEquip.m_EmbellishList[k].lnUID = pCharacterData.lnUID_E[k];
    }
    for ( k = 0; (signed int)k < 88; ++k )
    {
      _FORCEKEY::LoadDBKey((_FORCEKEY *)((char *)&v24->dbForce + 25 * (signed int)k), pCharacterData.lF[k]);
      v24->dbForce.m_List[k].lnUID = pCharacterData.lnUID_F[k];
    }
    if ( v23 == 1 )
    {
      for ( k = 0; (signed int)k < 4; ++k )
      {
        _ANIMUSKEY::LoadDBKey((_ANIMUSKEY *)&v24->dbAnimus + 34 * (signed int)k, pCharacterData.byAK[k]);
        v24->dbAnimus.m_List[k].dwExp = pCharacterData.dwAD[k];
        v24->dbAnimus.m_List[k].dwParam = pCharacterData.dwAP[k];
        v24->dbAnimus.m_List[k].lnUID = pCharacterData.lnUID_E[k];
      }
    }
    for ( k = 0; (signed int)k < 2; ++k )
    {
      sprintf(&Dst, "WM%d", k);
      v24->dbStat.m_dwDamWpCnt[k] = pCharacterData.dwWM[k];
    }
    for ( k = 0; (signed int)k < 24; ++k )
    {
      sprintf(&Dst, "FM%d", k);
      v24->dbStat.m_dwForceCum[k] = pCharacterData.dwFM[k];
    }
    for ( k = 0; (signed int)k < 48; ++k )
    {
      sprintf(&Dst, "SM%d", k);
      v24->dbStat.m_dwSkillCum[k] = pCharacterData.dwSM[k];
    }
    for ( k = 0; (signed int)k < 3; ++k )
    {
      sprintf(&Dst, "MI%d", k);
      v24->dbStat.m_dwMakeCum[k] = pCharacterData.dwMI[k];
    }
    v24->dbStat.m_dwSpecialCum = pCharacterData.dwSR;
    v24->dbStat.m_dwDefenceCnt = pCharacterData.dwDM;
    v24->dbStat.m_dwShieldCnt = pCharacterData.dwPM;
    for ( k = 0; (signed int)k < 3; ++k )
      v24->dbAvator.m_zClassHistory[k] = pCharacterData.zClassHistory[k];
    v24->dbAvator.m_dwClassInitCnt = pCharacterData.dwClassInitCnt;
    v24->dbAvator.m_byLastClassGrade = pCharacterData.byLastClassGrade;
    v24->dbAvator.m_dPvPPoint = pCharacterData.dPvPPoint;
    v24->dbAvator.m_dPvPCashBag = pCharacterData.dPvPCashBag;
    memcpy_0(v24->dbAvator.m_szBindMapCode, pCharacterData.szBindMapCode, 0xCui64);
    v24->dbAvator.m_szBindMapCode[11] = 0;
    memcpy_0(v24->dbAvator.m_szBindDummy, pCharacterData.szBindDummy, 0xCui64);
    v24->dbAvator.m_szBindDummy[11] = 0;
    v24->dbAvator.m_dwGuildSerial = pCharacterData.dwGuildSerial;
    v24->dbAvator.m_byClassInGuild = pCharacterData.byGuildGrade;
    v24->dbAvator.m_dwRadarDelayTime = pCharacterData.dwRadarDelayTime;
    v24->dbAvator.m_dwTakeLastMentalTicket = pCharacterData.dwTakeLastMentalTicket;
    v24->dbAvator.m_dwTakeLastCriTicket = pCharacterData.dwTakeLastCriTicket;
    v24->dbAvator.m_byMaxLevel = pCharacterData.byMaxLevel;
    result = 0;
  }
  return result;
}
