/*
 * Function: ?pc_ChatOperatorRequest@CPlayer@@QEAAXEPEAD@Z
 * Address: 0x140090B00
 */

void __fastcall CPlayer::pc_ChatOperatorRequest(CPlayer *this, char byRaceCode, char *pwszChatData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  int j; // [sp+40h] [bp-18h]@5
  CPlayer *v7; // [sp+48h] [bp-10h]@8
  CPlayer *v8; // [sp+60h] [bp+8h]@1
  char v9; // [sp+68h] [bp+10h]@1
  char *v10; // [sp+70h] [bp+18h]@1

  v10 = pwszChatData;
  v9 = byRaceCode;
  v8 = this;
  v3 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v8->m_byUserDgr == 2 )
  {
    for ( j = 0; j < 2532; ++j )
    {
      v7 = &g_Player + j;
      if ( v7->m_bLive
        && ((unsigned __int8)v9 == 255 || v7->m_byUserDgr || CPlayerDB::GetRaceCode(&v7->m_Param) == (unsigned __int8)v9) )
      {
        CPlayer::SendData_ChatTrans(v7, 0, 0xFFFFFFFF, v9, 0, v10, -1, 0i64);
      }
    }
  }
}
