/*
 * Function: ?_db_Update_Unit@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401AC7C0
 */

char __fastcall CMainThread::_db_Update_Unit(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pSzQuery)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  size_t v7; // rax@40
  __int64 v9; // [sp+0h] [bp-F8h]@1
  unsigned int v10; // [sp+20h] [bp-D8h]@33
  char Source; // [sp+40h] [bp-B8h]@4
  char v12; // [sp+41h] [bp-B7h]@4
  char *Dest; // [sp+C8h] [bp-30h]@4
  size_t Size; // [sp+D0h] [bp-28h]@4
  unsigned int j; // [sp+D8h] [bp-20h]@30
  unsigned __int64 v16; // [sp+E8h] [bp-10h]@4
  unsigned int v17; // [sp+108h] [bp+10h]@1
  _AVATOR_DATA *v18; // [sp+110h] [bp+18h]@1
  _AVATOR_DATA *v19; // [sp+118h] [bp+20h]@1

  v19 = pOldData;
  v18 = pNewData;
  v17 = dwSerial;
  v5 = &v9;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v16 = (unsigned __int64)&v9 ^ _security_cookie;
  Source = 0;
  memset(&v12, 0, 0x7Fui64);
  Dest = pSzQuery;
  sprintf(pSzQuery, "UPDATE tbl_unit SET ");
  for ( Size = (unsigned int)strlen_0(Dest); SHIDWORD(Size) < 4; ++HIDWORD(Size) )
  {
    if ( v18->dbUnit.m_List[SHIDWORD(Size)].byFrame == 255 )
    {
      if ( v19->dbUnit.m_List[SHIDWORD(Size)].byFrame != 255 )
      {
        sprintf(&Source, "F_%d=%d,", HIDWORD(Size), 255i64);
        strcat_0(Dest, &Source);
      }
    }
    else
    {
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].byFrame != v19->dbUnit.m_List[SHIDWORD(Size)].byFrame )
      {
        sprintf(&Source, "F_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].byFrame);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].dwGauge != v19->dbUnit.m_List[SHIDWORD(Size)].dwGauge )
      {
        sprintf(&Source, "Gg_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].dwGauge);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].byPart[0] != v19->dbUnit.m_List[SHIDWORD(Size)].byPart[0] )
      {
        sprintf(&Source, "H_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].byPart[0]);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].byPart[1] != v19->dbUnit.m_List[SHIDWORD(Size)].byPart[1] )
      {
        sprintf(&Source, "U_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].byPart[1]);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].byPart[2] != v19->dbUnit.m_List[SHIDWORD(Size)].byPart[2] )
      {
        sprintf(&Source, "L_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].byPart[2]);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].byPart[3] != v19->dbUnit.m_List[SHIDWORD(Size)].byPart[3] )
      {
        sprintf(&Source, "A_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].byPart[3]);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].byPart[4] != v19->dbUnit.m_List[SHIDWORD(Size)].byPart[4] )
      {
        sprintf(&Source, "S_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].byPart[4]);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].byPart[5] != v19->dbUnit.m_List[SHIDWORD(Size)].byPart[5] )
      {
        sprintf(&Source, "B_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].byPart[5]);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].dwBullet[0] != v19->dbUnit.m_List[SHIDWORD(Size)].dwBullet[0] )
      {
        sprintf(&Source, "AB_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].dwBullet[0]);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].dwBullet[1] != v19->dbUnit.m_List[SHIDWORD(Size)].dwBullet[1] )
      {
        sprintf(&Source, "SB_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].dwBullet[1]);
        strcat_0(Dest, &Source);
      }
      for ( j = 0; (signed int)j < 8; ++j )
      {
        if ( v18->dbUnit.m_List[SHIDWORD(Size)].dwSpare[j] != v19->dbUnit.m_List[SHIDWORD(Size)].dwSpare[j] )
        {
          v10 = v18->dbUnit.m_List[SHIDWORD(Size)].dwSpare[j];
          sprintf(&Source, "Sp%d_%d=%d,", j, HIDWORD(Size));
          strcat_0(Dest, &Source);
        }
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].nPullingFee != v19->dbUnit.m_List[SHIDWORD(Size)].nPullingFee )
      {
        sprintf(&Source, "PF_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].nPullingFee);
        strcat_0(Dest, &Source);
      }
      if ( v18->dbUnit.m_List[SHIDWORD(Size)].dwCutTime != v19->dbUnit.m_List[SHIDWORD(Size)].dwCutTime )
      {
        sprintf(&Source, "Cut_%d=%d,", HIDWORD(Size), v18->dbUnit.m_List[SHIDWORD(Size)].dwCutTime);
        strcat_0(Dest, &Source);
      }
    }
  }
  v7 = strlen_0(Dest);
  if ( v7 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE Serial=%d", v17);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
