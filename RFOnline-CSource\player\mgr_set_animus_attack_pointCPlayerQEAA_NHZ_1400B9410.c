/*
 * Function: ?mgr_set_animus_attack_point@CPlayer@@QEAA_NH@Z
 * Address: 0x1400B9410
 */

char __fastcall CPlayer::mgr_set_animus_attack_point(CPlayer *this, int nPoint)
{
  char result; // al@2

  if ( this->m_pUserDB )
  {
    if ( this->m_nAnimusAttackPnt == nPoint )
    {
      result = 0;
    }
    else
    {
      this->m_nAnimusAttackPnt = nPoint;
      if ( this->m_pRecalledAnimusChar )
        this->m_pRecalledAnimusChar->m_nMaxAttackPnt = nPoint;
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
