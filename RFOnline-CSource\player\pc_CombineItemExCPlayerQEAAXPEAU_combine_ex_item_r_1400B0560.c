/*
 * Function: ?pc_CombineItemEx@CPlayer@@QEAAXPEAU_combine_ex_item_request_clzo@@@Z
 * Address: 0x1400B0560
 */

void __fastcall CPlayer::pc_CombineItemEx(CPlayer *this, _combine_ex_item_request_clzo *pRecv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-198h]@1
  char v5; // [sp+20h] [bp-178h]@4
  _combine_ex_item_result_zocl pSend; // [sp+40h] [bp-158h]@4
  CPlayer *v7; // [sp+1A0h] [bp+8h]@1
  _combine_ex_item_request_clzo *pRecva; // [sp+1A8h] [bp+10h]@1

  pRecva = pRecv;
  v7 = this;
  v2 = &v4;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  _combine_ex_item_result_zocl::_combine_ex_item_result_zocl(&pSend);
  v5 = ItemCombineMgr::RequestCombineProcess(&v7->m_ItemCombineMgr, pRecva, &pSend);
  CPlayer::SendMsg_CombineItemExResult(v7, &pSend);
}
