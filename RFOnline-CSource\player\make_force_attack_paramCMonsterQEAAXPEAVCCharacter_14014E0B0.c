/*
 * Function: ?make_force_attack_param@CMonster@@QEAAXPEAVCCharacter@@PEAVCMonsterSkill@@PEAU_attack_param@@@Z
 * Address: 0x14014E0B0
 */

void __fastcall CMonster::make_force_attack_param(CMonster *this, CCharacter *pDst, CMonsterSkill *pSkill, _attack_param *pAP)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  _base_fld *v7; // [sp+20h] [bp-18h]@10
  CMonster *v8; // [sp+40h] [bp+8h]@1
  CCharacter *v9; // [sp+48h] [bp+10h]@1
  CMonsterSkill *v10; // [sp+50h] [bp+18h]@1
  _attack_param *v11; // [sp+58h] [bp+20h]@1

  v11 = pAP;
  v10 = pSkill;
  v9 = pDst;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pSkill && CMonsterSkill::GetType(pSkill) == 3 )
  {
    v11->pDst = v9;
    if ( v9 )
      v11->nPart = CCharacter::GetAttackRandomPart(v9);
    else
      v11->nPart = CCharacter::GetAttackRandomPart((CCharacter *)&v8->vfptr);
    v11->nClass = v8->m_pMonRec->m_bAttRangeType;
    v11->nTol = CMonsterSkill::GetElement(v10);
    v11->nMinAF = CMonsterSkill::GetMinDmg(v10);
    v11->nMaxAF = CMonsterSkill::GetMaxDmg(v10);
    v11->nMinSel = CMonsterSkill::GetMinProb(v10);
    v11->nMaxSel = CMonsterSkill::GetMaxProb(v10);
    v11->pFld = CMonsterSkill::GetFld(v10);
    v11->byEffectCode = 1;
    v7 = v11->pFld;
    v11->nLevel = CMonsterSkill::GetSFLv(v10);
    v11->nMastery = 1;
    if ( v9 )
      memcpy_0(v11->fArea, v9->m_fCurPos, 0xCui64);
    else
      memcpy_0(v11->fArea, v8->m_fCurPos, 0xCui64);
    v11->nMaxAttackPnt = 0;
  }
}
