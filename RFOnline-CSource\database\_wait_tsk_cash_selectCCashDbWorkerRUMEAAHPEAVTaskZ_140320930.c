/*
 * Function: ?_wait_tsk_cash_select@CCashDbWorkerRU@@MEAAHPEAVTask@@@Z
 * Address: 0x140320930
 */

__int64 __fastcall CCashDbWorkerRU::_wait_tsk_cash_select(CCashDbWorkerRU *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  _param_cash_select *rParam; // [sp+20h] [bp-18h]@4
  CCashDbWorkerRU *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  rParam = (_param_cash_select *)Task::GetTaskBuf(pkTsk);
  return CRusiaBillingMgr::CallFunc_RFOnline_Auth(v7->_pkBill, rParam) != 0;
}
