#pragma once

namespace NexusPro {
namespace Modules {
    
    class AuthenticationModule {
    private:
        bool isInitialized_;
        bool hooksInstalled_;
        std::mutex moduleMutex_;
        
        // Statistics
        std::atomic<DWORD> loginAttempts_;
        std::atomic<DWORD> successfulLogins_;
        std::atomic<DWORD> failedLogins_;
        std::atomic<DWORD> blockedAttempts_;
        
        // Anti-exploit tracking
        struct ClientInfo {
            std::wstring ipAddress;
            DWORD loginAttempts;
            DWORD lastLoginTime;
            bool isBlocked;
            DWORD blockTime;
        };
        
        std::unordered_map<DWORD, ClientInfo> clientTracking_;
        std::mutex trackingMutex_;
        
    public:
        AuthenticationModule();
        ~AuthenticationModule();
        
        // Module lifecycle
        bool Initialize();
        void Shutdown();
        bool IsInitialized() const { return isInitialized_; }
        
        // Hook management
        bool InstallHooks();
        bool UninstallHooks();
        bool AreHooksInstalled() const { return hooksInstalled_; }
        
        // Authentication processing
        bool ProcessLogin(void* userDB, const std::wstring& username, const std::wstring& password);
        bool ValidateSession(HANDLE session, DWORD clientId);
        bool CheckAntiSpeedHack(DWORD clientId, DWORD currentTime);
        
        // Client management
        void RegisterClient(DWORD clientId, const std::wstring& ipAddress);
        void UnregisterClient(DWORD clientId);
        bool IsClientBlocked(DWORD clientId);
        void BlockClient(DWORD clientId, DWORD duration);
        void UnblockClient(DWORD clientId);
        
        // Statistics
        struct AuthStatistics {
            DWORD totalLoginAttempts;
            DWORD successfulLogins;
            DWORD failedLogins;
            DWORD blockedAttempts;
            DWORD activeClients;
            DWORD blockedClients;
            
            AuthStatistics() { memset(this, 0, sizeof(AuthStatistics)); }
        };
        
        AuthStatistics GetStatistics();
        void ResetStatistics();
        
        // Configuration
        void UpdateConfiguration();
        
        // Logging
        void LogLoginAttempt(const std::wstring& username, const std::wstring& ipAddress, bool success);
        void LogSecurityEvent(const std::wstring& event, DWORD clientId, const std::wstring& details);
        
    private:
        // Internal processing
        bool ValidateLoginData(void* userDB);
        bool CheckLoginLimits(DWORD clientId);
        bool DetectSpeedHack(DWORD clientId, DWORD currentTime);
        void UpdateClientTracking(DWORD clientId, bool loginSuccess);
        void CleanupExpiredBlocks();
        
        // Hook implementations (called by HookManager)
        friend class HookManager;
        static bool OnLogin(void* thisPtr, void* userDB);
        static bool OnCheckSession(void* thisPtr, HANDLE session);
        static void OnConnect(DWORD clientId, const std::wstring& ipAddress);
        static void OnDisconnect(DWORD clientId);
    };
    
}}  // namespace NexusPro::Modules
