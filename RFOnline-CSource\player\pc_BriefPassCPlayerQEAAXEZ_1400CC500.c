/*
 * Function: ?pc_BriefPass@CPlayer@@QEAAXE@Z
 * Address: 0x1400CC500
 */

void __fastcall CPlayer::pc_BriefPass(CPlayer *this, char byQuestSlotIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  _QUEST_DB_BASE *v5; // [sp+30h] [bp-18h]@4
  _base_fld *v6; // [sp+38h] [bp-10h]@5
  CPlayer *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = (_QUEST_DB_BASE *)((char *)&v7->m_Param.m_QuestDB + 13 * (unsigned __int8)byQuestSlotIndex);
  if ( v5->m_List[0].byQuestType != 255 )
  {
    v6 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, v5->m_List[0].wIndex);
    if ( v6 )
      CPlayer::Emb_CheckActForQuest(v7, 15, v6->m_strCode, 1u, 0);
  }
}
