/*
 * Function: ?make_gen_attack_param@CAnimus@@QEAAXPEAVCCharacter@@EPEAU_attack_param@@H@Z
 * Address: 0x14012AA70
 */

void __usercall CAnimus::make_gen_attack_param(CAnimus *this@<rcx>, CCharacter *pDst@<rdx>, char by<PERSON>art@<r8b>, _attack_param *pAP@<r9>, float a5@<xmm0>, int nSkillIndex)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  CAnimus *v9; // [sp+30h] [bp+8h]@1
  CCharacter *v10; // [sp+38h] [bp+10h]@1
  _attack_param *v11; // [sp+48h] [bp+20h]@1

  v11 = pAP;
  v10 = pDst;
  v9 = this;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  pAP->pDst = pDst;
  if ( !pDst || pDst->m_ObjID.m_byID || pDst[25].m_SFCont[0][5].m_wszPlayerName[16] == 255 )
    pAP->nPart = (unsigned __int8)byPart;
  else
    pAP->nPart = pDst[25].m_SFCont[0][5].m_wszPlayerName[16];
  pAP->nClass = v9->m_byRoleCode != 1;
  pAP->nTol = v9->m_Skill[nSkillIndex].m_Element;
  pAP->nMinAF = v9->m_Skill[nSkillIndex].m_MinDmg;
  pAP->nMaxAF = v9->m_Skill[nSkillIndex].m_MaxDmg;
  if ( v9->m_byRoleCode == 4 )
  {
    _effect_parameter::GetEff_Rate(&v9->m_pMaster->m_EP, 22);
    a5 = (float)v11->nMaxAF * a5;
    v11->nMaxAF = (signed int)ffloor(a5);
  }
  v11->nMinSel = v9->m_Skill[nSkillIndex].m_MinProb;
  v11->nMaxSel = v9->m_Skill[nSkillIndex].m_MaxProb;
  if ( v9->m_byRoleCode == 2 )
  {
    _effect_parameter::GetEff_Plus(&v9->m_pMaster->m_EP, 39);
    v11->nMaxSel = (signed int)ffloor((float)v11->nMaxSel - a5);
    v11->nAttactType = 6;
    v11->nExtentRange = 15;
    if ( v10 )
      memcpy_0(v11->fArea, v10->m_fCurPos, 0xCui64);
  }
  v11->nMaxAttackPnt = v9->m_nMaxAttackPnt;
}
