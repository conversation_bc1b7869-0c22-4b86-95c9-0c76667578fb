/*
 * Function: ?_db_Load_ItemCombineEx@CMainThread@@AEAAEKPEAU_ITEMCOMBINE_DB_BASE@@@Z
 * Address: 0x1401A90B0
 */

char __fastcall CMainThread::_db_Load_ItemCombineEx(CMainThread *this, unsigned int dwSerial, _ITEMCOMBINE_DB_BASE *pCombineEx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-188h]@1
  bool Dst; // [sp+30h] [bp-158h]@4
  unsigned int v8; // [sp+34h] [bp-154h]@13
  char v9; // [sp+38h] [bp-150h]@13
  unsigned int v10; // [sp+3Ch] [bp-14Ch]@13
  unsigned __int8 v11; // [sp+40h] [bp-148h]@13
  char v12; // [sp+41h] [bp-147h]@13
  int pl_nKey; // [sp+44h] [bp-144h]@16
  int v14; // [sp+48h] [bp-140h]@16
  int v15[70]; // [sp+4Ch] [bp-13Ch]@16
  unsigned int v16; // [sp+164h] [bp-24h]@19
  unsigned int v17; // [sp+168h] [bp-20h]@19
  char v18; // [sp+174h] [bp-14h]@4
  int j; // [sp+178h] [bp-10h]@13
  CMainThread *v20; // [sp+190h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+198h] [bp+10h]@1
  _ITEMCOMBINE_DB_BASE *v22; // [sp+1A0h] [bp+18h]@1

  v22 = pCombineEx;
  dwSeriala = dwSerial;
  v20 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset_0(&Dst, 0, 0x13Cui64);
  v18 = CRFWorldDatabase::Select_ItemCombineEx(v20->m_pWorldDB, dwSeriala, (_worlddb_itemcombineex_info *)&Dst);
  if ( v18 == 1 )
    return 24;
  if ( v18 != 2 )
    goto LABEL_22;
  if ( !CRFWorldDatabase::Insert_ItemCombineEx(v20->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_ItemCombineEx(v20->m_pWorldDB, dwSeriala, (_worlddb_itemcombineex_info *)&Dst) )
  {
    result = 24;
  }
  else
  {
LABEL_22:
    _ITEMCOMBINE_DB_BASE::Init(v22);
    v22->m_bIsResult = Dst;
    if ( v22->m_bIsResult )
    {
      v22->m_dwCheckKey = v8;
      v22->m_byDlgType = v9;
      v22->m_dwDalant = v10;
      v22->m_byItemListNum = v11;
      v22->m_bySelectItemCount = v12;
      for ( j = 0; j < 24; ++j )
      {
        if ( j >= v11 )
        {
          _COMBINEKEY::SetRelease(&v22->m_List[j].Key);
        }
        else
        {
          _COMBINEKEY::LoadDBKey(&v22->m_List[j].Key, *(&pl_nKey + 3 * j));
          v22->m_List[j].dwDur = *(&v14 + 3 * j);
          v22->m_List[j].dwUpt = v15[3 * j];
        }
      }
      v22->m_dwResultEffectType = v16;
      v22->m_dwResultEffectMsgCode = v17;
      result = 0;
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
