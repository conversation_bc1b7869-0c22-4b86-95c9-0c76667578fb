/*
 * Function: ?_GameDataBaseInit@CMainThread@@AEAA_NXZ
 * Address: 0x1401ED4E0
 */

char __fastcall CMainThread::_GameDataBaseInit(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CMoneySupplyMgr *v4; // rax@8
  CPvpUserAndGuildRankingSystem *v5; // rax@8
  CGuildBattleController *v6; // rax@10
  CTotalGuildRankManager *v7; // rax@12
  CWeeklyGuildRankManager *v8; // rax@14
  AutoMineMachineMng *v9; // rax@16
  CUnmannedTraderTaxRateManager *v10; // rax@20
  AutominePersonalMgr *v11; // rax@22
  CUnmannedTraderController *v12; // rax@24
  CGuildRoomSystem *v13; // rax@26
  CPostSystemManager *v14; // rax@28
  CItemStoreManager *v15; // rax@32
  CHonorGuild *v16; // rax@34
  CRaceBossWinRate *v17; // rax@36
  CCashDBWorkManager *v18; // rax@40
  CGoldenBoxItemMgr *v19; // rax@40
  qry_case_select_golden_box_item *v20; // rax@40
  CGoldenBoxItemMgr *v21; // rax@41
  CGoldenBoxItemMgr *v22; // rax@41
  CGoldenBoxItemMgr *v23; // rax@42
  __int64 v24; // [sp+0h] [bp-B58h]@1
  unsigned int v25; // [sp+40h] [bp-B18h]@4
  int nHisNum; // [sp+54h] [bp-B04h]@4
  char __t; // [sp+80h] [bp-AD8h]@4
  _economy_history_data pCurData; // [sp+A00h] [bp-158h]@4
  int pnCurMgrValue; // [sp+AE4h] [bp-74h]@4
  int pnNextMgrValue; // [sp+B04h] [bp-54h]@4
  int pnDBSerial; // [sp+B24h] [bp-34h]@40
  char v32; // [sp+B34h] [bp-24h]@40
  unsigned __int64 v33; // [sp+B40h] [bp-18h]@4
  CMainThread *v34; // [sp+B60h] [bp+8h]@1

  v34 = this;
  v1 = &v24;
  for ( i = 724i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v33 = (unsigned __int64)&v24 ^ _security_cookie;
  CLogFile::Write(&v34->m_logLoadingError, "Avators DBRestore Complete!!");
  v25 = eGetLocalDate();
  nHisNum = 0;
  `vector constructor iterator'(
    &__t,
    0xC8ui64,
    12,
    (void *(__cdecl *)(void *))_economy_history_data::_economy_history_data);
  _economy_history_data::_economy_history_data(&pCurData);
  pnCurMgrValue = 1000;
  pnNextMgrValue = 1000;
  if ( CMainThread::db_Select_Economy_History(
         v34,
         &pCurData,
         &pnCurMgrValue,
         &pnNextMgrValue,
         (_economy_history_data *)&__t,
         &nHisNum,
         v25) )
  {
    MyMessageBox("DatabaseInit", "Economy data load fail");
    result = 0;
  }
  else if ( eInitEconomySystem(pnCurMgrValue, pnNextMgrValue, (_economy_history_data *)&__t, nHisNum, &pCurData) )
  {
    v4 = CMoneySupplyMgr::Instance();
    CMoneySupplyMgr::Initialize(v4);
    CLogFile::Write(&v34->m_logLoadingError, "Economy History Load Complete!!");
    v5 = CPvpUserAndGuildRankingSystem::Instance();
    if ( CPvpUserAndGuildRankingSystem::Load(v5) )
    {
      v6 = CGuildBattleController::Instance();
      if ( CGuildBattleController::Load(v6) )
      {
        v7 = CTotalGuildRankManager::Instance();
        if ( CTotalGuildRankManager::Load(v7) )
        {
          v8 = CWeeklyGuildRankManager::Instance();
          if ( CWeeklyGuildRankManager::Load(v8) )
          {
            v9 = AutoMineMachineMng::Instance();
            if ( AutoMineMachineMng::Initialzie(v9) )
            {
              if ( CRFWorldDatabase::create_table_atrade_taxrate(v34->m_pWorldDB) )
              {
                v10 = CUnmannedTraderTaxRateManager::Instance();
                if ( CUnmannedTraderTaxRateManager::Load(v10) )
                {
                  v11 = AutominePersonalMgr::instance();
                  if ( !AutominePersonalMgr::CreateDBTable(v11) )
                  {
                    MyMessageBox("CMainThread::_GameDataBaseInit()", "AutominePersonalMgr::CreateDBTable Fail!");
                    CLogFile::Write(&v34->m_logLoadingError, "AutominePersonalMgr Create Database table Fail!");
                  }
                  v12 = CUnmannedTraderController::Instance();
                  if ( CUnmannedTraderController::Load(v12) )
                  {
                    v13 = CGuildRoomSystem::GetInstance();
                    if ( CGuildRoomSystem::Load_db(v13) )
                    {
                      v14 = CPostSystemManager::Instace();
                      if ( CPostSystemManager::Load(v14) )
                      {
                        if ( CMainThread::db_LoadGreetingMsg(v34) )
                        {
                          v15 = CItemStoreManager::Instance();
                          if ( CItemStoreManager::Load(v15) )
                          {
                            CMainThread::CreateSelectCharacterLogTable(v34, 1);
                            v16 = CHonorGuild::Instance();
                            if ( CHonorGuild::LoadDB(v16) )
                            {
                              v17 = CRaceBossWinRate::Instance();
                              if ( CRaceBossWinRate::LoadDB(v17) )
                              {
                                CMainThread::_db_Load_BattleTournamentInfo(v34);
                                if ( CMainThread::LoadLimitInfo(v34) )
                                {
                                  v18 = CTSingleton<CCashDBWorkManager>::Instance();
                                  CCashDBWorkManager::Start(v18);
                                  pnDBSerial = 0;
                                  v19 = CGoldenBoxItemMgr::Instance();
                                  v20 = (qry_case_select_golden_box_item *)CGoldenBoxItemMgr::GetGodBoxItemInfoPtr(v19);
                                  v32 = CMainThread::_db_Load_GoldBoxItem(v34, v20, &pnDBSerial);
                                  if ( v32 )
                                  {
                                    MyMessageBox("CMainThread::_GameDataBaseInit()", "_db_Load_GoldBoxItem() Fail!");
                                    CLogFile::Write(&v34->m_logLoadingError, "_db_Load_GoldBoxItem Fail!");
                                    result = 0;
                                  }
                                  else
                                  {
                                    v21 = CGoldenBoxItemMgr::Instance();
                                    CGoldenBoxItemMgr::SetDBSerial(v21, pnDBSerial);
                                    v22 = CGoldenBoxItemMgr::Instance();
                                    if ( CGoldenBoxItemMgr::Get_Event_Status(v22) == 2 )
                                    {
                                      v23 = CGoldenBoxItemMgr::Instance();
                                      CGoldenBoxItemMgr::SynchINIANDDB(v23);
                                    }
                                    result = 1;
                                  }
                                }
                                else
                                {
                                  result = 0;
                                }
                              }
                              else
                              {
                                MyMessageBox(
                                  "CMainThread::_GameDataBaseInit()",
                                  "CRaceBossWinRate::Instance()->LoadDB() Fail!");
                                CLogFile::Write(&v34->m_logLoadingError, "CRaceBossWinRate::Instance()->LoadDB() Fail!");
                                result = 0;
                              }
                            }
                            else
                            {
                              MyMessageBox(
                                "CMainThread::_GameDataBaseInit()",
                                "CHonorGuild::Instance()->LoadDB() Fail!");
                              CLogFile::Write(&v34->m_logLoadingError, "CHonorGuild::Instance()->LoadDB() Fail!");
                              result = 0;
                            }
                          }
                          else
                          {
                            MyMessageBox(
                              "CMainThread::_GameDataBaseInit()",
                              "CItemStoreManager::Instance()->Load() Fail!");
                            CLogFile::Write(&v34->m_logLoadingError, "CItemStoreManager::Instance()->Load() Fail!");
                            result = 0;
                          }
                        }
                        else
                        {
                          MyMessageBox("CMainThread::_GameDataBaseInit()", "db_LoadGreetingMsg() Fail!");
                          CLogFile::Write(&v34->m_logLoadingError, "db_LoadGreetingMsg() Fail!");
                          result = 0;
                        }
                      }
                      else
                      {
                        MyMessageBox("CMainThread::_GameDataBaseInit()", "CPostSystemManager::Instace()->Load() Fail!");
                        CLogFile::Write(&v34->m_logLoadingError, "CPostSystemManager::Instace()->Load() Fail!");
                        result = 0;
                      }
                    }
                    else
                    {
                      MyMessageBox("CMainThread::Init() : ", "CGuildRoomSystem::GetInstance()->Load_db()");
                      CLogFile::Write(&v34->m_logLoadingError, "CGuildRoomSystem::GetInstance()->Load_db()");
                      result = 0;
                    }
                  }
                  else
                  {
                    MyMessageBox(
                      "CMainThread::_GameDataBaseInit()",
                      "CUnmannedTraderController::Instance()->Load() Fail!");
                    CLogFile::Write(&v34->m_logLoadingError, "CUnmannedTraderController::Instance()->Load() Fail!");
                    result = 0;
                  }
                }
                else
                {
                  MyMessageBox("CMainThread::_GameDataBaseInit()", "ATradeTaxRateTable() Fail!");
                  CLogFile::Write(&v34->m_logLoadingError, "ATradeTaxRateTable() Fail!");
                  result = 0;
                }
              }
              else
              {
                result = 0;
              }
            }
            else
            {
              MyMessageBox("CMainThread::_GameDataBaseInit()", "InitAutoMineMachines() Fail!");
              CLogFile::Write(&v34->m_logLoadingError, "InitAutoMineMachines() Fail!");
              result = 0;
            }
          }
          else
          {
            MyMessageBox("CMainThread::_GameDataBaseInit()", "CWeeklyGuildRankManager::Instance()->Load() Fail!");
            CLogFile::Write(&v34->m_logLoadingError, "CWeeklyGuildRankManager::Instance()->Load() Fail!");
            result = 0;
          }
        }
        else
        {
          MyMessageBox("CMainThread::_GameDataBaseInit()", "CTotalGuildRankManager::Instance()->Load() Fail!");
          CLogFile::Write(&v34->m_logLoadingError, "CGuildBattleController::Instance()->Load() Fail!");
          result = 0;
        }
      }
      else
      {
        MyMessageBox("CMainThread::_GameDataBaseInit()", "CGuildBattleController::Instance()->Load() Fail!");
        CLogFile::Write(&v34->m_logLoadingError, "CGuildBattleController::Instance()->Load() Fail!");
        result = 0;
      }
    }
    else
    {
      MyMessageBox("CMainThread::_GameDataBaseInit()", "CPvpUserRankingAndGuildRankingSystem::Instance()->Load() Fail!");
      CLogFile::Write(&v34->m_logLoadingError, "CPvpUserRankingAndGuildRankingSystem::Instance()->Load() Fail!");
      result = 0;
    }
  }
  else
  {
    MyMessageBox("DatabaseInit", "Economy data init fail");
    result = 0;
  }
  return result;
}
