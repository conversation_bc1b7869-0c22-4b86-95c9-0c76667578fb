/*
 * Function: ?_db_Load_Start_NpcQuest_History@CMainThread@@AEAAEKEPEAU_QUEST_DB_BASE@@@Z
 * Address: 0x1401A82C0
 */

char __fastcall CMainThread::_db_Load_Start_NpcQuest_History(CMainThread *this, unsigned int dwSerial, char byRaceCode, _QUEST_DB_BASE *pCon)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@12
  __int64 v7; // [sp+0h] [bp-A8h]@1
  unsigned int pdwCount; // [sp+28h] [bp-80h]@13
  const char *v9; // [sp+30h] [bp-78h]@10
  char v10; // [sp+44h] [bp-64h]@13
  unsigned int j; // [sp+48h] [bp-60h]@20
  int __n[2]; // [sp+50h] [bp-58h]@4
  void *v13; // [sp+58h] [bp-50h]@7
  void *__t; // [sp+60h] [bp-48h]@4
  int v15[2]; // [sp+68h] [bp-40h]@7
  void *v16; // [sp+70h] [bp-38h]@10
  void *v17; // [sp+78h] [bp-30h]@7
  __int64 v18; // [sp+80h] [bp-28h]@4
  void *v19; // [sp+88h] [bp-20h]@5
  void *v20; // [sp+90h] [bp-18h]@8
  CMainThread *v21; // [sp+B0h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+B8h] [bp+10h]@1
  char v23; // [sp+C0h] [bp+18h]@1
  _QUEST_DB_BASE *v24; // [sp+C8h] [bp+20h]@1

  v24 = pCon;
  v23 = byRaceCode;
  dwSeriala = dwSerial;
  v21 = this;
  v4 = &v7;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v18 = -2i64;
  *(_QWORD *)__n = v21->m_dwStartNPCQuestCnt[(unsigned __int8)byRaceCode];
  __t = operator new[](saturated_mul(0x59ui64, *(unsigned __int64 *)__n));
  if ( __t )
  {
    `vector constructor iterator'(
      __t,
      0x59ui64,
      __n[0],
      (void *(__cdecl *)(void *))_QUEST_DB_BASE::_START_NPC_QUEST_HISTORY::_START_NPC_QUEST_HISTORY);
    v19 = __t;
  }
  else
  {
    v19 = 0i64;
  }
  v13 = v19;
  v24->m_StartHistory = (_QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *)v19;
  *(_QWORD *)v15 = v21->m_dwStartNPCQuestCnt[(unsigned __int8)v23];
  v17 = operator new[](saturated_mul(0x50ui64, *(unsigned __int64 *)v15));
  if ( v17 )
  {
    `vector constructor iterator'(
      v17,
      0x50ui64,
      v15[0],
      (void *(__cdecl *)(void *))_worlddb_start_npc_quest_complete_history::__list::__list);
    v20 = v17;
  }
  else
  {
    v20 = 0i64;
  }
  v16 = v20;
  v9 = (const char *)v20;
  if ( v24->m_StartHistory && v9 )
  {
    v10 = CRFWorldDatabase::Select_Start_NpcQuest_History_Count(v21->m_pWorldDB, dwSeriala, &pdwCount);
    if ( v10 == 1 )
    {
      result = 24;
    }
    else
    {
      if ( v10 == 2 )
      {
        pdwCount = 0;
        v24->dwListCnt = 0;
      }
      v24->dwListCnt = pdwCount;
      v10 = CRFWorldDatabase::Select_Start_NpcQuest_History(
              v21->m_pWorldDB,
              dwSeriala,
              (_worlddb_start_npc_quest_complete_history *)&pdwCount,
              pdwCount);
      if ( v10 == 1 )
      {
        result = 24;
      }
      else
      {
        if ( v10 != 2 )
        {
          for ( j = 0; j < pdwCount; ++j )
          {
            strcpy_0(v24->m_StartHistory[j].szQuestCode, &v9[80 * j]);
            v24->m_StartHistory[j].byLevel = v9[80 * j + 64];
            v24->m_StartHistory[j].nEndTime = *(_QWORD *)&v9[80 * j + 72];
          }
        }
        result = 0;
      }
    }
  }
  else
  {
    result = 24;
  }
  return result;
}
