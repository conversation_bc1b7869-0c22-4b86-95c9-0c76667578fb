/*
 * Function: ?pc_AddBag@CPlayer@@QEAAXG@Z
 * Address: 0x1400B4340
 */

void __fastcall CPlayer::pc_AddBag(CPlayer *this, unsigned __int16 wBagItemSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@18
  char v5; // al@18
  char v6; // al@19
  char v7; // al@21
  __int64 v8; // [sp+0h] [bp-68h]@1
  bool bDelete; // [sp+20h] [bp-48h]@18
  char *strErrorCodePos; // [sp+28h] [bp-40h]@18
  char v11; // [sp+30h] [bp-38h]@4
  _STORAGE_LIST *v12; // [sp+38h] [bp-30h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+40h] [bp-28h]@4
  __int64 v14; // [sp+48h] [bp-20h]@4
  int v15; // [sp+50h] [bp-18h]@18
  CPlayer *v16; // [sp+70h] [bp+8h]@1
  unsigned __int16 v17; // [sp+78h] [bp+10h]@1

  v17 = wBagItemSerial;
  v16 = this;
  v2 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = 0;
  v12 = v16->m_Param.m_pStoragePtr[0];
  pItem = 0i64;
  v14 = 0i64;
  if ( v16->m_pUserDB )
  {
    if ( _effect_parameter::GetEff_State(&v16->m_EP, 20) )
    {
      v11 = 3;
    }
    else if ( _effect_parameter::GetEff_State(&v16->m_EP, 28) )
    {
      v11 = 3;
    }
    else
    {
      pItem = _STORAGE_LIST::GetPtrFromSerial(v12, v17);
      if ( pItem )
      {
        if ( pItem->m_byTableCode == 12 )
        {
          if ( pItem->m_bLock )
          {
            v11 = 11;
          }
          else if ( (signed int)(unsigned __int8)CPlayerDB::GetBagNum(&v16->m_Param) >= 5 )
          {
            v11 = 2;
          }
        }
        else
        {
          v11 = 1;
        }
      }
      else
      {
        v11 = 1;
      }
    }
    if ( !v11 )
    {
      v4 = CPlayerDB::GetBagNum(&v16->m_Param);
      v15 = (unsigned __int8)v4;
      CPlayerDB::SetBagNum(&v16->m_Param, v4 + 1);
      v5 = CPlayerDB::GetBagNum(&v16->m_Param);
      _STORAGE_LIST::SetUseListNum(v12, 20 * (unsigned __int8)v5);
      strErrorCodePos = "CPlayer::pc_AddBag()";
      bDelete = 1;
      if ( !CPlayer::Emb_DelStorage(v16, v12->m_nListCode, pItem->m_byStorageIndex, 0, 1, "CPlayer::pc_AddBag()") )
      {
        CPlayerDB::SetBagNum(&v16->m_Param, v15);
        v6 = CPlayerDB::GetBagNum(&v16->m_Param);
        _STORAGE_LIST::SetUseListNum(v12, 20 * (unsigned __int8)v6);
        CPlayer::SendMsg_AddBagResult(v16, -1);
        return;
      }
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v16->m_ObjID.m_wIndex,
        pItem,
        v16->m_szItemHistoryFileName);
      if ( v16->m_pUserDB )
      {
        v7 = CPlayerDB::GetBagNum(&v16->m_Param);
        CUserDB::Update_BagNum(v16->m_pUserDB, v7);
      }
    }
    CPlayer::SendMsg_AddBagResult(v16, v11);
  }
}
