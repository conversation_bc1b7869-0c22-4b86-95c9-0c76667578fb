/*
 * Function: ?AddDefaultDBTable@CGuildBattleScheduleManager@GUILD_BATTLE@@AEAA_NXZ
 * Address: 0x1403DD320
 */

char __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::AddDefaultDBTable(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@5
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  _DWORD byUnitTimeCntPerTime[6]; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleScheduleManager *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  LOBYTE(byUnitTimeCntPerTime[0]) = 1;
  if ( CRFWorldDatabase::InsertGuildBattleScheduleDefaultRecord(pkDB, 2u, v7->m_uiMapCnt, 23, 1) )
  {
    result = 1;
  }
  else
  {
    v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    byUnitTimeCntPerTime[0] = 30;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v3,
      "CGuildBattleScheduleManager::AddDefaultDBTable() :  g_Main.m_pWorldDB->InsertGuildBattleScheduleDefaultRecord( 2 D"
      "AY, %u, %u, %u ) Fail!",
      v7->m_uiMapCnt,
      23i64);
    result = 0;
  }
  return result;
}
