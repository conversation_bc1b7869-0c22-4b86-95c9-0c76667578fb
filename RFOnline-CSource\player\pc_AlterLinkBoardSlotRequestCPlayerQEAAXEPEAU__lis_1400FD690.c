/*
 * Function: ?pc_AlterLinkBoardSlotRequest@CPlayer@@QEAAXEPEAU__list@_alter_link_slot_request_clzo@@E@Z
 * Address: 0x1400FD690
 */

void __fastcall CPlayer::pc_AlterLinkBoardSlotRequest(CPlayer *this, char byNum, _alter_link_slot_request_clzo::__list *pList, char byLBLock)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@5
  char *v8; // [sp+28h] [bp-40h]@7
  unsigned __int8 v9; // [sp+30h] [bp-38h]@7
  unsigned __int16 v10; // [sp+34h] [bp-34h]@7
  char pbyStorageCode; // [sp+44h] [bp-24h]@8
  _STORAGE_LIST::_db_con *v12; // [sp+58h] [bp-10h]@8
  CPlayer *v13; // [sp+70h] [bp+8h]@1
  char v14; // [sp+78h] [bp+10h]@1
  _alter_link_slot_request_clzo::__list *v15; // [sp+80h] [bp+18h]@1
  char v16; // [sp+88h] [bp+20h]@1

  v16 = byLBLock;
  v15 = pList;
  v14 = byNum;
  v13 = this;
  v4 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v13->m_pUserDB )
  {
    for ( j = 0; j < (unsigned __int8)v14; ++j )
    {
      v8 = &v15[j].bySlotIndex;
      v9 = v15[j].byLinkCode;
      v10 = v15[j].wIndex;
      if ( v9 == 4 )
      {
        v12 = CPlayerDB::GetPtrItemStorage(&v13->m_Param, v10, &pbyStorageCode);
        if ( v12 )
        {
          v10 = 0;
          v10 = v12->m_byStorageIndex | (unsigned __int16)((unsigned __int8)pbyStorageCode << 8);
          CPlayerDB::PushLink(&v13->m_Param, (unsigned __int8)*v8, v12->m_wSerial, 0);
        }
        else
        {
          v9 = -1;
          v10 = -1;
        }
      }
      else if ( v9 == 255 )
      {
        CPlayerDB::PopLink(&v13->m_Param, (unsigned __int8)*v8);
      }
      CUserDB::Update_LinkBoardSlot(v13->m_pUserDB, *v8, v9, v10);
    }
    CUserDB::Update_LinkBoardLock(v13->m_pUserDB, v16);
  }
}
