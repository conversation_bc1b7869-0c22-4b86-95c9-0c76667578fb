/*
 * Function: ?Clear@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403D4280
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Clear(GUILD_BATTLE::CNormalGuildBattleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleManager *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  GUILD_BATTLE::CNormalGuildBattleManager::Clear(v4, v4->m_ppkTodayBattle);
  GUILD_BATTLE::CNormalGuildBattleManager::Clear(v4, v4->m_ppkTomorrowBattle);
}
