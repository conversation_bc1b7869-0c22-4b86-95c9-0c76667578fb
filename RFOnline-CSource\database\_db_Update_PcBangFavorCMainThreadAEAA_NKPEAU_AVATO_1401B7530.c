/*
 * Function: ?_db_Update_PcBangFavor@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEADH@Z
 * Address: 0x1401B7530
 */

char __fastcall CMainThread::_db_Update_PcBangFavor(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szPcBangFavorQuery, int nSize)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  size_t v8; // rax@9
  __int64 v10; // [sp+0h] [bp-D8h]@1
  char Dest; // [sp+30h] [bp-A8h]@4
  char v12; // [sp+31h] [bp-A7h]@4
  int v13; // [sp+B4h] [bp-24h]@4
  unsigned int j; // [sp+B8h] [bp-20h]@4
  unsigned __int64 v15; // [sp+C8h] [bp-10h]@4
  unsigned int v16; // [sp+E8h] [bp+10h]@1
  _AVATOR_DATA *v17; // [sp+F0h] [bp+18h]@1
  _AVATOR_DATA *v18; // [sp+F8h] [bp+20h]@1

  v18 = pOldData;
  v17 = pNewData;
  v16 = dwSerial;
  v6 = &v10;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v15 = (unsigned __int64)&v10 ^ _security_cookie;
  Dest = 0;
  memset(&v12, 0, 0x7Fui64);
  sprintf_s(szPcBangFavorQuery, nSize, "UPDATE tbl_pcbangitem Set ");
  v13 = strlen_0(szPcBangFavorQuery);
  for ( j = 0; (signed int)j < 50; ++j )
  {
    if ( v17->dbPcBangFavorItem.lnUID[j] != v18->dbPcBangFavorItem.lnUID[j] )
    {
      sprintf(&Dest, "K%d=%I64d,", j, v17->dbPcBangFavorItem.lnUID[j]);
      strcat_s(szPcBangFavorQuery, nSize, &Dest);
    }
  }
  v8 = strlen_0(szPcBangFavorQuery);
  if ( v8 <= v13 )
  {
    memset_0(szPcBangFavorQuery, 0, v13);
  }
  else
  {
    sprintf_s(&Dest, 0x80ui64, "WHERE Serial = %d", v16);
    szPcBangFavorQuery[strlen_0(szPcBangFavorQuery) - 1] = 32;
    strcat_s(szPcBangFavorQuery, nSize, &Dest);
  }
  return 1;
}
