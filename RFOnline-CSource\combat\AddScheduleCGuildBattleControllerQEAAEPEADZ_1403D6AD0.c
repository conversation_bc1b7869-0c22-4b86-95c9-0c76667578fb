/*
 * Function: ?AddSchedule@CGuildBattleController@@QEAAEPEAD@Z
 * Address: 0x1403D6AD0
 */

char __fastcall CGuildBattleController::AddSchedule(CGuildBattleController *this, char *szData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned int dwMapID[2]; // [sp+20h] [bp-28h]@4
  unsigned __int16 byNumber[4]; // [sp+28h] [bp-20h]@4
  char *v8; // [sp+30h] [bp-18h]@4

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = szData;
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 0);
  LOBYTE(byNumber[0]) = v8[16];
  dwMapID[0] = *((_DWORD *)v8 + 3);
  if ( CRFWorldDatabase::UpdateGuildBattleInfo(
         pkDB,
         *(_DWORD *)v8,
         *((_DWORD *)v8 + 1),
         *((_DWORD *)v8 + 2),
         dwMapID[0],
         byNumber[0]) )
  {
    byNumber[0] = *((_WORD *)v8 + 20);
    *(_QWORD *)dwMapID = *((_QWORD *)v8 + 4);
    if ( CRFWorldDatabase::UpdateGuildBattleScheduleInfo(
           pkDB,
           *(_DWORD *)v8,
           *((_DWORD *)v8 + 5),
           v8[24],
           *(__int64 *)dwMapID,
           byNumber[0]) )
    {
      CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&pkDB->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 1);
      result = 0;
    }
    else
    {
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&pkDB->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 1);
      result = 24;
    }
  }
  else
  {
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&pkDB->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 1);
    result = 24;
  }
  return result;
}
