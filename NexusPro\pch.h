#pragma once

// Windows Headers
#include <windows.h>
#include <winternl.h>
#include <psapi.h>
#include <tlhelp32.h>

// Standard Library
#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <regex>

// Project Common Headers
#include "Utils/Common.h"
#include "Utils/Logger.h"
#include "Utils/RFTypes.h"

// Microsoft Detours (when available)
#ifdef DETOURS_AVAILABLE
#include <detours.h>
#pragma comment(lib, "detours.lib")
#endif

// Project Defines
#define NEXUSPRO_VERSION L"1.0.0"
#define NEXUSPRO_BUILD L"001"

// Export Macros
#ifdef NEXUSPRO_EXPORTS
#define NEXUSPRO_API __declspec(dllexport)
#else
#define NEXUSPRO_API __declspec(dllimport)
#endif

// Debug Macros
#ifdef _DEBUG
#define NEXUS_DEBUG(msg) Logger::Debug(msg)
#define NEXUS_TRACE(msg) Logger::Trace(msg)
#else
#define NEXUS_DEBUG(msg)
#define NEXUS_TRACE(msg)
#endif

#define NEXUS_INFO(msg) Logger::Info(msg)
#define NEXUS_WARN(msg) Logger::Warning(msg)
#define NEXUS_ERROR(msg) Logger::Error(msg)

// Common Types
using QWORD = unsigned long long;
using DWORD64 = unsigned long long;

// Function Pointer Types for RF Online (Updated with actual signatures)
namespace RFTypes {
    // Authentication function types (based on actual RF Online code)
    typedef void(__fastcall* LoginFunc)(void* thisPtr, void* userDB);
    typedef bool(__fastcall* CheckSessionFunc)(void* thisPtr, int session);

    // Network function types (based on actual RF Online code)
    typedef int(__fastcall* SendMsgFunc)(void* thisPtr, unsigned int dwClientIndex, unsigned __int16 wType, char* szMsg, unsigned __int16 nLen);
    typedef void(__fastcall* RecvMsgFunc)(void* thisPtr, unsigned __int16 wSocketIndex);

    // Monster function types (based on actual RF Online code)
    typedef char(__fastcall* CreateMonsterFunc)(void* thisPtr, void* createData);
    typedef void(__fastcall* MonsterLoopFunc)(void* thisPtr);

    // General function types
    typedef BOOL(__stdcall* GenericBoolFunc)();
    typedef void(__stdcall* GenericVoidFunc)();
}

// Forward Declarations
namespace NexusPro {
    class HookManager;
    class MemoryPatcher;
    class ConfigManager;
    class Logger;
    class AddressResolver;
}
