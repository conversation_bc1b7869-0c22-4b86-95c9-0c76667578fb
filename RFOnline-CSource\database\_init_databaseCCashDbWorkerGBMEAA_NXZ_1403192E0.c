/*
 * Function: ?_init_database@CCashDbWorkerGB@@MEAA_NXZ
 * Address: 0x1403192E0
 */

char __fastcall CCashDbWorkerGB::_init_database(CCashDbWorkerGB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  char result; // al@8
  CNationSettingManager *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-48h]@1
  CEnglandBillingMgr *v7; // [sp+20h] [bp-28h]@7
  CEnglandBillingMgr *v8; // [sp+28h] [bp-20h]@4
  __int64 v9; // [sp+30h] [bp-18h]@4
  CEnglandBillingMgr *v10; // [sp+38h] [bp-10h]@5
  CCashDbWorkerGB *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  v8 = (CEnglandBillingMgr *)operator new(0xC8ui64);
  if ( v8 )
  {
    CEnglandBillingMgr::CEnglandBillingMgr(v8);
    v10 = (CEnglandBillingMgr *)v3;
  }
  else
  {
    v10 = 0i64;
  }
  v7 = v10;
  v11->_pkNet = v10;
  if ( v11->_pkNet )
  {
    CEnglandBillingMgr::MakeConnectionThread(v11->_pkNet);
    v5 = CTSingleton<CNationSettingManager>::Instance();
    CNationSettingManager::SetCashDBInitState(v5);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
