/*
 * Function: ?_db_Load_TimeLimitInfo@CMainThread@@AEAAEKPEAU_TIMELIMITINFO_DB_BASE@@@Z
 * Address: 0x1401B7C60
 */

char __fastcall CMainThread::_db_Load_TimeLimitInfo(CMainThread *this, unsigned int dwAccSerial, _TIMELIMITINFO_DB_BASE *pDbTimeLimitInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-58h]@1
  _worlddb_time_limit_info pTimeLimiInfo; // [sp+28h] [bp-30h]@4
  char v8; // [sp+44h] [bp-14h]@4
  CMainThread *v9; // [sp+60h] [bp+8h]@1
  unsigned int dwAccountSerial; // [sp+68h] [bp+10h]@1
  _TIMELIMITINFO_DB_BASE *v11; // [sp+70h] [bp+18h]@1

  v11 = pDbTimeLimitInfo;
  dwAccountSerial = dwAccSerial;
  v9 = this;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = CRFWorldDatabase::Select_PlayerTimeLimitInfo(v9->m_pWorldDB, dwAccSerial, &pTimeLimiInfo);
  if ( v8 == 1 )
    return 24;
  if ( v8 != 2 )
    goto LABEL_14;
  if ( !CRFWorldDatabase::Insert_PlayerTimeLimitInfo(v9->m_pWorldDB, dwAccountSerial) )
    return 24;
  if ( CRFWorldDatabase::Select_PlayerTimeLimitInfo(v9->m_pWorldDB, dwAccountSerial, &pTimeLimiInfo) )
  {
    result = 24;
  }
  else
  {
LABEL_14:
    v11->dwAccSerial = dwAccountSerial;
    v11->dwFatigue = pTimeLimiInfo.dwFatigue;
    v11->byTLStatus = pTimeLimiInfo.byTLStatus;
    v11->dwLastLogoutTime = pTimeLimiInfo.dwLastLogoutTime;
    result = 0;
  }
  return result;
}
