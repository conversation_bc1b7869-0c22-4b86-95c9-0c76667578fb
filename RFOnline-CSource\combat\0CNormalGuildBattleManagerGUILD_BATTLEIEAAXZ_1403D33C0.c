/*
 * Function: ??0CNormalGuildBattleManager@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403D33C0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::CNormalGuildBattleManager(GUILD_BATTLE::CNormalGuildBattleManager *this)
{
  this->m_bLoad = 0;
  this->m_bDone = 0;
  this->m_uiMapCnt = 0;
  this->m_uiMaxBattleCnt = 0;
  this->m_ppkNormalBattle = 0i64;
  this->m_ppkTodayBattle = 0i64;
  this->m_ppkTomorrowBattle = 0i64;
}
