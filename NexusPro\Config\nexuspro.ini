; NexusPro Configuration File
; RF Online Zone Server Enhancement Framework
; Version 1.0.0

[Core]
; Enable logging system
Core.EnableLogging = true

; Log level: 0=Trace, 1=Debug, 2=Info, 3=Warning, 4=Error, 5=Critical
Core.LogLevel = 2

; Enable console output for debugging
Core.EnableConsoleOutput = false

; Enable file output
Core.EnableFileOutput = true

; Hook installation timeout in milliseconds
Core.HookTimeout = 5000

; Enable debug mode (more verbose logging)
Core.EnableDebugMode = false

[Authentication]
; Enable authentication hooks
Authentication.EnableAuthHooks = true

; Log all login attempts
Authentication.EnableLoginLogging = true

; Validate session integrity
Authentication.EnableSessionValidation = true

; Enable anti-speed hack detection
Authentication.EnableAntiSpeedHack = true

; Maximum login attempts before temporary ban
Authentication.MaxLoginAttempts = 5

; Session timeout in milliseconds
Authentication.SessionTimeout = 300000

[Network]
; Enable network packet hooks
Network.EnableNetworkHooks = true

; Log network packets (warning: generates large logs)
Network.EnablePacketLogging = false

; Validate packet integrity
Network.EnablePacketValidation = true

; Enable anti-flood protection
Network.EnableAntiFlood = true

; Maximum packets per second per client
Network.MaxPacketsPerSecond = 100

; Packet buffer size
Network.PacketBufferSize = 8192

[BugFix]
; Enable bug fix module
BugFix.EnableBugFixes = true

; Fix monster level limit issues
BugFix.EnableMonsterLimitFix = true

; Fix item duplication exploits
BugFix.EnableItemDupeFix = true

; Fix memory leaks
BugFix.EnableMemoryLeakFix = true

; Fix crash-related bugs
BugFix.EnableCrashFix = true

[Enhancement]
; Enable enhancement module
Enhancement.EnableEnhancements = true

; Improve monster AI behavior
Enhancement.EnableImprovedAI = true

; Enhance guild battle system
Enhancement.EnableBetterGuildSystem = true

; Enhance PvP mechanics
Enhancement.EnableEnhancedPvP = true

; Enable custom events (experimental)
Enhancement.EnableCustomEvents = false

; AI update interval in milliseconds
Enhancement.AIUpdateInterval = 1000

[Monitoring]
; Enable monitoring system
Monitoring.EnableMonitoring = true

; Monitor server performance
Monitoring.EnablePerformanceMonitoring = true

; Monitor resource usage
Monitoring.EnableResourceMonitoring = true

; Monitor player activities
Monitoring.EnablePlayerMonitoring = true

; Monitoring check interval in milliseconds
Monitoring.MonitoringInterval = 30000

; Enable alert system
Monitoring.EnableAlerts = true
