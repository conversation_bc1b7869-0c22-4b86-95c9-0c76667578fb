/*
 * Function: ??0CNormalGuildBattleFieldList@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403EE250
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::CNormalGuildBattleFieldList(GUILD_BATTLE::CNormalGuildBattleFieldList *this)
{
  this->m_dwCnt = 0;
  this->m_pkField = 0i64;
  this->m_byUseFieldCnt[0] = 0;
  this->m_byUseFieldCnt[1] = 0;
  this->m_byUseFieldCnt[2] = 0;
  this->m_ppkUseFieldByRace[0] = 0i64;
  this->m_ppkUseFieldByRace[1] = 0i64;
  this->m_ppkUseFieldByRace[2] = 0i64;
}
