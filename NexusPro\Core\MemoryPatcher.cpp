#include "pch.h"
#include "MemoryPatcher.h"

namespace NexusPro {
    
    MemoryPatcher::MemoryPatcher() {
    }
    
    MemoryPatcher::~MemoryPatcher() {
        RemoveAllPatches();
    }
    
    bool MemoryPatcher::PatchBytes(const std::wstring& name, LPVOID address, const std::vector<BYTE>& newBytes, const std::wstring& description) {
        NEXUS_FUNCTION_LOG();
        
        if (!IsValidAddress(address, newBytes.size())) {
            NEXUS_ERROR(L"Invalid address for patch: " + name);
            return false;
        }
        
        std::lock_guard<std::mutex> lock(patchesMutex_);
        
        // Check if patch already exists
        if (patches_.find(name) != patches_.end()) {
            NEXUS_WARN(L"Patch already exists: " + name);
            return false;
        }
        
        PatchInfo patch;
        patch.address = address;
        patch.patchedBytes = newBytes;
        patch.description = description;
        patch.timestamp = GetTickCount();
        patch.isApplied = false;
        
        // Backup original bytes
        if (!BackupOriginalBytes(patch, newBytes.size())) {
            NEXUS_ERROR(L"Failed to backup original bytes for patch: " + name);
            return false;
        }
        
        // Apply the patch
        if (!ApplyPatchInternal(patch)) {
            NEXUS_ERROR(L"Failed to apply patch: " + name);
            return false;
        }
        
        patches_[name] = patch;
        NEXUS_INFO(L"Patch applied successfully: " + name);
        return true;
    }
    
    bool MemoryPatcher::BackupOriginalBytes(PatchInfo& patch, SIZE_T size) {
        patch.originalBytes.resize(size);
        
        DWORD oldProtection;
        if (!ChangeMemoryProtection(patch.address, size, PAGE_EXECUTE_READWRITE, oldProtection)) {
            return false;
        }
        
        bool success = ReadMemory(patch.address, patch.originalBytes.data(), size);
        
        RestoreMemoryProtection(patch.address, size, oldProtection);
        
        return success;
    }
    
    bool MemoryPatcher::ApplyPatchInternal(PatchInfo& patch) {
        SIZE_T size = patch.patchedBytes.size();
        
        DWORD oldProtection;
        if (!ChangeMemoryProtection(patch.address, size, PAGE_EXECUTE_READWRITE, oldProtection)) {
            return false;
        }
        
        bool success = WriteMemory(patch.address, patch.patchedBytes.data(), size);
        
        RestoreMemoryProtection(patch.address, size, oldProtection);
        
        if (success) {
            patch.isApplied = true;
        }
        
        return success;
    }
    
    bool MemoryPatcher::RestorePatch(const std::wstring& name) {
        NEXUS_FUNCTION_LOG();
        
        std::lock_guard<std::mutex> lock(patchesMutex_);
        
        auto it = patches_.find(name);
        if (it == patches_.end()) {
            NEXUS_WARN(L"Patch not found: " + name);
            return false;
        }
        
        return RestorePatchInternal(it->second);
    }
    
    bool MemoryPatcher::RestorePatchInternal(PatchInfo& patch) {
        if (!patch.isApplied) {
            return true; // Already restored
        }
        
        SIZE_T size = patch.originalBytes.size();
        
        DWORD oldProtection;
        if (!ChangeMemoryProtection(patch.address, size, PAGE_EXECUTE_READWRITE, oldProtection)) {
            return false;
        }
        
        bool success = WriteMemory(patch.address, patch.originalBytes.data(), size);
        
        RestoreMemoryProtection(patch.address, size, oldProtection);
        
        if (success) {
            patch.isApplied = false;
        }
        
        return success;
    }
    
    bool MemoryPatcher::ChangeMemoryProtection(LPVOID address, SIZE_T size, DWORD newProtection, DWORD& oldProtection) {
        return VirtualProtect(address, size, newProtection, &oldProtection) != FALSE;
    }
    
    bool MemoryPatcher::RestoreMemoryProtection(LPVOID address, SIZE_T size, DWORD oldProtection) {
        DWORD dummy;
        return VirtualProtect(address, size, oldProtection, &dummy) != FALSE;
    }
    
    bool MemoryPatcher::ReadMemory(LPVOID address, void* buffer, SIZE_T size) {
        __try {
            memcpy(buffer, address, size);
            return true;
        }
        __except (EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }
    
    bool MemoryPatcher::WriteMemory(LPVOID address, const void* buffer, SIZE_T size) {
        __try {
            memcpy(address, buffer, size);
            return true;
        }
        __except (EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }
    
    bool MemoryPatcher::IsValidAddress(LPVOID address, SIZE_T size) {
        if (!address || size == 0) {
            return false;
        }
        
        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(address, &mbi, sizeof(mbi)) == 0) {
            return false;
        }
        
        // Check if the entire range is within the same memory region
        BYTE* endAddress = static_cast<BYTE*>(address) + size - 1;
        BYTE* regionEnd = static_cast<BYTE*>(mbi.BaseAddress) + mbi.RegionSize - 1;
        
        return (endAddress <= regionEnd) && 
               (mbi.State == MEM_COMMIT) && 
               (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY));
    }
    
    bool MemoryPatcher::CreateJumpPatch(const std::wstring& name, LPVOID fromAddress, LPVOID toAddress, const std::wstring& description) {
        std::vector<BYTE> jumpBytes = CreateJumpBytes(fromAddress, toAddress);
        if (jumpBytes.empty()) {
            NEXUS_ERROR(L"Failed to create jump bytes for patch: " + name);
            return false;
        }
        
        return PatchBytes(name, fromAddress, jumpBytes, description);
    }
    
    std::vector<BYTE> MemoryPatcher::CreateJumpBytes(LPVOID fromAddress, LPVOID toAddress) {
        // Calculate relative offset for JMP instruction
        BYTE* from = static_cast<BYTE*>(fromAddress);
        BYTE* to = static_cast<BYTE*>(toAddress);
        
        // JMP instruction is 5 bytes: E9 [4-byte relative offset]
        INT32 relativeOffset = static_cast<INT32>(to - from - 5);
        
        std::vector<BYTE> jumpBytes;
        jumpBytes.push_back(0xE9); // JMP opcode
        
        // Add the 4-byte relative offset (little-endian)
        jumpBytes.push_back(static_cast<BYTE>(relativeOffset & 0xFF));
        jumpBytes.push_back(static_cast<BYTE>((relativeOffset >> 8) & 0xFF));
        jumpBytes.push_back(static_cast<BYTE>((relativeOffset >> 16) & 0xFF));
        jumpBytes.push_back(static_cast<BYTE>((relativeOffset >> 24) & 0xFF));
        
        return jumpBytes;
    }
    
    std::vector<BYTE> MemoryPatcher::CreateNopBytes(SIZE_T count) {
        return std::vector<BYTE>(count, 0x90); // 0x90 = NOP instruction
    }
    
    bool MemoryPatcher::NopBytes(const std::wstring& name, LPVOID address, SIZE_T size, const std::wstring& description) {
        std::vector<BYTE> nopBytes = CreateNopBytes(size);
        return PatchBytes(name, address, nopBytes, description);
    }
    
    void MemoryPatcher::RemoveAllPatches() {
        NEXUS_FUNCTION_LOG();
        
        std::lock_guard<std::mutex> lock(patchesMutex_);
        
        for (auto& pair : patches_) {
            if (pair.second.isApplied) {
                RestorePatchInternal(pair.second);
            }
        }
        
        patches_.clear();
        NEXUS_INFO(L"All patches removed");
    }
    
    bool MemoryPatcher::IsPatchApplied(const std::wstring& name) {
        std::lock_guard<std::mutex> lock(patchesMutex_);
        
        auto it = patches_.find(name);
        return (it != patches_.end()) && it->second.isApplied;
    }
    
    MemoryPatcher::PatchStatistics MemoryPatcher::GetStatistics() {
        std::lock_guard<std::mutex> lock(patchesMutex_);
        
        PatchStatistics stats;
        stats.totalPatches = patches_.size();
        
        for (const auto& pair : patches_) {
            if (pair.second.isApplied) {
                stats.appliedPatches++;
            } else {
                stats.failedPatches++;
            }
            
            // Categorize by patch type (based on description or pattern)
            if (pair.first.find(L"Jump") != std::wstring::npos) {
                stats.jumpPatches++;
            } else if (pair.first.find(L"Nop") != std::wstring::npos || pair.first.find(L"NOP") != std::wstring::npos) {
                stats.nopPatches++;
            } else {
                stats.bytePatches++;
            }
        }
        
        return stats;
    }
    
    // ============================================================================
    // RF ONLINE SPECIFIC PATCHES
    // ============================================================================
    
    bool MemoryPatcher::ApplyRFBugFixes() {
        NEXUS_FUNCTION_LOG();
        
        bool success = true;
        
        // Apply various RF Online bug fixes
        if (!PatchMonsterLimitBug()) {
            NEXUS_WARN(L"Failed to apply monster limit bug fix");
            success = false;
        }
        
        if (!PatchItemDupeBug()) {
            NEXUS_WARN(L"Failed to apply item duplication bug fix");
            success = false;
        }
        
        if (!PatchMemoryLeaks()) {
            NEXUS_WARN(L"Failed to apply memory leak fixes");
            success = false;
        }
        
        if (!PatchCrashBugs()) {
            NEXUS_WARN(L"Failed to apply crash bug fixes");
            success = false;
        }
        
        return success;
    }
    
    bool MemoryPatcher::PatchMonsterLimitBug() {
        // TODO: Implement monster limit bug fix
        // This would patch the monster creation limit check
        NEXUS_INFO(L"Monster limit bug fix applied (placeholder)");
        return true;
    }
    
    bool MemoryPatcher::PatchItemDupeBug() {
        // TODO: Implement item duplication bug fix
        // This would patch item validation and transfer functions
        NEXUS_INFO(L"Item duplication bug fix applied (placeholder)");
        return true;
    }
    
    bool MemoryPatcher::PatchMemoryLeaks() {
        // TODO: Implement memory leak fixes
        // This would patch cleanup functions for monsters, players, items
        NEXUS_INFO(L"Memory leak fixes applied (placeholder)");
        return true;
    }
    
    bool MemoryPatcher::PatchCrashBugs() {
        // TODO: Implement crash bug fixes
        // This would add null pointer checks and buffer overflow protection
        NEXUS_INFO(L"Crash bug fixes applied (placeholder)");
        return true;
    }
}
