/*
 * Function: ?pc_EquipPart@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@@Z
 * Address: 0x1400AD960
 */

void __fastcall CPlayer::pc_EquipPart(CPlayer *this, _STORAGE_POS_INDIV *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-118h]@1
  bool bDelete; // [sp+20h] [bp-F8h]@22
  char *strErrorCodePos; // [sp+28h] [bp-F0h]@22
  char v7; // [sp+30h] [bp-E8h]@4
  _STORAGE_LIST *v8; // [sp+38h] [bp-E0h]@4
  _STORAGE_LIST::_db_con *pFixingItem; // [sp+40h] [bp-D8h]@4
  void *Src; // [sp+48h] [bp-D0h]@4
  __int64 v11; // [sp+50h] [bp-C8h]@4
  _base_fld *v12; // [sp+58h] [bp-C0h]@4
  char v13; // [sp+60h] [bp-B8h]@17
  _STORAGE_LIST::_db_con Dst; // [sp+78h] [bp-A0h]@22
  _STORAGE_LIST::_db_con v15; // [sp+C8h] [bp-50h]@25
  CPlayer *v16; // [sp+120h] [bp+8h]@1
  _STORAGE_POS_INDIV *v17; // [sp+128h] [bp+10h]@1

  v17 = pItem;
  v16 = this;
  v2 = &v4;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = 0;
  v8 = (_STORAGE_LIST *)&v16->m_Param.m_dbInven.m_nListNum;
  pFixingItem = 0i64;
  Src = 0i64;
  v11 = 0i64;
  v12 = 0i64;
  if ( _effect_parameter::GetEff_State(&v16->m_EP, 20) )
  {
    v7 = 8;
  }
  else if ( _effect_parameter::GetEff_State(&v16->m_EP, 28) )
  {
    v7 = 8;
  }
  else
  {
    pFixingItem = _STORAGE_LIST::GetPtrFromSerial(v8, v17->wItemSerial);
    if ( pFixingItem )
    {
      v12 = CRecordData::GetRecord(
              (CRecordData *)&unk_1799C6AA0 + pFixingItem->m_byTableCode,
              pFixingItem->m_wItemIndex);
      if ( pFixingItem->m_byTableCode < 8 )
      {
        if ( pFixingItem->m_bLock )
        {
          v7 = 10;
        }
        else
        {
          Src = &v16->m_Param.m_dbEquip.m_pStorageList[pFixingItem->m_byTableCode];
          if ( *(_BYTE *)Src && *((_BYTE *)Src + 19) )
          {
            v7 = 10;
          }
          else
          {
            v13 = GetItemEquipGrade(pFixingItem->m_byTableCode, pFixingItem->m_wItemIndex);
            if ( CPlayer::IsEquipAbleGrade(v16, v13) )
            {
              if ( !CPlayer::_check_equip_part(v16, pFixingItem) )
                v7 = 7;
            }
            else
            {
              v7 = 9;
            }
          }
        }
      }
      else
      {
        v7 = 3;
      }
    }
    else
    {
      v7 = 2;
    }
  }
  if ( v7 )
    goto LABEL_34;
  _STORAGE_LIST::_db_con::_db_con(&Dst);
  memcpy_0(&Dst, pFixingItem, 0x32ui64);
  strErrorCodePos = "CPlayer::pc_EquipPart() -- 0";
  bDelete = 0;
  if ( !CPlayer::Emb_DelStorage(
          v16,
          v8->m_nListCode,
          pFixingItem->m_byStorageIndex,
          0,
          0,
          "CPlayer::pc_EquipPart() -- 0") )
  {
    CPlayer::SendMsg_EquipPartResult(v16, -1);
    return;
  }
  if ( *(_BYTE *)Src )
  {
    _STORAGE_LIST::_db_con::_db_con(&v15);
    memcpy_0(&v15, Src, 0x32ui64);
    bDelete = 0;
    if ( !CPlayer::Emb_AddStorage(v16, v8->m_nListCode, (_STORAGE_LIST::_storage_con *)&v15.m_bLoad, 1, 0) )
    {
      bDelete = 0;
      CPlayer::Emb_AddStorage(v16, v8->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
      CPlayer::SendMsg_EquipPartResult(v16, -1);
      return;
    }
    strErrorCodePos = "CPlayer::pc_EquipPart() -- 1";
    bDelete = 0;
    if ( !CPlayer::Emb_DelStorage(v16, 1, *((_BYTE *)Src + 49), 1, 0, "CPlayer::pc_EquipPart() -- 1") )
    {
      strErrorCodePos = 0i64;
      bDelete = 0;
      CPlayer::Emb_DelStorage(v16, v8->m_nListCode, *((_BYTE *)Src + 49), 1, 0, 0i64);
      bDelete = 0;
      CPlayer::Emb_AddStorage(v16, v8->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
      CPlayer::SendMsg_EquipPartResult(v16, -1);
      return;
    }
  }
  if ( CPlayer::Emb_AddStorage(v16, 1, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0) )
  {
    CPlayer::Emb_EquipLink(v16);
LABEL_34:
    CPlayer::SendMsg_EquipPartResult(v16, v7);
    return;
  }
  if ( *(_BYTE *)Src )
  {
    strErrorCodePos = 0i64;
    bDelete = 0;
    CPlayer::Emb_DelStorage(v16, v8->m_nListCode, *((_BYTE *)Src + 49), 1, 0, 0i64);
    bDelete = 0;
    CPlayer::Emb_AddStorage(v16, v8->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
    CPlayer::SendMsg_EquipPartResult(v16, -1);
  }
  else
  {
    bDelete = 0;
    CPlayer::Emb_AddStorage(v16, v8->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
    CPlayer::SendMsg_EquipPartResult(v16, -1);
  }
}
