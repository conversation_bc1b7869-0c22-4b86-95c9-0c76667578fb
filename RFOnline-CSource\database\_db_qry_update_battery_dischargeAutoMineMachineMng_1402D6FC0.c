/*
 * Function: ?_db_qry_update_battery_discharge@AutoMineMachineMng@@AEAAEPEAD@Z
 * Address: 0x1402D6FC0
 */

char __fastcall AutoMineMachineMng::_db_qry_update_battery_discharge(AutoMineMachineMng *this, char *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned int dwBattery; // [sp+20h] [bp-28h]@4
  char *v7; // [sp+30h] [bp-18h]@4

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pdata;
  dwBattery = *(_DWORD *)(pdata + 7);
  if ( CRFWorldDatabase::update_amine_battery(pkDB, pdata[2], pdata[1], *(_DWORD *)(pdata + 3), dwBattery) )
    result = 0;
  else
    result = 24;
  return result;
}
