/*
 * Function: ??_ECUserDB@@UEAAPEAXI@Z
 * Address: 0x140203230
 */

CUserDB *__fastcall CUserDB::`vector deleting destructor'(CUserDB *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUserDB *result; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUserDB *ptr; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  ptr = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( a2 & 2 )
  {
    `eh vector destructor iterator'(
      ptr,
      0x13870ui64,
      *(_DWORD *)&ptr[-1].m_szLobbyHistoryFileName[60],
      (void (__cdecl *)(void *))CUserDB::~CUserDB);
    if ( v7 & 1 )
      operator delete[](&ptr[-1].m_szLobbyHistoryFileName[60]);
    result = (CUserDB *)((char *)ptr - 8);
  }
  else
  {
    CUserDB::~CUserDB(ptr);
    if ( v7 & 1 )
      operator delete(ptr);
    result = ptr;
  }
  return result;
}
