/*
 * Function: ?_wait_tsk_cash_update@CCashDbWorkerGB@@IEAAHPEAVTask@@H@Z
 * Address: 0x140319200
 */

__int64 __fastcall CCashDbWorkerGB::_wait_tsk_cash_update(CCashDbWorkerGB *this, Task *pkTsk, int nIdx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  _param_cash_update *rParam; // [sp+20h] [bp-28h]@4
  unsigned int v8; // [sp+28h] [bp-20h]@4
  int v9; // [sp+2Ch] [bp-1Ch]@4
  int v10; // [sp+30h] [bp-18h]@4
  int n; // [sp+34h] [bp-14h]@4
  char *v12; // [sp+38h] [bp-10h]@6
  CCashDbWorkerGB *v13; // [sp+50h] [bp+8h]@1
  int nIdxa; // [sp+60h] [bp+18h]@1

  nIdxa = nIdx;
  v13 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  rParam = (_param_cash_update *)Task::GetTaskBuf(pkTsk);
  v8 = 0;
  v9 = -1;
  v10 = 0;
  for ( n = 0; n < rParam->in_nNum10; ++n )
  {
    v12 = &rParam->in_item[(signed __int64)n].byRet;
    if ( CEnglandBillingMgr::CallFunc_Item_Buy(v13->_pkNet, rParam, n, nIdxa) )
      return 1;
    v12[40] = v8;
  }
  return v8;
}
