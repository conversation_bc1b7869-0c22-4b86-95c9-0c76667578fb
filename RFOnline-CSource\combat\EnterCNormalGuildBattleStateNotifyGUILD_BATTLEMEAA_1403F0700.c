/*
 * Function: ?Enter@CNormalGuildBattleStateNotify@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F0700
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateNotify::Enter(GUILD_BATTLE::CNormalGuildBattleStateNotify *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateNotify *v6; // [sp+30h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *pkBattlea; // [sp+38h] [bp+10h]@1

  pkBattlea = pkBattle;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  GUILD_BATTLE::CNormalGuildBattle::CreateLogFile(pkBattle);
  GUILD_BATTLE::CNormalGuildBattle::DecideColorInx(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattle::NotifyBeforeStart(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleState::Log(
    (GUILD_BATTLE::CNormalGuildBattleState *)&v6->vfptr,
    pkBattlea,
    "Enter : Notify 10 Minutes Before Start");
  return 0i64;
}
