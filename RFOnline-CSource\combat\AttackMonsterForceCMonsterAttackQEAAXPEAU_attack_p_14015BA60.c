/*
 * Function: ?AttackMonsterForce@CMonsterAttack@@QEAAXPEAU_attack_param@@@Z
 * Address: 0x14015BA60
 */

void __usercall CMonsterAttack::AttackMonsterForce(CMonsterAttack *this@<rcx>, _attack_param *pParam@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed int v5; // eax@7
  int v6; // eax@19
  float v7; // xmm0_4@19
  int v8; // xmm0_4@19
  __int64 v9; // rcx@25
  float *v10; // rax@26
  __int64 v11; // rdx@26
  _attack_param *v12; // rax@28
  CCharacter **v13; // rcx@28
  _attack_param *v14; // rdx@28
  _attack_param *v15; // r8@28
  __int64 v16; // [sp+0h] [bp-88h]@1
  int nEffAttPower[2]; // [sp+20h] [bp-68h]@25
  bool bUseEffBullet; // [sp+28h] [bp-60h]@25
  _base_fld *v19; // [sp+30h] [bp-58h]@4
  char v20; // [sp+38h] [bp-50h]@4
  int v21; // [sp+3Ch] [bp-4Ch]@7
  float v22; // [sp+40h] [bp-48h]@19
  int v23; // [sp+44h] [bp-44h]@19
  float v24; // [sp+48h] [bp-40h]@7
  int v25; // [sp+4Ch] [bp-3Ch]@8
  int v26; // [sp+50h] [bp-38h]@11
  int v27; // [sp+54h] [bp-34h]@19
  __int64 v28; // [sp+58h] [bp-30h]@25
  int *v29; // [sp+60h] [bp-28h]@25
  int *v30; // [sp+68h] [bp-20h]@26
  int nAttPnt; // [sp+70h] [bp-18h]@28
  CMonsterAttack *v32; // [sp+90h] [bp+8h]@1

  v32 = this;
  v3 = &v16;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v32->m_nDamagedObjNum = 0;
  v32->m_bIsCrtAtt = 0;
  v32->m_pp = pParam;
  v19 = v32->m_pp->pFld;
  v20 = 1;
  CCharacter::BreakStealth(v32->m_pAttChar);
  if ( v32->m_pp->pDst )
  {
    if ( _effect_parameter::GetEff_State(&v32->m_pp->pDst->m_EP, 8) )
    {
      v20 = 0;
    }
    else
    {
      _effect_parameter::GetEff_Plus(&v32->m_pAttChar->m_EP, 31);
      v24 = a3 + 100.0;
      v5 = ((int (__fastcall *)(CCharacter *))v32->m_pp->pDst->vfptr->GetAvoidRate)(v32->m_pp->pDst);
      v21 = (signed int)ffloor(v24 - (float)v5);
      if ( v21 <= 0 )
        v25 = 0;
      else
        v25 = v21;
      v21 = v25;
      if ( v25 >= 100 )
        v26 = 100;
      else
        v26 = v21;
      v21 = v26;
      if ( rand() % 100 >= v21 )
        v20 = 0;
    }
  }
  if ( v20 )
  {
    v6 = CAttack::_CalcForceAttPnt((CAttack *)&v32->m_pp, 0);
    v7 = (float)v6;
    v22 = (float)v6;
    MonsterSetInfoData::GetMonsterForcePowerRate(&g_MonsterSetInfoData);
    *(float *)&v8 = v22 * v7;
    v22 = *(float *)&v8;
    _effect_parameter::GetEff_Rate(&v32->m_pAttChar->m_EP, 4);
    v23 = v8;
    CMonsterAttack::ModifyMonsterAttFc(v32, *(float *)&v8);
    v22 = v22 * *(float *)&v8;
    v27 = *(_DWORD *)&v19[11].m_strCode[4];
    if ( v27 < 0 )
      return;
    if ( v27 <= 2 )
    {
      if ( v32->m_pp->pDst )
      {
        v32->m_DamList[0].m_pChar = v32->m_pp->pDst;
        v12 = v32->m_pp;
        v13 = &v32->m_pp->pDst;
        v14 = v32->m_pp;
        v15 = v32->m_pp;
        nAttPnt = (signed int)ffloor(v22);
        bUseEffBullet = v12->bBackAttack;
        *(_QWORD *)nEffAttPower = *v13;
        v32->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                        v32->m_pAttChar,
                                        nAttPnt,
                                        v15->nPart,
                                        v14->nTol,
                                        *(CCharacter **)nEffAttPower,
                                        bUseEffBullet);
        v32->m_nDamagedObjNum = 1;
      }
      goto LABEL_31;
    }
    if ( v27 != 4 )
    {
      if ( v27 == 5 )
      {
        v9 = *(_DWORD *)&v19[4].m_strCode[60];
        v28 = *(_DWORD *)&v19[4].m_strCode[60];
        v29 = s_Mon_nLimitDist;
        bUseEffBullet = 0;
        nEffAttPower[0] = 0;
        CAttack::FlashDamageProc(
          (CAttack *)&v32->m_pp,
          s_Mon_nLimitDist[v28],
          (signed int)ffloor(v22),
          s_Mon_nLimitAngle[1][v9],
          0,
          0);
LABEL_31:
        CAttack::CalcAvgDamage((CAttack *)&v32->m_pp);
        return;
      }
      if ( v27 != 6 )
        return;
    }
    v10 = v32->m_pp->fArea;
    v11 = *(_DWORD *)&v19[4].m_strCode[60];
    v30 = s_Mon_nLimitRadius;
    bUseEffBullet = 0;
    nEffAttPower[0] = 0;
    CAttack::AreaDamageProc((CAttack *)&v32->m_pp, s_Mon_nLimitRadius[v11], (signed int)ffloor(v22), v10, 0, 0);
    goto LABEL_31;
  }
  if ( v32->m_pp->pDst )
  {
    v32->m_DamList[0].m_pChar = v32->m_pp->pDst;
    v32->m_DamList[0].m_nDamage = 0;
    v32->m_nDamagedObjNum = 1;
  }
}
