/*
 * Function: ?mgr_destroy_system_tower@CPlayer@@QEAA_NXZ
 * Address: 0x1400BEC30
 */

char __fastcall CPlayer::mgr_destroy_system_tower(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  _sec_info *v4; // rax@9
  float v5; // xmm0_4@16
  __int64 v7; // [sp+0h] [bp-208h]@1
  _pnt_rect pRect; // [sp+28h] [bp-1E0h]@4
  int j; // [sp+44h] [bp-1C4h]@4
  int k; // [sp+48h] [bp-1C0h]@6
  unsigned int dwSecIndex; // [sp+4Ch] [bp-1BCh]@9
  CObjectList *v12; // [sp+50h] [bp-1B8h]@9
  _object_list_point *v13; // [sp+58h] [bp-1B0h]@10
  CGuardTower *v14; // [sp+60h] [bp-1A8h]@12
  char *v15; // [sp+68h] [bp-1A0h]@12
  CGuardTower *v16; // [sp+70h] [bp-198h]@15
  char wszTran; // [sp+90h] [bp-178h]@18
  LPCSTR lpAppName; // [sp+128h] [bp-E0h]@19
  const char *v19; // [sp+130h] [bp-D8h]@19
  const char *v20; // [sp+138h] [bp-D0h]@19
  char Dest; // [sp+160h] [bp-A8h]@19
  unsigned __int64 v22; // [sp+1F0h] [bp-18h]@4
  CPlayer *v23; // [sp+210h] [bp+8h]@1

  v23 = this;
  v1 = &v7;
  for ( i = 128i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v22 = (unsigned __int64)&v7 ^ _security_cookie;
  v3 = CGameObject::GetCurSecNum((CGameObject *)&v23->vfptr);
  CMapData::GetRectInRadius(v23->m_pCurMap, &pRect, 1, v3);
  for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
    {
      v4 = CMapData::GetSecInfo(v23->m_pCurMap);
      dwSecIndex = v4->m_nSecNumW * j + k;
      v12 = CMapData::GetSectorListObj(v23->m_pCurMap, v23->m_wMapLayerIndex, dwSecIndex);
      if ( v12 )
      {
        v13 = v12->m_Head.m_pNext;
        while ( v13 != &v12->m_Tail )
        {
          v14 = (CGuardTower *)v13->m_pItem;
          v13 = v13->m_pNext;
          v15 = &v14->m_ObjID.m_byKind;
          if ( !v14->m_ObjID.m_byKind && v15[1] == 4 )
          {
            v16 = v14;
            if ( v14->m_bSystemStruct )
            {
              v5 = v14->m_fCurPos[1] - v23->m_fCurPos[1];
              abs(v5);
              if ( v5 <= 100.0 )
              {
                GetSqrt(v14->m_fCurPos, v23->m_fCurPos);
                if ( v5 < 20.0 )
                {
                  M2W(v16->m_pRecordSet->m_strCode, &wszTran, 0x80u);
                  if ( v16->m_nIniIndex != -1 )
                  {
                    lpAppName = "BELLATO";
                    v19 = "CORA";
                    v20 = "ACCRETIA";
                    sprintf(&Dest, "Map%d", v16->m_nIniIndex);
                    WritePrivateProfileStringA(
                      (&lpAppName)[8 * v16->m_byRaceCode],
                      &Dest,
                      "NULL",
                      ".\\Script\\SystemGuardTower.ini");
                  }
                  CGuardTower::Destroy(v16, 0, 1);
                  return 1;
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}
