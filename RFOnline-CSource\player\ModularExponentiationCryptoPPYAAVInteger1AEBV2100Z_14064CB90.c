/*
 * Function: ?ModularExponentiation@CryptoPP@@YA?AVInteger@1@AEBV21@00@Z
 * Address: 0x14064CB90
 */

struct CryptoPP::Integer *__fastcall CryptoPP::ModularExponentiation(CryptoPP *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, const struct CryptoPP::Integer *a5)
{
  struct CryptoPP::Integer *v6; // [sp+20h] [bp-18h]@0
  CryptoPP *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  LODWORD(v6) = 0;
  CryptoPP::a_exp_b_mod_c(this, retstr, a3, a4, v6);
  return (struct CryptoPP::Integer *)v7;
}
