/*
 * Function: ?pc_CastVoteRequest@CPlayer@@QEAAXHE@Z
 * Address: 0x140108590
 */

void __fastcall CPlayer::pc_CastVoteRequest(CPlayer *this, int nVoteSerial, char byCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@10
  int v6; // eax@13
  __int64 v7; // [sp+0h] [bp-38h]@1
  char v8; // [sp+20h] [bp-18h]@4
  int v9; // [sp+24h] [bp-14h]@10
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  int nSerial; // [sp+48h] [bp+10h]@1
  char v12; // [sp+50h] [bp+18h]@1

  v12 = byCode;
  nSerial = nVoteSerial;
  v10 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = 0;
  if ( LOBYTE(g_VoteSys[760 * CPlayerDB::GetRaceCode(&v10->m_Param)]) )
  {
    if ( *(_DWORD *)&g_VoteSys[760 * CPlayerDB::GetRaceCode(&v10->m_Param) + 2] == nSerial )
    {
      if ( v10->m_nVoteSerial == nSerial )
      {
        v8 = 5;
      }
      else
      {
        v9 = v10->m_Param.m_byPvPGrade;
        v5 = CPlayerDB::GetRaceCode(&v10->m_Param);
        if ( v9 < SLOBYTE(g_VoteSys[760 * v5 + 96]) )
          v8 = 6;
      }
    }
    else
    {
      v8 = 4;
    }
  }
  else
  {
    v8 = 3;
  }
  if ( !v8 )
  {
    CPlayer::SetVote(v10, nSerial);
    v6 = CPlayerDB::GetRaceCode(&v10->m_Param);
    CVoteSystem::ActVote((CVoteSystem *)&g_VoteSys[760 * v6], v10->m_dwObjSerial, v12);
  }
  CPlayer::SendMsg_CastVoteResult(v10, v8);
}
