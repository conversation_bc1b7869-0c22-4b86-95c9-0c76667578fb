/*
 * Function: ?pc_AnimusRecallRequest@CPlayer@@QEAAXGGG@Z
 * Address: 0x1400D02E0
 */

void __fastcall CPlayer::pc_AnimusRecallRequest(CPlayer *this, unsigned __int16 wAnimusItemSerial, unsigned __int16 wAnimusClientHP, unsigned __int16 wAnimusClientFP)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@67
  long double v7; // xmm0_8@70
  int v8; // eax@78
  unsigned __int16 v9; // ax@80
  __int64 v10; // [sp+0h] [bp-E8h]@1
  char v11; // [sp+20h] [bp-C8h]@4
  _STORAGE_LIST::_storage_con *v12; // [sp+28h] [bp-C0h]@4
  _animus_fld *v13; // [sp+30h] [bp-B8h]@4
  unsigned int *v14; // [sp+38h] [bp-B0h]@4
  CAnimus *pNewAnimus; // [sp+40h] [bp-A8h]@4
  unsigned __int16 v16; // [sp+48h] [bp-A0h]@53
  unsigned __int16 v17; // [sp+4Ch] [bp-9Ch]@53
  int j; // [sp+50h] [bp-98h]@16
  _base_fld *v19; // [sp+58h] [bp-90h]@4
  _STORAGE_LIST::_storage_con *v20; // [sp+60h] [bp-88h]@19
  char v21; // [sp+68h] [bp-80h]@29
  __int64 v22; // [sp+70h] [bp-78h]@38
  unsigned int v23; // [sp+78h] [bp-70h]@48
  int v24; // [sp+7Ch] [bp-6Ch]@63
  _animus_create_setdata Dst; // [sp+90h] [bp-58h]@79
  CPlayer *v26; // [sp+F0h] [bp+8h]@1
  unsigned __int16 v27; // [sp+F8h] [bp+10h]@1
  unsigned __int16 v28; // [sp+100h] [bp+18h]@1
  unsigned __int16 v29; // [sp+108h] [bp+20h]@1

  v29 = wAnimusClientFP;
  v28 = wAnimusClientHP;
  v27 = wAnimusItemSerial;
  v26 = this;
  v4 = &v10;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = 0;
  v12 = 0i64;
  v13 = 0i64;
  v14 = 0i64;
  pNewAnimus = 0i64;
  v19 = 0i64;
  if ( v26->m_bObserver )
  {
    v11 = 13;
  }
  else if ( v26->m_bInGuildBattle && v26->m_bTakeGravityStone )
  {
    v11 = 14;
  }
  else if ( CPlayerDB::GetRaceCode(&v26->m_Param) == 1 )
  {
    if ( CGameObject::GetCurSecNum((CGameObject *)&v26->vfptr) == -1 || v26->m_bMapLoading )
    {
      v11 = 6;
    }
    else if ( v26->m_pRecalledAnimusItem )
    {
      v11 = 1;
    }
    else
    {
      for ( j = 0; j < 4; ++j )
      {
        v20 = (_STORAGE_LIST::_storage_con *)&v26->m_Param.m_dbAnimus.m_pStorageList[j].m_bLoad;
        if ( v20->m_bLoad && v20->m_wSerial == v27 )
        {
          v12 = v20;
          break;
        }
      }
      if ( v12 )
      {
        if ( v12->m_bLock )
        {
          v11 = 13;
        }
        else
        {
          v19 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 24, v12->m_wItemIndex);
          if ( v19 )
          {
            if ( v26->m_bFreeSFByClass )
              goto LABEL_83;
            v21 = 0;
            if ( *(_DWORD *)&v19[3].m_strCode[60] == 1 )
            {
              if ( v26->m_Param.m_pClassHistory[0]
                && v26->m_Param.m_pClassHistory[0]->m_nClass == 3
                && v26->m_Param.m_pClassData->m_nClass == 3
                && v26->m_Param.m_pClassData->m_bAnimusUsable )
              {
                v21 = 1;
              }
            }
            else
            {
              for ( j = 0; j < 4; ++j )
              {
                v22 = (__int64)*v26->m_Param.m_ppHistoryEffect[j];
                if ( !v22 )
                  break;
                if ( *(_DWORD *)(v22 + 1440) )
                {
                  v21 = 1;
                  break;
                }
              }
            }
            if ( v21 )
            {
LABEL_83:
              if ( v26->m_bFreeRecallWaitTime )
              {
                v26->m_bFreeRecallWaitTime = 0;
              }
              else if ( v26->m_wTimeFreeRecallSerial != v12->m_wSerial )
              {
                v23 = GetLoopTime() - v26->m_dwLastRecallTime;
                if ( v23 < 0x7530 )
                {
                  v11 = 9;
                  goto $RESULT_57;
                }
              }
              v13 = GetAnimusFldFromExp(v12->m_wItemIndex, v12->m_dwDur);
              if ( !v13 )
                return;
              v14 = &v12->m_dwLv;
              v16 = v12->m_dwLv;
              v17 = HIWORD(v12->m_dwLv);
              if ( v26->m_wTimeFreeRecallSerial == v12->m_wSerial )
                goto LABEL_84;
              if ( abs_0(*(_WORD *)v14 - v28) <= 4 )
                v16 = v28;
              if ( abs_0(*((_WORD *)v14 + 1) - v29) <= 4 )
                v17 = v29;
              if ( (float)((float)v16 / (float)v13->m_nMaxHP) >= 0.30000001 )
              {
LABEL_84:
                if ( v26->m_wTimeFreeRecallSerial != v12->m_wSerial )
                {
                  v24 = _MASTERY_PARAM::GetMasteryPerMast(&v26->m_pmMst, 6, 0);
                  if ( v13->m_nLevel < 50 )
                  {
                    v7 = (double)(v13->m_nLevel - 5);
                    pow(v7, 2);
                    if ( v24 < (signed int)floor(v7 / 20.0) )
                    {
                      v11 = 11;
                      goto $RESULT_57;
                    }
                  }
                  else
                  {
                    if ( v24 < 99 )
                    {
                      v11 = 11;
                      goto $RESULT_57;
                    }
                    if ( v13->m_nLevel > 50 )
                    {
                      v6 = ((int (__fastcall *)(CPlayer *))v26->vfptr->GetLevel)(v26);
                      if ( v13->m_nLevel > v6 + 1 )
                      {
                        v11 = 11;
                        goto $RESULT_57;
                      }
                    }
                  }
                  if ( CPlayer::GetFP(v26) < 60 )
                  {
                    v11 = 4;
                    goto $RESULT_57;
                  }
                }
                pNewAnimus = FindEmptyAnimus(g_Animus, 500);
                if ( !pNewAnimus )
                  v11 = 5;
                goto $RESULT_57;
              }
              v11 = 3;
            }
            else
            {
              v11 = 12;
            }
          }
          else
          {
            v11 = 8;
          }
        }
      }
      else
      {
        v11 = 2;
      }
    }
  }
  else
  {
    v11 = 10;
  }
$RESULT_57:
  if ( !v11 )
  {
    if ( v26->m_wTimeFreeRecallSerial != v12->m_wSerial )
    {
      v8 = CPlayer::GetFP(v26);
      CPlayer::SetFP(v26, v8 - nConsumeFPPoint, 1);
    }
    *(_WORD *)v14 = v16;
    *((_WORD *)v14 + 1) = v17;
    _animus_create_setdata::_animus_create_setdata(&Dst);
    Dst.m_pMap = v26->m_pCurMap;
    Dst.m_nLayerIndex = v26->m_wMapLayerIndex;
    Dst.m_pRecordSet = CRecordData::GetRecord(&stru_1799C6370, v12->m_wItemIndex);
    memcpy_0(Dst.m_fStartPos, v26->m_fCurPos, 0xCui64);
    Dst.nHP = *(_WORD *)v14;
    Dst.nFP = *((_WORD *)v14 + 1);
    Dst.dwExp = v12->m_dwDur;
    Dst.pMaster = v26;
    Dst.nMaxAttackPnt = v26->m_nAnimusAttackPnt;
    CAnimus::Create(pNewAnimus, &Dst);
    v26->m_pRecalledAnimusItem = (_STORAGE_LIST::_db_con *)v12;
    v26->m_pRecalledAnimusChar = pNewAnimus;
    v26->m_wTimeFreeRecallSerial = -1;
    _STORAGE_LIST::_storage_con::lock(v12, 1);
  }
  v9 = CPlayer::GetFP(v26);
  CPlayer::SendMsg_AnimusRecallResult(v26, v11, v9, pNewAnimus);
}
