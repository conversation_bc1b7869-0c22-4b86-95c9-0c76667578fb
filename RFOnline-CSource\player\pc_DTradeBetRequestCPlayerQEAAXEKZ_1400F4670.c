/*
 * Function: ?pc_DTradeBetRequest@CPlayer@@QEAAXEK@Z
 * Address: 0x1400F4670
 */

void __fastcall CPlayer::pc_DTradeBetRequest(CPlayer *this, char byMoneyUnit, unsigned int dwBetAmount)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  char v6; // [sp+20h] [bp-38h]@4
  CPlayer *p_pDst; // [sp+38h] [bp-20h]@4
  CPlayer *lp_pOne; // [sp+60h] [bp+8h]@1
  char v9; // [sp+68h] [bp+10h]@1
  unsigned int dwAmount; // [sp+70h] [bp+18h]@1

  dwAmount = dwBetAmount;
  v9 = byMoneyUnit;
  lp_pOne = this;
  v3 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = 0;
  p_pDst = 0i64;
  if ( DTradeEqualPerson(lp_pOne, &p_pDst) )
  {
    if ( lp_pOne->m_pmTrd.bDTradeLock )
    {
      v6 = 4;
    }
    else if ( lp_pOne->m_pCurMap->m_pMapSet->m_nMapType == 1 )
    {
      v6 = 6;
    }
    else if ( v9 )
    {
      if ( dwAmount > CPlayerDB::GetGold(&lp_pOne->m_Param) )
        v6 = 2;
    }
    else if ( dwAmount > CPlayerDB::GetDalant(&lp_pOne->m_Param) )
    {
      v6 = 1;
    }
  }
  else
  {
    v6 = 1;
  }
  if ( v6 )
  {
    _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
    CPlayer::SendMsg_DTradeCloseInform(lp_pOne, 0);
    if ( p_pDst )
    {
      _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
      CPlayer::SendMsg_DTradeCloseInform(p_pDst, 0);
    }
  }
  else
  {
    if ( v9 )
      lp_pOne->m_pmTrd.dwDTrade_Gold = dwAmount;
    else
      lp_pOne->m_pmTrd.dwDTrade_Dalant = dwAmount;
    CPlayer::SendMsg_DTradeBetInform(p_pDst, v9, dwAmount);
    CPlayer::SendMsg_DTradeBetResult(lp_pOne, v6);
  }
}
