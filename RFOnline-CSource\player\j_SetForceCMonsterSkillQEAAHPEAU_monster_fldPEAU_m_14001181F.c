/*
 * Function: j_?Set<PERSON><PERSON>ce@CMonsterSkill@@QEAAHPEAU_monster_fld@@PEAU_monster_sp_fld@@HPEAU_force_fld@@KMKHHH@Z
 * Address: 0x14001181F
 */

int __fastcall CMonsterSkill::SetForce(CMonsterSkill *this, _monster_fld *pMonsterFld, _monster_sp_fld *pSPCont, int nSFLv, _force_fld *pForceFld, unsigned int dwDelayTime, float fAttackDist, unsigned int dwCastDelay, int nMotive, int nMotiveValue, int skillDestType)
{
  return CMonsterSkill::SetForce(
           this,
           pMonsterFld,
           pSPCont,
           nSFLv,
           pForceFld,
           dwDelayTime,
           fAttackDist,
           dwCastDelay,
           nMotive,
           nMotiveValue,
           skillDestType);
}
