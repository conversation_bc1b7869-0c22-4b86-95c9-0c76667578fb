/*
 * Function: ?DecPvpPoint@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAANAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E1860
 */

long double __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::DecPvpPoint(GUILD_BATTLE::CNormalGuildBattleGuild *this, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  double v4; // xmm0_8@4
  long double result; // xmm0_8@9
  __int64 v6; // [sp+0h] [bp-98h]@1
  __int128 v7; // [sp+20h] [bp-78h]@9
  char pSend; // [sp+38h] [bp-60h]@4
  double v9; // [sp+39h] [bp-5Fh]@8
  char byType; // [sp+64h] [bp-34h]@4
  char v11; // [sp+65h] [bp-33h]@4
  double v12; // [sp+78h] [bp-20h]@4
  int j; // [sp+80h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v14; // [sp+A0h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleLogger *kLoggera; // [sp+A8h] [bp+10h]@1

  kLoggera = kLogger;
  v14 = this;
  v2 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  byType = 27;
  v11 = 79;
  pSend = 0;
  v4 = 0.0;
  v12 = 0.0;
  for ( j = 0; j < 50; ++j )
  {
    if ( !GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEmpty(&v14->m_kMember[j]) )
    {
      GUILD_BATTLE::CNormalGuildBattleGuildMember::DecPvpPoint(&v14->m_kMember[j], kLoggera);
      v9 = v4;
      v4 = v12 + v4;
      v12 = v12 + v9;
      GUILD_BATTLE::CNormalGuildBattleGuildMember::Send(&v14->m_kMember[j], &byType, &pSend, 9u);
    }
  }
  _mm_storeu_si128((__m128i *)&v7, (__m128i)*(unsigned __int64 *)&v12);
  *(_QWORD *)&result = (unsigned __int128)_mm_loadu_si128((const __m128i *)&v7);
  return result;
}
