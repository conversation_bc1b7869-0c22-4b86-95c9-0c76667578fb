/*
 * Function: j_?SetAggro@CMonsterAggroMgr@@QEAAXPEAVCCharacter@@HHKHH@Z
 * Address: 0x140005D67
 */

void __fastcall CMonsterAggroMgr::SetAggro(CMonsterAggroMgr *this, CCharacter *pCharacter, int nDam, int nAttackType, unsigned int dwAttackSerial, int bOtherPlayerSupport, int bTempSkill)
{
  CMonsterAggroMgr::SetAggro(this, pCharacter, nDam, nAttackType, dwAttackSerial, bOtherPlayerSupport, bTempSkill);
}
