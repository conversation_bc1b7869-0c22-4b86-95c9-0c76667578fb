/*
 * Function: ?_db_Load_PcBangFavor@CMainThread@@AEAAEKPEAU_PCBANG_FAVOR_ITEM_DB_BASE@@@Z
 * Address: 0x1401B73F0
 */

char __fastcall CMainThread::_db_Load_PcBangFavor(CMainThread *this, unsigned int dwSerial, _PCBANG_FAVOR_ITEM_DB_BASE *pDbPcBangFavor)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-1D8h]@1
  _worlddb_pcbang_favor_item pPcBangFavorItem; // [sp+30h] [bp-1A8h]@4
  char v8; // [sp+1C4h] [bp-14h]@4
  int j; // [sp+1C8h] [bp-10h]@11
  CMainThread *v10; // [sp+1E0h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+1E8h] [bp+10h]@1
  _PCBANG_FAVOR_ITEM_DB_BASE *v12; // [sp+1F0h] [bp+18h]@1

  v12 = pDbPcBangFavor;
  dwSeriala = dwSerial;
  v10 = this;
  v3 = &v6;
  for ( i = 116i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = CRFWorldDatabase::Select_PcBangFavorItem(v10->m_pWorldDB, dwSerial, &pPcBangFavorItem);
  if ( v8 == 1 )
    return 24;
  if ( v8 != 2 )
    goto LABEL_17;
  if ( !CRFWorldDatabase::Insert_PcBangFavorItem(v10->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_PcBangFavorItem(v10->m_pWorldDB, dwSeriala, &pPcBangFavorItem) )
  {
    result = 24;
  }
  else
  {
LABEL_17:
    for ( j = 0; j < 50; ++j )
      v12->lnUID[j] = pPcBangFavorItem.lnUID[j];
    result = 0;
  }
  return result;
}
