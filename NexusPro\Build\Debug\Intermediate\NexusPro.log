﻿  pch.cpp
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(6,16): error C2011: 'NexusPro::LogLevel': 'enum' type redefinition
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Common.h(22,16):
      see declaration of 'NexusPro::LogLevel'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(17,22): error C2371: 'NexusPro::Constants::MAX_LOG_MESSAGE': redefinition; different basic types
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Common.h(8,25):
      see declaration of 'NexusPro::Constants::MAX_LOG_MESSAGE'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,21): error C2686: cannot overload static and non-static member functions with the same parameter types
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,21):
      could be 'void NexusPro::Logger::Trace(const std::wstring &)'
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(46,14):
      or       'void NexusPro::Logger::Trace(const std::wstring &)'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,71): error C3484: syntax error: expected '->' before the return type
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,72): error C3613: missing return type after '->' ('int' assumed)
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,72): error C3646: 'Trace': unknown override specifier
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,58): error C3551: if a trailing return type is used then the leading return type shall be the single type-specifier 'auto' (not 'int')
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,58): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,58): error C2686: cannot overload static and non-static member functions with the same parameter types
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(37,24):
      could be 'NexusPro::Logger &NexusPro::Logger::GetInstance(void)'
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,58):
      or       'int NexusPro::Logger::GetInstance(void)'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,77): error C2059: syntax error: '('
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(54,58): warning C4183: 'GetInstance': missing return type; assumed to be a member function returning 'int'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(55,16): error C2628: 'NexusPro::Logger' followed by 'void' is illegal (did you forget a ';'?)
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(55,58): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(32,44): error C2027: use of undefined type 'NexusPro::LogLevel'
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Common.h(22,16):
      see declaration of 'NexusPro::LogLevel'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(32,44): error C3867: 'NexusPro::Logger::Info': non-standard syntax; use '&' to create a pointer to member
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(46,65): error C2027: use of undefined type 'NexusPro::LogLevel'
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Common.h(22,16):
      see declaration of 'NexusPro::LogLevel'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(46,65): error C3867: 'NexusPro::Logger::Trace': non-standard syntax; use '&' to create a pointer to member
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(47,65): error C2027: use of undefined type 'NexusPro::LogLevel'
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Common.h(22,16):
      see declaration of 'NexusPro::LogLevel'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(47,65): error C3867: 'NexusPro::Logger::Debug': non-standard syntax; use '&' to create a pointer to member
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(48,64): error C2027: use of undefined type 'NexusPro::LogLevel'
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Common.h(22,16):
      see declaration of 'NexusPro::LogLevel'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(48,64): error C3867: 'NexusPro::Logger::Info': non-standard syntax; use '&' to create a pointer to member
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(49,67): error C2027: use of undefined type 'NexusPro::LogLevel'
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Common.h(22,16):
      see declaration of 'NexusPro::LogLevel'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(49,67): error C3867: 'NexusPro::Logger::Warning': non-standard syntax; use '&' to create a pointer to member
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(50,65): error C2027: use of undefined type 'NexusPro::LogLevel'
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Common.h(22,16):
      see declaration of 'NexusPro::LogLevel'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(50,65): error C3867: 'NexusPro::Logger::Error': non-standard syntax; use '&' to create a pointer to member
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(51,68): error C2027: use of undefined type 'NexusPro::LogLevel'
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Common.h(22,16):
      see declaration of 'NexusPro::LogLevel'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(51,68): error C3867: 'NexusPro::Logger::Critical': non-standard syntax; use '&' to create a pointer to member
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(56,57): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(57,60): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(58,58): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(59,61): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(67,17): error C3861: 'Log': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(67,20):
      'Log': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(70,17): error C3861: 'Log': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(70,20):
      'Log': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(76,13): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(76,24):
      'GetInstance': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(81,13): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(81,24):
      'GetInstance': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(86,13): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(86,24):
      'GetInstance': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(91,22): error C2270: 'GetLogFilePath': modifiers not allowed on nonmember functions
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(91,54): error C2065: 'logFilePath_': undeclared identifier
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(93,5): error C2059: syntax error: 'private'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(109,1): error C2059: syntax error: '}'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(109,1): error C2143: syntax error: missing ';' before '}'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\RFTypes.h(5,20): error C2143: syntax error: missing ';' before '{'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\RFTypes.h(5,20): error C2447: '{': missing function header (old-style formal list?)
  (compiling source file 'pch.cpp')
  
