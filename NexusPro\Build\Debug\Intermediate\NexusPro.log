﻿  pch.cpp
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,21): error C2686: cannot overload static and non-static member functions with the same parameter types
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,21):
      could be 'void NexusPro::Logger::Trace(const std::wstring &)'
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(31,14):
      or       'void NexusPro::Logger::Trace(const std::wstring &)'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,71): error C3484: syntax error: expected '->' before the return type
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,72): error C3613: missing return type after '->' ('int' assumed)
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,72): error C3646: 'Trace': unknown override specifier
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,58): error C3551: if a trailing return type is used then the leading return type shall be the single type-specifier 'auto' (not 'int')
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,58): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,58): error C2686: cannot overload static and non-static member functions with the same parameter types
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(22,24):
      could be 'NexusPro::Logger &NexusPro::Logger::GetInstance(void)'
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,58):
      or       'int NexusPro::Logger::GetInstance(void)'
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,77): error C2059: syntax error: '('
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(39,58): warning C4183: 'GetInstance': missing return type; assumed to be a member function returning 'int'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(40,16): error C2628: 'NexusPro::Logger' followed by 'void' is illegal (did you forget a ';'?)
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(40,58): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(41,57): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(42,60): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(43,58): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(44,61): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(52,17): error C3861: 'Log': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(52,20):
      'Log': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(55,17): error C3861: 'Log': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(55,20):
      'Log': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(61,13): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(61,24):
      'GetInstance': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(66,13): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(66,24):
      'GetInstance': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(71,13): error C3861: 'GetInstance': identifier not found
  (compiling source file 'pch.cpp')
      D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(71,24):
      'GetInstance': function declaration must be available as none of the arguments depend on a template parameter
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(76,22): error C2270: 'GetLogFilePath': modifiers not allowed on nonmember functions
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(76,54): error C2065: 'logFilePath_': undeclared identifier
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(78,5): error C2059: syntax error: 'private'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(93,13): error C2653: 'Logger': is not a class or namespace name
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(93,21): error C3861: 'Debug': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(99,13): error C2653: 'Logger': is not a class or namespace name
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(99,21): error C3861: 'Debug': identifier not found
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(102,1): error C2059: syntax error: '}'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\Logger.h(102,1): error C2143: syntax error: missing ';' before '}'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\RFTypes.h(5,20): error C2143: syntax error: missing ';' before '{'
  (compiling source file 'pch.cpp')
  
D:\4_GameGuardProject2232server\NexusPro\Utils\RFTypes.h(5,20): error C2447: '{': missing function header (old-style formal list?)
  (compiling source file 'pch.cpp')
  
