#pragma once

namespace NexusPro {
    
    // Common constants
    namespace Constants {
        constexpr DWORD MAX_PATH_LENGTH = 260;
        constexpr DWORD MAX_LOG_MESSAGE = 1024;
        constexpr DWORD HOOK_TIMEOUT_MS = 5000;
        constexpr DWORD CONFIG_CHECK_INTERVAL_MS = 30000;
    }
    
    // Common enums
    enum class HookStatus {
        NotInstalled,
        Installing,
        Installed,
        Failed,
        Uninstalling
    };
    
    enum class LogLevel {
        Trace = 0,
        Debug = 1,
        Info = 2,
        Warning = 3,
        Error = 4,
        Critical = 5
    };
    
    enum class ModuleType {
        Core,
        Authentication,
        Network,
        BugFix,
        Enhancement,
        Monitoring
    };
    
    // Common structures
    struct HookInfo {
        std::wstring name;
        LPVOID originalFunction;
        LPVOID hookFunction;
        HookStatus status;
        ModuleType moduleType;
        bool enabled;
        DWORD installTime;
    };
    
    struct ModuleInfo {
        std::wstring name;
        std::wstring description;
        ModuleType type;
        bool enabled;
        std::vector<std::wstring> dependencies;
        DWORD version;
    };
    
    // Utility functions
    namespace Utils {
        
        // String conversion utilities
        inline std::wstring StringToWString(const std::string& str) {
            if (str.empty()) return std::wstring();
            int size = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, nullptr, 0);
            std::wstring result(size - 1, 0);
            MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &result[0], size);
            return result;
        }
        
        inline std::string WStringToString(const std::wstring& wstr) {
            if (wstr.empty()) return std::string();
            int size = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
            std::string result(size - 1, 0);
            WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], size, nullptr, nullptr);
            return result;
        }
        
        // Time utilities
        inline std::wstring GetCurrentTimeString() {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                now.time_since_epoch()) % 1000;
            
            std::tm tm;
            localtime_s(&tm, &time_t);
            
            wchar_t buffer[64];
            swprintf_s(buffer, L"%04d-%02d-%02d %02d:%02d:%02d.%03d",
                tm.tm_year + 1900, tm.tm_mon + 1, tm.tm_mday,
                tm.tm_hour, tm.tm_min, tm.tm_sec, static_cast<int>(ms.count()));
            
            return std::wstring(buffer);
        }
        
        // Memory utilities
        inline bool IsValidPointer(LPVOID ptr) {
            if (!ptr) return false;
            
            MEMORY_BASIC_INFORMATION mbi;
            if (VirtualQuery(ptr, &mbi, sizeof(mbi)) == 0) {
                return false;
            }
            
            return (mbi.State == MEM_COMMIT) && 
                   (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE));
        }
        
        // Process utilities
        inline HMODULE GetProcessModule(const std::wstring& moduleName) {
            return GetModuleHandleW(moduleName.c_str());
        }
        
        inline LPVOID GetProcAddressEx(HMODULE hModule, const std::string& procName) {
            return GetProcAddress(hModule, procName.c_str());
        }
        
        // File utilities
        inline bool FileExists(const std::wstring& filePath) {
            DWORD attributes = GetFileAttributesW(filePath.c_str());
            return (attributes != INVALID_FILE_ATTRIBUTES && 
                    !(attributes & FILE_ATTRIBUTE_DIRECTORY));
        }
        
        inline std::wstring GetModuleDirectory() {
            wchar_t path[MAX_PATH];
            GetModuleFileNameW(nullptr, path, MAX_PATH);
            std::wstring fullPath(path);
            size_t lastSlash = fullPath.find_last_of(L"\\");
            return (lastSlash != std::wstring::npos) ? fullPath.substr(0, lastSlash + 1) : L"";
        }
        
        // Error handling
        inline std::wstring GetLastErrorString() {
            DWORD error = GetLastError();
            if (error == 0) return L"No error";
            
            LPWSTR buffer = nullptr;
            size_t size = FormatMessageW(
                FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                nullptr, error, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                (LPWSTR)&buffer, 0, nullptr);
            
            std::wstring message(buffer, size);
            LocalFree(buffer);
            
            // Remove trailing newlines
            while (!message.empty() && (message.back() == L'\n' || message.back() == L'\r')) {
                message.pop_back();
            }
            
            return message;
        }
    }
    
    // RAII helper classes
    class CriticalSectionLock {
    private:
        CRITICAL_SECTION& cs_;
    public:
        explicit CriticalSectionLock(CRITICAL_SECTION& cs) : cs_(cs) {
            EnterCriticalSection(&cs_);
        }
        ~CriticalSectionLock() {
            LeaveCriticalSection(&cs_);
        }
    };
    
    class HandleGuard {
    private:
        HANDLE handle_;
    public:
        explicit HandleGuard(HANDLE handle) : handle_(handle) {}
        ~HandleGuard() {
            if (handle_ && handle_ != INVALID_HANDLE_VALUE) {
                CloseHandle(handle_);
            }
        }
        HANDLE get() const { return handle_; }
        HANDLE release() {
            HANDLE temp = handle_;
            handle_ = nullptr;
            return temp;
        }
    };
}
