/*
 * Function: ?pc_AnimusTargetRequest@CPlayer@@QEAAXEGK@Z
 * Address: 0x1400D0B60
 */

void __usercall CPlayer::pc_AnimusTargetRequest(CPlayer *this@<rcx>, char byObjectID@<dl>, unsigned __int16 wObjectIndex@<r8w>, unsigned int dwObjectSerial@<r9d>, float a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  char v8; // [sp+20h] [bp-18h]@4
  CCharacter *pTarget; // [sp+28h] [bp-10h]@4
  CPlayer *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8 = 0;
  pTarget = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, (unsigned __int8)byObjectID, wObjectIndex);
  if ( v10->m_pRecalledAnimusItem && v10->m_pRecalledAnimusChar )
  {
    if ( pTarget->m_bLive && !pTarget->m_bCorpse )
    {
      GetSqrt(v10->m_pRecalledAnimusChar->m_fCurPos, pTarget->m_fCurPos);
      if ( a5 > 400.0 )
        v8 = 8;
    }
    else
    {
      v8 = 8;
    }
  }
  else
  {
    v8 = 7;
  }
  if ( !v8 && !CAnimus::ChangeTarget_MasterCommand(v10->m_pRecalledAnimusChar, pTarget) )
    v8 = 8;
  CPlayer::SendMsg_AnimusTargetResult(v10, v8);
}
