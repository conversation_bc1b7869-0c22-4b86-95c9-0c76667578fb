/*
 * Function: ?OnLoop_Static@CPlayer@@SAXXZ
 * Address: 0x140066350
 */

void CPlayer::OnLoop_Static(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  CMgrAvatorItemHistory::OnLoop(&CPlayer::s_MgrItemHistory);
  CMgrAvatorLvHistory::OnLoop(&CPlayer::s_MgrLvHistory);
  _DELAY_PROCESS::CheckOnLoop(&CPlayer::s_AnimusReturnDelay);
  _DELAY_PROCESS::CheckOnLoop(&CPlayer::s_BillingForceCloseDelay);
}
