/*
 * Function: ?CleanUp@CGuildBattleController@@IEAAXXZ
 * Address: 0x1403D7900
 */

void __fastcall CGuildBattleController::CleanUp(CGuildBattleController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1

  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  GUILD_BATTLE::CGuildBattleLogger::Destroy();
  GUILD_BATTLE::CGuildBattleRankManager::Destroy();
  GUILD_BATTLE::CNormalGuildBattleFieldList::Destroy();
  GUILD_BATTLE::CGuildBattleScheduler::Destroy();
  GUILD_BATTLE::CPossibleBattleGuildListManager::Destroy();
  GUILD_BATTLE::CNormalGuildBattleManager::Destroy();
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Destroy();
  GUILD_BATTLE::CCurrentGuildBattleInfoManager::Destroy();
  GUILD_BATTLE::CNormalGuildBattleStateListPool::Destroy();
}
