/*
 * Function: ?ClearInBattleState@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E1680
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::ClearInBattleState(GUILD_BATTLE::CNormalGuildBattleGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 50; ++j )
  {
    if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v5->m_kMember[j]) )
      GUILD_BATTLE::CNormalGuildBattleGuildMember::SetBattleState(&v5->m_kMember[j], 0, -1);
  }
  v5->m_pkGuild->m_bInGuildBattle = 0;
}
