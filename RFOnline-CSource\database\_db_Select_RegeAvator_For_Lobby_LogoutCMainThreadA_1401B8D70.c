/*
 * Function: ?_db_Select_RegeAvator_For_Lobby_Logout@CMainThread@@AEAAEPEAD@Z
 * Address: 0x1401B8D70
 */

char __fastcall CMainThread::_db_Select_RegeAvator_For_Lobby_Logout(CMainThread *this, char *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-E8h]@1
  char *v6; // [sp+20h] [bp-C8h]@4
  _rege_char_data pRegeCharData; // [sp+40h] [bp-A8h]@4
  int j; // [sp+C4h] [bp-24h]@6
  unsigned __int64 v9; // [sp+D0h] [bp-18h]@4
  CMainThread *v10; // [sp+F0h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  v6 = pSheet;
  _rege_char_data::_rege_char_data(&pRegeCharData);
  v6[68] = CRFWorldDatabase::Select_RegeAvator_For_Lobby_Logout(v10->m_pWorldDB, *(_DWORD *)v6, &pRegeCharData);
  if ( v6[68] )
  {
    result = 24;
  }
  else
  {
    *((_DWORD *)v6 + 18) = pRegeCharData.nCharNum;
    for ( j = 0; j < *((_DWORD *)v6 + 18); ++j )
    {
      v6[40 * j + 76] = pRegeCharData.RegeList[j].bySlotIndex;
      *(_DWORD *)&v6[40 * j + 80] = pRegeCharData.RegeList[j].dwCharSerial;
      *(_DWORD *)&v6[40 * j + 104] = pRegeCharData.RegeList[j].nLevel;
      *(_DWORD *)&v6[40 * j + 108] = pRegeCharData.RegeList[j].dwDalant;
      *(_DWORD *)&v6[40 * j + 112] = pRegeCharData.RegeList[j].dwGold;
      strcpy_s(&v6[40 * j + 84], 0x11ui64, pRegeCharData.RegeList[j].szCharName);
    }
    result = 0;
  }
  return result;
}
