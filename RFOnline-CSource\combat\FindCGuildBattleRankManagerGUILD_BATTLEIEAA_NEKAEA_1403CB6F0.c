/*
 * Function: ?Find@CGuildBattleRankManager@GUILD_BATTLE@@IEAA_NEKAEAHAEAE@Z
 * Address: 0x1403CB6F0
 */

char __fastcall GUILD_BATTLE::CGuildBattleRankManager::Find(GUILD_BATTLE::CGuildBattleRankManager *this, char byRace, unsigned int dwGuildSerial, int *iFindInx, char *byFindPage)
{
  int *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int j; // [sp+0h] [bp-18h]@1
  int k; // [sp+4h] [bp-14h]@8
  GUILD_BATTLE::CGuildBattleRankManager *v10; // [sp+20h] [bp+8h]@1

  v10 = this;
  v5 = (int *)&j;
  for ( i = 4i64; i; --i )
  {
    *v5 = -858993460;
    ++v5;
  }
  if ( dwGuildSerial == -1 )
  {
    result = 0;
  }
  else
  {
    for ( j = 0; j < 0x1E; ++j )
    {
      for ( k = 0; k < 10; ++k )
      {
        if ( v10->m_dwGuildSerial[(unsigned __int8)byRace][j][k] == dwGuildSerial )
        {
          *byFindPage = j;
          *iFindInx = k;
          return 1;
        }
      }
    }
    result = 0;
  }
  return result;
}
