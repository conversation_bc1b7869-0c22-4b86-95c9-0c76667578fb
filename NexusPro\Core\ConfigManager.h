#pragma once

namespace NexusPro {
    
    class ConfigManager {
    private:
        std::wstring configFilePath_;
        std::unordered_map<std::wstring, std::wstring> configValues_;
        std::mutex configMutex_;
        FILETIME lastWriteTime_;
        std::thread watcherThread_;
        std::atomic<bool> stopWatcher_;
        
    public:
        ConfigManager();
        ~ConfigManager();
        
        // Initialization
        bool Initialize(const std::wstring& configFileName = L"nexuspro.ini");
        void Shutdown();
        
        // Configuration access
        std::wstring GetString(const std::wstring& key, const std::wstring& defaultValue = L"");
        int GetInt(const std::wstring& key, int defaultValue = 0);
        bool GetBool(const std::wstring& key, bool defaultValue = false);
        double GetDouble(const std::wstring& key, double defaultValue = 0.0);
        
        // Configuration modification
        void SetString(const std::wstring& key, const std::wstring& value);
        void SetInt(const std::wstring& key, int value);
        void SetBool(const std::wstring& key, bool value);
        void SetDouble(const std::wstring& key, double value);
        
        // File operations
        bool LoadConfig();
        bool SaveConfig();
        bool Reload();
        
        // Configuration sections for different modules
        struct CoreConfig {
            bool enableLogging;
            LogLevel logLevel;
            bool enableConsoleOutput;
            bool enableFileOutput;
            int hookTimeout;
            bool enableDebugMode;
            
            CoreConfig() : enableLogging(true), logLevel(LogLevel::Info), 
                          enableConsoleOutput(false), enableFileOutput(true),
                          hookTimeout(5000), enableDebugMode(false) {}
        };
        
        struct AuthenticationConfig {
            bool enableAuthHooks;
            bool enableLoginLogging;
            bool enableSessionValidation;
            bool enableAntiSpeedHack;
            int maxLoginAttempts;
            int sessionTimeout;
            
            AuthenticationConfig() : enableAuthHooks(true), enableLoginLogging(true),
                                   enableSessionValidation(true), enableAntiSpeedHack(true),
                                   maxLoginAttempts(5), sessionTimeout(300000) {}
        };
        
        struct NetworkConfig {
            bool enableNetworkHooks;
            bool enablePacketLogging;
            bool enablePacketValidation;
            bool enableAntiFlood;
            int maxPacketsPerSecond;
            int packetBufferSize;
            
            NetworkConfig() : enableNetworkHooks(true), enablePacketLogging(false),
                            enablePacketValidation(true), enableAntiFlood(true),
                            maxPacketsPerSecond(100), packetBufferSize(8192) {}
        };
        
        struct BugFixConfig {
            bool enableBugFixes;
            bool enableMonsterLimitFix;
            bool enableItemDupeFix;
            bool enableMemoryLeakFix;
            bool enableCrashFix;
            
            BugFixConfig() : enableBugFixes(true), enableMonsterLimitFix(true),
                           enableItemDupeFix(true), enableMemoryLeakFix(true),
                           enableCrashFix(true) {}
        };
        
        struct EnhancementConfig {
            bool enableEnhancements;
            bool enableImprovedAI;
            bool enableBetterGuildSystem;
            bool enableEnhancedPvP;
            bool enableCustomEvents;
            int aiUpdateInterval;
            
            EnhancementConfig() : enableEnhancements(true), enableImprovedAI(true),
                                enableBetterGuildSystem(true), enableEnhancedPvP(true),
                                enableCustomEvents(false), aiUpdateInterval(1000) {}
        };
        
        struct MonitoringConfig {
            bool enableMonitoring;
            bool enablePerformanceMonitoring;
            bool enableResourceMonitoring;
            bool enablePlayerMonitoring;
            int monitoringInterval;
            bool enableAlerts;
            
            MonitoringConfig() : enableMonitoring(true), enablePerformanceMonitoring(true),
                               enableResourceMonitoring(true), enablePlayerMonitoring(true),
                               monitoringInterval(30000), enableAlerts(true) {}
        };
        
        // Get configuration sections
        CoreConfig GetCoreConfig();
        AuthenticationConfig GetAuthenticationConfig();
        NetworkConfig GetNetworkConfig();
        BugFixConfig GetBugFixConfig();
        EnhancementConfig GetEnhancementConfig();
        MonitoringConfig GetMonitoringConfig();
        
        // Set configuration sections
        void SetCoreConfig(const CoreConfig& config);
        void SetAuthenticationConfig(const AuthenticationConfig& config);
        void SetNetworkConfig(const NetworkConfig& config);
        void SetBugFixConfig(const BugFixConfig& config);
        void SetEnhancementConfig(const EnhancementConfig& config);
        void SetMonitoringConfig(const MonitoringConfig& config);
        
        // Utility methods
        bool HasKey(const std::wstring& key);
        void RemoveKey(const std::wstring& key);
        std::vector<std::wstring> GetAllKeys();
        std::wstring GetConfigFilePath() const { return configFilePath_; }
        
        // File watching
        void StartFileWatcher();
        void StopFileWatcher();
        
    private:
        void ParseConfigLine(const std::wstring& line);
        std::wstring FormatConfigLine(const std::wstring& key, const std::wstring& value);
        void FileWatcherThread();
        bool HasFileChanged();
        void UpdateFileTime();
        
        // Helper methods for type conversion
        int StringToInt(const std::wstring& str, int defaultValue = 0);
        bool StringToBool(const std::wstring& str, bool defaultValue = false);
        double StringToDouble(const std::wstring& str, double defaultValue = 0.0);
        std::wstring BoolToString(bool value);
        std::wstring IntToString(int value);
        std::wstring DoubleToString(double value);
        
        // Section key helpers
        std::wstring MakeSectionKey(const std::wstring& section, const std::wstring& key);
    };
}
