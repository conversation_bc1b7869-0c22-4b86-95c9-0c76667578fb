/*
 * Function: ?_wait_tsk_cash_rollback@CCashDbWorkerJP@@MEAAHPEAVTask@@@Z
 * Address: 0x140320200
 */

__int64 __fastcall CCashDbWorkerJP::_wait_tsk_cash_rollback(CCashDbWorkerJP *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  _param_cash_rollback *list; // [sp+20h] [bp-18h]@4
  int iIndex; // [sp+28h] [bp-10h]@4
  CCashDbWorkerJP *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  list = (_param_cash_rollback *)Task::GetTaskBuf(pkTsk);
  for ( iIndex = 0; iIndex < list->in_byNum; ++iIndex )
  {
    if ( CRFCashItemDatabase::CallProc_RFONLINE_Cancel_Jap(v8->_pkDb, list, iIndex) )
    {
      list->data[iIndex].out_nCashAmount = 0;
      list->out_nCashAmount = 0;
      list->data[iIndex].out_cStatus = 1;
    }
    else
    {
      list->data[iIndex].out_cStatus = 0;
      if ( list->data[iIndex].out_cStatus )
      {
        list->data[iIndex].out_nCashAmount = 0;
        list->out_nCashAmount = 0;
      }
      else
      {
        list->out_nCashAmount = list->data[iIndex].out_nCashAmount;
      }
    }
  }
  return 0i64;
}
