/*
 * Function: ?LogIn@CGuildBattleController@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403D5E40
 */

void __fastcall CGuildBattleController::LogIn(CGuildBattleController *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  unsigned int dwGuildSerial; // [sp+24h] [bp-14h]@4
  unsigned int dwCharacSerial; // [sp+28h] [bp-10h]@4

  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  n = pkPlayer->m_ObjID.m_wIndex;
  dwGuildSerial = pkPlayer->m_Param.m_pGuild->m_dwSerial;
  dwCharacSerial = pkPlayer->m_pUserDB->m_dwSerial;
  v4 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::LogIn(v4, n, dwGuildSerial, dwCharacSerial);
}
