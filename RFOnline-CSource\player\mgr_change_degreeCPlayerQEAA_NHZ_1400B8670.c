/*
 * Function: ?mgr_change_degree@CPlayer@@QEAA_NH@Z
 * Address: 0x1400B8670
 */

char __fastcall CPlayer::mgr_change_degree(CPlayer *this, int nDegree)
{
  char *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // [sp+0h] [bp-18h]@1
  CPlayer *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 += 4;
  }
  v5 = v6->m_byUserDgr;
  v6->m_byUserDgr = nDegree;
  v6->m_pUserDB->m_byUserDgr = nDegree;
  return 1;
}
