/*
 * Function: ?OnButtonOffplayer@CGameServerView@@QEAAXXZ
 * Address: 0x14002B290
 */

void __fastcall CGameServerView::OnButtonOffplayer(CGameServerView *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1

  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
    bCreate = bCreate == 0;
}
