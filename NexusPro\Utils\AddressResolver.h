#pragma once

namespace NexusPro {
    
    class AddressResolver {
    private:
        HMODULE targetModule_;
        std::wstring moduleName_;
        LPVOID moduleBase_;
        DWORD moduleSize_;
        std::unordered_map<std::string, LPVOID> cachedAddresses_;
        std::mutex cacheMutex_;
        
    public:
        AddressResolver();
        ~AddressResolver();
        
        // Initialization
        bool Initialize(const std::wstring& moduleName = L"");
        bool IsInitialized() const { return targetModule_ != nullptr; }
        
        // Address resolution methods
        LPVOID GetFunctionAddress(const std::string& functionName);
        LPVOID GetAddressByPattern(const std::vector<BYTE>& pattern, const std::string& mask);
        LPVOID GetAddressBySignature(const std::string& signature);
        LPVOID GetRelativeAddress(LPVOID baseAddress, DWORD offset);
        
        // Pattern scanning
        LPVOID ScanPattern(LPVOID startAddress, SIZE_T scanSize, const std::vector<BYTE>& pattern, const std::string& mask);
        std::vector<LPVOID> ScanAllPatterns(const std::vector<BYTE>& pattern, const std::string& mask);
        
        // Module information
        HMODULE GetModule() const { return targetModule_; }
        LPVOID GetModuleBase() const { return moduleBase_; }
        DWORD GetModuleSize() const { return moduleSize_; }
        std::wstring GetModuleName() const { return moduleName_; }
        
        // RF Online specific addresses (based on decompiled code analysis)
        struct RFAddresses {
            // Authentication functions
            LPVOID LoginCBillingManager_Login;
            LPVOID OnCheckSession_FirstVerify;
            LPVOID OnConnectSession;
            LPVOID OnDisConnectSession;
            
            // Network functions
            LPVOID CNetworkEX_SendMsg;
            LPVOID CNetworkEX_RecvMsg;
            LPVOID LogInControllServer;
            LPVOID LogInWebAgentServer;
            
            // Monster functions
            LPVOID CreateCMonster;
            LPVOID CMonster_Loop;
            LPVOID CMonsterAI_Update;
            LPVOID SearchNearMonster;
            
            // World functions
            LPVOID CMapData_LoadMonBlk;
            LPVOID CMapOperation_RespawnMonster;
            LPVOID UpdateSecterList;
            
            // Guild functions
            LPVOID CGuild_SendMsg_GuildMemberLogin;
            LPVOID LogInCNormalGuildBattle;
            
            // Item/Economy functions
            LPVOID CItem_Create;
            LPVOID AutoTrade_Login;
            
            RFAddresses() { memset(this, 0, sizeof(RFAddresses)); }
        };
        
        // Get RF Online specific addresses
        bool ResolveRFAddresses(RFAddresses& addresses);
        
        // Utility methods
        bool IsAddressValid(LPVOID address);
        bool IsAddressInModule(LPVOID address);
        std::wstring GetAddressInfo(LPVOID address);
        
        // Cache management
        void ClearCache();
        size_t GetCacheSize() const;
        
    private:
        bool UpdateModuleInfo();
        LPVOID ResolveByExportName(const std::string& exportName);
        LPVOID ResolveByOrdinal(WORD ordinal);
        bool PatternMatch(const BYTE* data, const std::vector<BYTE>& pattern, const std::string& mask);
        
        // RF Online specific pattern signatures
        struct RFPatterns {
            // Authentication patterns (based on function analysis)
            static const std::vector<BYTE> LOGIN_BILLING_PATTERN;
            static const std::string LOGIN_BILLING_MASK;
            
            static const std::vector<BYTE> CHECK_SESSION_PATTERN;
            static const std::string CHECK_SESSION_MASK;
            
            // Network patterns
            static const std::vector<BYTE> NETWORK_SEND_PATTERN;
            static const std::string NETWORK_SEND_MASK;
            
            static const std::vector<BYTE> NETWORK_RECV_PATTERN;
            static const std::string NETWORK_RECV_MASK;
            
            // Monster patterns
            static const std::vector<BYTE> CREATE_MONSTER_PATTERN;
            static const std::string CREATE_MONSTER_MASK;
            
            static const std::vector<BYTE> MONSTER_LOOP_PATTERN;
            static const std::string MONSTER_LOOP_MASK;
        };
    };
}
