/*
 * Function: ?_wait_tsk_cash_rollback@CCashDbWorkerRU@@MEAAHPEAVTask@@@Z
 * Address: 0x140320A80
 */

__int64 __fastcall CCashDbWorkerRU::_wait_tsk_cash_rollback(CCashDbWorkerRU *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@4
  int j; // [sp+28h] [bp-10h]@4
  CCashDbWorkerRU *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = Task::GetTaskBuf(pkTsk);
  for ( j = 0; j < *((_DWORD *)v6 + 8); ++j )
  {
    if ( CRusiaBillingMgr::CallFunc_Item_Cancel(v8->_pkBill, (_param_cash_rollback::__list *)&v6[48 * j + 112], v6 + 16) )
    {
      *(_DWORD *)&v6[48 * j + 148] = 0;
      *((_DWORD *)v6 + 9) = 0;
      v6[48 * j + 144] = 1;
    }
    else
    {
      v6[48 * j + 144] = 0;
      if ( v6[48 * j + 144] )
      {
        *(_DWORD *)&v6[48 * j + 148] = 0;
        *((_DWORD *)v6 + 9) = 0;
      }
      else
      {
        *((_DWORD *)v6 + 9) = *(_DWORD *)&v6[48 * j + 148];
      }
    }
  }
  return 0i64;
}
