/*
 * Function: ?mgr_recall_guild_player@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BF8C0
 */

char __fastcall CPlayer::mgr_recall_guild_player(CPlayer *this, char *wszDestCharName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  int iRange; // [sp+20h] [bp-38h]@13
  CPlayer *v7; // [sp+30h] [bp-28h]@8
  CPlayer *v8; // [sp+38h] [bp-20h]@14
  int j; // [sp+40h] [bp-18h]@14
  _guild_member_info *v10; // [sp+48h] [bp-10h]@17
  CPlayer *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( wszDestCharName )
  {
    if ( v11->m_pCurMap->m_pMapSet->m_nMapType )
    {
      result = 0;
    }
    else
    {
      v7 = GetPtrPlayerFromName(&g_Player, 2532, wszDestCharName);
      if ( v7 )
      {
        if ( v7->m_Param.m_pGuild )
        {
          v8 = 0i64;
          for ( j = 0; j < 50; ++j )
          {
            v10 = &v7->m_Param.m_pGuild->m_MemberData[j];
            if ( _guild_member_info::IsFill(v10) )
            {
              if ( v10->pPlayer )
              {
                v8 = v10->pPlayer;
                if ( v8->m_bLive )
                {
                  if ( v11->m_dwObjSerial != v8->m_dwObjSerial )
                  {
                    iRange = 200;
                    CPlayer::RecallRandomPositionInRange(v8, v11->m_pCurMap, v11->m_wMapLayerIndex, v11->m_fCurPos, 200);
                  }
                }
              }
            }
          }
          result = 1;
        }
        else if ( v11->m_dwObjSerial == v7->m_dwObjSerial )
        {
          result = 0;
        }
        else
        {
          iRange = 200;
          CPlayer::RecallRandomPositionInRange(v7, v11->m_pCurMap, v11->m_wMapLayerIndex, v11->m_fCurPos, 200);
          result = 1;
        }
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
