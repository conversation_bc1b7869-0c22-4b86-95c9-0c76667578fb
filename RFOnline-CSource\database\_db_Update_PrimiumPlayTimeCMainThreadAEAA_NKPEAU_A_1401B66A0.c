/*
 * Function: ?_db_Update_PrimiumPlayTime@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD1@Z
 * Address: 0x1401B66A0
 */

char __fastcall CMainThread::_db_Update_PrimiumPlayTime(CMainThread *this, unsigned int dwAccSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szQuery, char *szError)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  size_t v8; // rax@14
  __int64 v10; // [sp+0h] [bp-D8h]@1
  char Dest; // [sp+30h] [bp-A8h]@4
  char v12; // [sp+31h] [bp-A7h]@4
  size_t Size; // [sp+B4h] [bp-24h]@4
  unsigned __int64 v14; // [sp+C0h] [bp-18h]@4
  unsigned int v15; // [sp+E8h] [bp+10h]@1
  _AVATOR_DATA *v16; // [sp+F0h] [bp+18h]@1
  _AVATOR_DATA *v17; // [sp+F8h] [bp+20h]@1

  v17 = pOldData;
  v16 = pNewData;
  v15 = dwAccSerial;
  v6 = &v10;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v14 = (unsigned __int64)&v10 ^ _security_cookie;
  Dest = 0;
  memset(&v12, 0, 0x7Fui64);
  sprintf(szQuery, "update [dbo].[tbl_PrimiumPlayTime] set ");
  LODWORD(Size) = strlen_0(szQuery);
  if ( v16->dbPlayTimeInPcbang.dwLastConnTime != v17->dbPlayTimeInPcbang.dwLastConnTime )
  {
    sprintf(&Dest, "[LastConnTime]= %d,", v16->dbPlayTimeInPcbang.dwLastConnTime);
    strcat_0(szQuery, &Dest);
  }
  if ( v16->dbPlayTimeInPcbang.dwContPlayTime != v17->dbPlayTimeInPcbang.dwContPlayTime )
  {
    sprintf(&Dest, "[ContPlayTime]= %d,", v16->dbPlayTimeInPcbang.dwContPlayTime);
    strcat_0(szQuery, &Dest);
  }
  if ( v16->dbPlayTimeInPcbang.bForcedClose != v17->dbPlayTimeInPcbang.bForcedClose )
  {
    sprintf(&Dest, "[ForcedClose]= %d,", v16->dbPlayTimeInPcbang.bForcedClose);
    strcat_0(szQuery, &Dest);
  }
  if ( v16->dbPlayTimeInPcbang.byReceiveCoupon != v17->dbPlayTimeInPcbang.byReceiveCoupon )
  {
    sprintf(&Dest, "[ReceiveCoupon]= %d,", v16->dbPlayTimeInPcbang.byReceiveCoupon);
    strcat_0(szQuery, &Dest);
  }
  if ( v16->dbPlayTimeInPcbang.byEnsureTime != v17->dbPlayTimeInPcbang.byEnsureTime )
  {
    sprintf(&Dest, "[EnsureTime]= %d ", v16->dbPlayTimeInPcbang.byEnsureTime);
    strcat_0(szQuery, &Dest);
  }
  v8 = strlen_0(szQuery);
  if ( v8 <= (unsigned int)Size )
  {
    memset_0(szQuery, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Dest, "where AccountSerial=%d", v15);
    szQuery[strlen_0(szQuery) - 1] = 32;
    strcat_0(szQuery, &Dest);
  }
  return 1;
}
