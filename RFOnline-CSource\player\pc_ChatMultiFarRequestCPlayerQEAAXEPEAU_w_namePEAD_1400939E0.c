/*
 * Function: ?pc_ChatMultiFarRequest@CPlayer@@QEAAXEPEAU_w_name@@PEAD@Z
 * Address: 0x1400939E0
 */

void __usercall CPlayer::pc_ChatMultiFarRequest(CPlayer *this@<rcx>, char by<PERSON><PERSON><PERSON><PERSON>@<dl>, _w_name *pDstName@<r8>, char *pwszMsg@<r9>, float a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v7; // rax@20
  unsigned int v8; // eax@20
  int v9; // eax@21
  char *v10; // rax@28
  char *v11; // rax@28
  char *v12; // rax@30
  char *v13; // rax@30
  CChatStealSystem *v14; // rax@31
  __int64 v15; // [sp+0h] [bp-3B8h]@1
  unsigned __int8 v16; // [sp+34h] [bp-384h]@4
  __int64 v17[5]; // [sp+58h] [bp-360h]@14
  int k; // [sp+84h] [bp-334h]@12
  int j; // [sp+88h] [bp-330h]@4
  CPlayer *v20; // [sp+90h] [bp-328h]@6
  char v21; // [sp+98h] [bp-320h]@6
  char Dest; // [sp+A8h] [bp-310h]@24
  char v23; // [sp+B9h] [bp-2FFh]@24
  char pbyType; // [sp+D4h] [bp-2E4h]@24
  char v25; // [sp+D5h] [bp-2E3h]@24
  _chat_multi_far_trans_zocl v26; // [sp+100h] [bp-2B8h]@28
  int v27; // [sp+304h] [bp-B4h]@28
  char *v28; // [sp+308h] [bp-B0h]@28
  unsigned __int8 Src; // [sp+314h] [bp-A4h]@28
  int l; // [sp+324h] [bp-94h]@28
  unsigned __int8 v31; // [sp+334h] [bp-84h]@30
  unsigned __int8 v32; // [sp+354h] [bp-64h]@31
  char v33; // [sp+374h] [bp-44h]@31
  char v34; // [sp+375h] [bp-43h]@31
  int v35; // [sp+384h] [bp-34h]@31
  int v36; // [sp+390h] [bp-28h]@20
  int v37; // [sp+394h] [bp-24h]@21
  size_t Size; // [sp+398h] [bp-20h]@28
  size_t v39; // [sp+3A0h] [bp-18h]@30
  unsigned __int64 v40; // [sp+3A8h] [bp-10h]@4
  CPlayer *pPlayer; // [sp+3C0h] [bp+8h]@1
  char v42; // [sp+3C8h] [bp+10h]@1
  _w_name *v43; // [sp+3D0h] [bp+18h]@1
  const char *Str; // [sp+3D8h] [bp+20h]@1

  Str = pwszMsg;
  v43 = pDstName;
  v42 = byDstNum;
  pPlayer = this;
  v5 = &v15;
  for ( i = 236i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v40 = (unsigned __int64)&v15 ^ _security_cookie;
  v16 = 0;
  for ( j = 0; j < (unsigned __int8)v42; ++j )
  {
    v20 = CPlayer::FindFarChatPlayerWithTemp(pPlayer, v43[j].name);
    v21 = 0;
    if ( v20 )
    {
      if ( v20->m_bBlockWhisper )
      {
        v21 = 2;
      }
      else if ( v20 == pPlayer )
      {
        v21 = 4;
      }
      else
      {
        for ( k = 0; k < v16; ++k )
        {
          if ( (CPlayer *)v17[k] == v20 )
          {
            v21 = 5;
            goto $RESULT_3;
          }
        }
        if ( pPlayer->m_byUserDgr != 2 && v20->m_byUserDgr != 2 )
        {
          _effect_parameter::GetEff_Have(&v20->m_EP, 3);
          if ( a5 <= 0.0 )
          {
            v36 = CPlayerDB::GetRaceCode(&v20->m_Param);
            v7 = CPvpUserAndGuildRankingSystem::Instance();
            v8 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v7, v36, 0);
            if ( v8 != v20->m_dwObjSerial )
            {
              v37 = CPlayerDB::GetRaceCode(&v20->m_Param);
              v9 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
              if ( v37 != v9 )
                v21 = 3;
            }
          }
        }
      }
    }
    else
    {
      v21 = 1;
    }
$RESULT_3:
    if ( v21 )
    {
      v23 = v21;
      strcpy_0(&Dest, v43[j].name);
      pbyType = 2;
      v25 = 103;
      CNetProcess::LoadSendMsg(unk_1414F2088, pPlayer->m_ObjID.m_wIndex, &pbyType, &Dest, 0x12u);
    }
    else
    {
      v17[(unsigned __int64)v16++] = (__int64)v20;
    }
  }
  if ( (signed int)v16 > 0 )
  {
    _chat_multi_far_trans_zocl::_chat_multi_far_trans_zocl(&v26);
    v27 = 0;
    v28 = v26.sData;
    v10 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
    Src = strlen_0(v10);
    memcpy_0(&v28[v27++], &Src, 1ui64);
    Size = Src;
    v11 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
    memcpy_0(&v28[v27], v11, Size);
    v27 += Src;
    memcpy_0(&v28[v27++], &v16, 1ui64);
    for ( l = 0; l < v16; ++l )
    {
      v12 = CPlayerDB::GetCharNameW((CPlayerDB *)(v17[l] + 1952));
      v31 = strlen_0(v12);
      memcpy_0(&v28[v27++], &v31, 1ui64);
      v39 = v31;
      v13 = CPlayerDB::GetCharNameW((CPlayerDB *)(v17[l] + 1952));
      memcpy_0(&v28[v27], v13, v39);
      v27 += v31;
    }
    v32 = strlen_0(Str) + 1;
    memcpy_0(&v28[v27++], &v32, 1ui64);
    memcpy_0(&v28[v27], Str, v32);
    v27 += v32;
    v26.wSize = v27;
    v33 = 2;
    v34 = 102;
    v14 = CChatStealSystem::Instance();
    CChatStealSystem::StealChatMsg(v14, pPlayer, 9, (char *)Str);
    v35 = _chat_multi_far_trans_zocl::size(&v26);
    for ( l = 0; l < v16; ++l )
      CNetProcess::LoadSendMsg(unk_1414F2088, *(_WORD *)(v17[l] + 18), &v33, (char *)&v26, v35);
    CNetProcess::LoadSendMsg(unk_1414F2088, pPlayer->m_ObjID.m_wIndex, &v33, (char *)&v26, v35);
  }
}
