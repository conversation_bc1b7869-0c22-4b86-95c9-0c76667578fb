/*
 * Function: j_?SetSkill@CMonsterSkill@@QEAAHPEAU_monster_fld@@PEAU_monster_sp_fld@@HHPEAU_skill_fld@@KMKHHH@Z
 * Address: 0x14000ABA0
 */

int __fastcall CMonsterSkill::SetSkill(CMonsterSkill *this, _monster_fld *pMonsterFld, _monster_sp_fld *pSPCont, int nSFLv, int nEffectType, _skill_fld *pSkillFld, unsigned int dwDelayTime, float fAttackDist, unsigned int dwCastDelay, int nMotive, int nMotiveValue, int skillDestType)
{
  return CMonsterSkill::SetSkill(
           this,
           pMonsterFld,
           pSPCont,
           nSFLv,
           nEffectType,
           pSkillFld,
           dwDelayTime,
           fAttackDist,
           dwCastDelay,
           nMotive,
           nMotiveValue,
           skillDestType);
}
