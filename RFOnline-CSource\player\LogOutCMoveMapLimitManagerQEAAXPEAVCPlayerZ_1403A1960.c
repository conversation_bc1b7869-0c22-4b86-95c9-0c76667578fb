/*
 * Function: ?LogOut@CMoveMapLimitManager@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403A1960
 */

void __fastcall CMoveMapLimitManager::LogOut(CMoveMapLimitManager *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CMoveMapLimitRightInfo *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMoveMapLimitManager *v6; // [sp+30h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+38h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CMoveMapLimitRightInfoList::LogOut(&v6->m_kRightInfo, pkPlayer);
  v4 = CMoveMapLimitRightInfoList::Get(&v6->m_kRightInfo, pkPlayera->m_ObjID.m_wIndex);
  CMoveMapLimitInfoList::LogOut(&v6->m_kLimitInfo, pkPlayera, v4);
}
