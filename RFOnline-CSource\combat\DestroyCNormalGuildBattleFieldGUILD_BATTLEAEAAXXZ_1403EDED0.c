/*
 * Function: ?Des<PERSON>y@CNormalGuildBattleField@GUILD_BATTLE@@AEAAXXZ
 * Address: 0x1403EDED0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleField::Destroy(GUILD_BATTLE::CNormalGuildBattleField *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@11
  __int64 v4; // rax@19
  __int64 v5; // rax@27
  __int64 v6; // rax@34
  __int64 v7; // [sp+0h] [bp-C8h]@1
  void *v8; // [sp+20h] [bp-A8h]@5
  void *v9; // [sp+28h] [bp-A0h]@7
  CCircleZone *v10; // [sp+30h] [bp-98h]@9
  CCircleZone *v11; // [sp+38h] [bp-90h]@9
  CCircleZone *v12; // [sp+40h] [bp-88h]@9
  CCircleZone *v13; // [sp+48h] [bp-80h]@17
  CCircleZone *v14; // [sp+50h] [bp-78h]@17
  CCircleZone *v15; // [sp+58h] [bp-70h]@17
  CGravityStoneRegener *v16; // [sp+60h] [bp-68h]@25
  CGravityStoneRegener *v17; // [sp+68h] [bp-60h]@25
  CGravityStoneRegener *v18; // [sp+70h] [bp-58h]@25
  CGravityStone *v19; // [sp+78h] [bp-50h]@33
  CGravityStone *v20; // [sp+80h] [bp-48h]@33
  __int64 v21; // [sp+88h] [bp-40h]@11
  __int64 v22; // [sp+90h] [bp-38h]@13
  __int64 v23; // [sp+98h] [bp-30h]@19
  __int64 v24; // [sp+A0h] [bp-28h]@21
  __int64 v25; // [sp+A8h] [bp-20h]@27
  __int64 v26; // [sp+B0h] [bp-18h]@29
  __int64 v27; // [sp+B8h] [bp-10h]@34
  GUILD_BATTLE::CNormalGuildBattleField *v28; // [sp+D0h] [bp+8h]@1

  v28 = this;
  v1 = &v7;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v28->m_bInit = 0;
  if ( v28->m_pkStartPos[0] )
  {
    v8 = v28->m_pkStartPos[0];
    operator delete(v8);
    v28->m_pkStartPos[0] = 0i64;
  }
  if ( v28->m_pkStartPos[1] )
  {
    v9 = v28->m_pkStartPos[1];
    operator delete(v9);
    v28->m_pkStartPos[1] = 0i64;
  }
  v28->m_ui1PGoalPosCnt = 0;
  if ( v28->m_pk1PGoalZone )
  {
    v12 = v28->m_pk1PGoalZone;
    v11 = v12;
    v10 = v12;
    if ( v12 )
    {
      if ( LODWORD(v10[-1].m_pkGoalPos) )
      {
        LODWORD(v3) = ((int (__fastcall *)(CCircleZone *, signed __int64))v11->vfptr->__vecDelDtor)(v11, 3i64);
        v21 = v3;
      }
      else
      {
        operator delete[](&v10[-1].m_pkGoalPos);
        v21 = 0i64;
      }
      v22 = v21;
    }
    else
    {
      v22 = 0i64;
    }
    v28->m_pk1PGoalZone = 0i64;
  }
  v28->m_ui2PGoalPosCnt = 0;
  if ( v28->m_pk2PGoalZone )
  {
    v15 = v28->m_pk2PGoalZone;
    v14 = v15;
    v13 = v15;
    if ( v15 )
    {
      if ( LODWORD(v13[-1].m_pkGoalPos) )
      {
        LODWORD(v4) = ((int (__fastcall *)(CCircleZone *, signed __int64))v14->vfptr->__vecDelDtor)(v14, 3i64);
        v23 = v4;
      }
      else
      {
        operator delete[](&v13[-1].m_pkGoalPos);
        v23 = 0i64;
      }
      v24 = v23;
    }
    else
    {
      v24 = 0i64;
    }
    v28->m_pk2PGoalZone = 0i64;
  }
  v28->m_uiRegenPosCnt = 0;
  if ( v28->m_pkRegenPos )
  {
    v18 = v28->m_pkRegenPos;
    v17 = v18;
    v16 = v18;
    if ( v18 )
    {
      if ( LODWORD(v16[-1].m_pkRegenPos) )
      {
        LODWORD(v5) = ((int (__fastcall *)(CGravityStoneRegener *, signed __int64))v17->vfptr->__vecDelDtor)(v17, 3i64);
        v25 = v5;
      }
      else
      {
        operator delete[](&v16[-1].m_pkRegenPos);
        v25 = 0i64;
      }
      v26 = v25;
    }
    else
    {
      v26 = 0i64;
    }
    v28->m_pkRegenPos = 0i64;
  }
  if ( v28->m_pkBall )
  {
    v20 = v28->m_pkBall;
    v19 = v20;
    if ( v20 )
    {
      LODWORD(v6) = ((int (__fastcall *)(CGravityStone *, signed __int64))v19->vfptr->__vecDelDtor)(v19, 1i64);
      v27 = v6;
    }
    else
    {
      v27 = 0i64;
    }
    v28->m_pkBall = 0i64;
  }
}
