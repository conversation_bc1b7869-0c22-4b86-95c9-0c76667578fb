# NexusPro Development Guide

## Project Overview
NexusPro is a comprehensive DLL hooking framework designed to enhance the RF Online zone server through bug fixes, security improvements, and gameplay enhancements.

## Architecture

### Core Components
- **HookManager**: Manages all function hooks and their lifecycle
- **MemoryPatcher**: Handles memory patching and code modification
- **ConfigManager**: Manages configuration files and runtime settings
- **AddressResolver**: Resolves function addresses and patterns
- **Logger**: Comprehensive logging system

### Module Structure
```
NexusPro/
├── Core/                   # Core hooking infrastructure
│   ├── HookManager.*       # Main hook management
│   ├── MemoryPatcher.*     # Memory patching utilities
│   └── ConfigManager.*     # Configuration management
├── Modules/               # Enhancement modules
│   ├── Authentication/    # Login and security hooks
│   ├── Network/          # Network packet hooks
│   ├── BugFix/           # Bug fix implementations
│   ├── Enhancement/      # Gameplay enhancements
│   └── Monitoring/       # Server monitoring
├── Utils/                # Utility classes
│   ├── Logger.*          # Logging system
│   ├── AddressResolver.* # Address resolution
│   └── Common.h          # Common definitions
└── Interface/            # External interfaces
    ├── AdminAPI.*        # Admin control API
    └── WebInterface.*    # Web-based interface
```

## Development Guidelines

### Code Style
- Use C++17 features
- Follow RAII principles
- Use smart pointers for memory management
- Prefer const-correctness
- Use meaningful variable and function names

### Error Handling
- Always check return values
- Use exceptions for exceptional cases only
- Log errors with appropriate detail level
- Provide fallback mechanisms where possible

### Threading
- Use std::mutex for thread synchronization
- Prefer std::atomic for simple shared data
- Use thread-safe logging throughout
- Avoid blocking operations in hook functions

### Memory Management
- Always validate pointers before use
- Use RAII for resource management
- Check memory protection before writing
- Backup original bytes before patching

## Hook Implementation

### Basic Hook Pattern
```cpp
// 1. Declare original function pointer
RFTypes::SomeFunc originalSomeFunc_;

// 2. Implement hook function
BOOL __fastcall Hook_SomeFunction(void* thisPtr, int param) {
    // Pre-processing
    NEXUS_DEBUG(L"SomeFunction called");
    
    // Call original function
    BOOL result = originalSomeFunc_(thisPtr, param);
    
    // Post-processing
    if (result) {
        // Handle success
    }
    
    return result;
}

// 3. Install hook
bool success = InstallSingleHook(
    L"SomeFunction",
    rfAddresses_.SomeFunction,
    Hook_SomeFunction,
    originalSomeFunc_,
    ModuleType::Enhancement
);
```

### Address Resolution
```cpp
// Pattern-based resolution
LPVOID address = addressResolver_->GetAddressByPattern(
    {0x48, 0x89, 0x5C, 0x24, 0x08},  // Pattern bytes
    "xxxxx"                           // Mask
);

// Export-based resolution
LPVOID address = addressResolver_->GetFunctionAddress("FunctionName");
```

### Memory Patching
```cpp
// Simple byte patch
memoryPatcher_->PatchBytes(
    L"BugFix_SomeIssue",
    targetAddress,
    {0x90, 0x90, 0x90},  // NOP instructions
    L"Fix for some issue"
);

// Jump patch
memoryPatcher_->CreateJumpPatch(
    L"Redirect_SomeFunction",
    originalAddress,
    newFunctionAddress,
    L"Redirect to enhanced function"
);
```

## Configuration System

### Adding New Configuration Options
1. Add to appropriate config structure in ConfigManager.h
2. Implement getter/setter in ConfigManager.cpp
3. Add default values to nexuspro.ini
4. Document the option

### Runtime Configuration Changes
```cpp
// Get current config
auto config = configManager->GetCoreConfig();

// Modify settings
config.enableDebugMode = true;

// Apply changes
configManager->SetCoreConfig(config);
```

## Logging Best Practices

### Log Levels
- **Trace**: Very detailed execution flow
- **Debug**: Development and debugging information
- **Info**: General information about operations
- **Warning**: Potential issues that don't prevent operation
- **Error**: Errors that prevent specific operations
- **Critical**: Errors that may cause system instability

### Logging Examples
```cpp
// Function entry/exit (debug builds only)
NEXUS_FUNCTION_LOG();

// Information logging
NEXUS_INFO(L"Hook installed successfully: " + hookName);

// Error logging with context
NEXUS_ERROR(L"Failed to resolve address for: " + functionName + 
           L" Error: " + Utils::GetLastErrorString());

// Formatted logging
Logger::InfoFormat(L"Player %d logged in from IP: %s", playerId, ipAddress);
```

## Testing

### Unit Testing
- Test each component in isolation
- Mock external dependencies
- Verify error handling paths
- Test edge cases and boundary conditions

### Integration Testing
- Test hook installation/uninstallation
- Verify configuration loading/saving
- Test address resolution accuracy
- Validate memory patching operations

### Performance Testing
- Measure hook overhead
- Test under high load conditions
- Monitor memory usage
- Verify thread safety

## Debugging

### Debug Builds
- Enable console output in configuration
- Use NEXUS_FUNCTION_LOG() for call tracing
- Set log level to Debug or Trace
- Enable debug mode in configuration

### Release Builds
- Minimize logging overhead
- Use Info level or higher
- Disable console output
- Optimize for performance

### Common Issues
1. **Hook Installation Fails**
   - Verify target address is correct
   - Check memory protection
   - Ensure pattern matching is accurate

2. **Crashes After Hook**
   - Verify calling convention matches
   - Check parameter types and order
   - Ensure original function is called correctly

3. **Address Resolution Fails**
   - Update patterns for new server version
   - Verify module is loaded
   - Check pattern uniqueness

## Security Considerations

### Anti-Detection
- Minimize memory footprint
- Use legitimate API calls
- Avoid suspicious patterns
- Implement proper cleanup

### Data Protection
- Encrypt sensitive configuration data
- Validate all input parameters
- Sanitize log output
- Protect against buffer overflows

## Performance Optimization

### Hook Performance
- Keep hook functions lightweight
- Avoid blocking operations
- Use efficient data structures
- Cache frequently accessed data

### Memory Usage
- Use memory pools for frequent allocations
- Implement proper cleanup
- Monitor for memory leaks
- Optimize data structures

## Deployment

### Build Configuration
- Use Release configuration for production
- Enable optimizations
- Strip debug symbols
- Minimize dependencies

### Installation
1. Copy NexusPro.dll to server directory
2. Copy nexuspro.ini configuration file
3. Ensure proper file permissions
4. Test in safe environment first

### Updates
- Implement version checking
- Support hot-swapping modules
- Provide rollback mechanism
- Test thoroughly before deployment
