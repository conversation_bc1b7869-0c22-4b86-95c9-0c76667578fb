/*
 * Function: ?_db_Update_Start_NpcQuest_History@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0@Z
 * Address: 0x1401ADF60
 */

char __fastcall CMainThread::_db_Update_Start_NpcQuest_History(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY *v6; // rdi@7
  signed __int64 v7; // rsi@7
  __int64 v9; // [sp+0h] [bp-108h]@1
  char *szTime; // [sp+20h] [bp-E8h]@7
  __int64 nEndTime; // [sp+28h] [bp-E0h]@7
  int v12; // [sp+30h] [bp-D8h]@7
  int v13; // [sp+38h] [bp-D0h]@7
  int v14; // [sp+40h] [bp-C8h]@7
  int v15; // [sp+48h] [bp-C0h]@7
  unsigned int v16; // [sp+50h] [bp-B8h]@5
  unsigned int v17; // [sp+54h] [bp-B4h]@5
  char DstBuf; // [sp+68h] [bp-A0h]@5
  char v19; // [sp+69h] [bp-9Fh]@5
  unsigned __int16 Dst; // [sp+A8h] [bp-60h]@7
  unsigned __int16 v21; // [sp+AAh] [bp-5Eh]@7
  unsigned __int16 v22; // [sp+AEh] [bp-5Ah]@7
  unsigned __int16 v23; // [sp+B0h] [bp-58h]@7
  unsigned __int16 v24; // [sp+B2h] [bp-56h]@7
  unsigned __int16 v25; // [sp+B4h] [bp-54h]@7
  unsigned __int16 v26; // [sp+B6h] [bp-52h]@7
  unsigned int v27; // [sp+C4h] [bp-44h]@5
  char v28; // [sp+D0h] [bp-38h]@7
  unsigned __int64 v29; // [sp+E0h] [bp-28h]@4
  CMainThread *v30; // [sp+110h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+118h] [bp+10h]@1
  _AVATOR_DATA *v32; // [sp+120h] [bp+18h]@1

  v32 = pNewData;
  dwSeriala = dwSerial;
  v30 = this;
  v4 = &v9;
  for ( i = 62i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v29 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( pNewData->dbQuest.dwListCnt > pOldData->dbQuest.dwListCnt )
  {
    v16 = pNewData->dbQuest.dwListCnt - pOldData->dbQuest.dwListCnt;
    v17 = pOldData->dbQuest.dwListCnt;
    DstBuf = 0;
    memset(&v19, 0, 0x18ui64);
    v27 = 0;
    while ( v27 < v16 )
    {
      qmemcpy(&v28, &v32->dbQuest.m_StartHistory[v17].tmStartTime, 0x10ui64);
      qmemcpy(&Dst, &v28, 0x10ui64);
      v15 = v26;
      v14 = v25;
      v13 = v24;
      v12 = v23;
      LODWORD(nEndTime) = v22;
      LODWORD(szTime) = v21;
      sprintf_s(&DstBuf, 0x19ui64, "%04d-%02d-%02d %02d:%02d:%02d.%d", Dst);
      v6 = v32->dbQuest.m_StartHistory;
      v7 = (signed __int64)&v32->dbQuest.m_StartHistory[v17];
      nEndTime = *(_QWORD *)(v7 + 81);
      szTime = &DstBuf;
      if ( !CRFWorldDatabase::Insert_Start_NpcQuest_History(
              v30->m_pWorldDB,
              dwSeriala,
              (char *)v7,
              v6[v17].byLevel,
              &DstBuf,
              nEndTime) )
        return 0;
      memset_0(&DstBuf, 0, 0x19ui64);
      memset_0(&Dst, 0, 0x10ui64);
      ++v27;
      ++v17;
    }
  }
  return 1;
}
