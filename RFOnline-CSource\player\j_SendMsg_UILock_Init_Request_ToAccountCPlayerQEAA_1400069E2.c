/*
 * Function: j_?SendMsg_UILock_Init_Request_ToAccount@CPlayer@@QEAAXKPEADGE0@Z
 * Address: 0x1400069E2
 */

void __fastcall CPlayer::SendMsg_UILock_Init_Request_ToAccount(CPlayer *this, unsigned int dwSerial, char *uszUILockPW, unsigned __int16 wUserIndex, char byHintIndex, char *uszHintAnswer)
{
  CPlayer::SendMsg_UILock_Init_Request_ToAccount(this, dwSerial, uszUILockPW, wUserIndex, byHintIndex, uszHintAnswer);
}
