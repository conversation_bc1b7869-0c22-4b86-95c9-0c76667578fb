/*
 * Function: ?OnPlayerCreateCompleteProc@ItemCombineMgr@@QEAAXXZ
 * Address: 0x1402AB950
 */

void __fastcall ItemCombineMgr::OnPlayerCreateCompleteProc(ItemCombineMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-188h]@1
  _combine_ex_item_result_zocl pLoadData; // [sp+30h] [bp-158h]@4
  ItemCombineMgr *v5; // [sp+190h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _combine_ex_item_result_zocl::_combine_ex_item_result_zocl(&pLoadData);
  if ( !ItemCombineMgr::LoadDB_CombineResult(v5, &pLoadData) )
    CPlayer::SendMsg_CombineItemExResult(v5->m_pMaster, &pLoadData);
}
