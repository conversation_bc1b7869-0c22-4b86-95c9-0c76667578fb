/*
 * Function: ?pc_BuddyAddRequest@CPlayer@@QEAAXGKPEAD@Z
 * Address: 0x14008F4E0
 */

void __fastcall CPlayer::pc_BuddyAddRequest(CPlayer *this, unsigned __int16 wDstIndex, unsigned int dwDstSerial, char *pwszDstName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@19
  char *v7; // rax@27
  __int64 v8; // [sp+0h] [bp-48h]@1
  char v9; // [sp+20h] [bp-28h]@4
  CPlayer *v10; // [sp+28h] [bp-20h]@4
  unsigned int dwDstSeriala; // [sp+30h] [bp-18h]@4
  int v12; // [sp+34h] [bp-14h]@19
  CPlayer *v13; // [sp+50h] [bp+8h]@1
  unsigned __int16 v14; // [sp+58h] [bp+10h]@1
  unsigned int v15; // [sp+60h] [bp+18h]@1
  char *pwszName; // [sp+68h] [bp+20h]@1

  pwszName = pwszDstName;
  v15 = dwDstSerial;
  v14 = wDstIndex;
  v13 = this;
  v4 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = 0;
  v10 = 0i64;
  dwDstSeriala = -1;
  if ( !_BUDDY_LIST::GetEmptyData(&v13->m_pmBuddy) )
  {
    v9 = 1;
    goto $RESULT_0;
  }
  if ( _BUDDY_LIST::IsBuddy(&v13->m_pmBuddy, v14) )
  {
    v9 = 2;
    goto $RESULT_0;
  }
  if ( v14 == 0xFFFF )
  {
    v10 = GetPtrPlayerFromName(&g_Player, 2532, pwszName);
    if ( !v10 )
    {
      v9 = 3;
      goto $RESULT_0;
    }
    if ( !v10->m_bLive )
    {
      v9 = 3;
      goto $RESULT_0;
    }
    dwDstSeriala = v10->m_dwObjSerial;
  }
  else
  {
    v10 = &g_Player + v14;
    if ( !v10->m_bLive )
    {
      v9 = 3;
      goto $RESULT_0;
    }
    if ( v10->m_dwObjSerial != v15 )
    {
      v9 = 3;
      goto $RESULT_0;
    }
    dwDstSeriala = v15;
  }
  v12 = CPlayerDB::GetRaceCode(&v10->m_Param);
  v6 = CPlayerDB::GetRaceCode(&v13->m_Param);
  if ( v12 == v6 )
  {
    if ( v10 == v13 )
    {
      v9 = 3;
    }
    else if ( !_BUDDY_LIST::GetEmptyData(&v10->m_pmBuddy) && !_BUDDY_LIST::IsBuddy(&v10->m_pmBuddy, v13->m_dwObjSerial) )
    {
      v9 = 4;
    }
  }
  else
  {
    v9 = 7;
  }
$RESULT_0:
  if ( v9 )
  {
    CPlayer::SendMsg_BuddyAddFail(v13, v9, pwszName);
  }
  else
  {
    _BUDDY_LIST::PushLastApplyTemp(&v13->m_pmBuddy, dwDstSeriala);
    v7 = CPlayerDB::GetCharNameW(&v13->m_Param);
    CPlayer::SendMsg_BuddyAddAsk(v10, v13->m_ObjID.m_wIndex, v13->m_dwObjSerial, v7);
  }
}
