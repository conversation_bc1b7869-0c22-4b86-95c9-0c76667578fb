/*
 * Function: ?Enter@CNormalGuildBattleStateInBattle@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F0A00
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateInBattle::Enter(GUILD_BATTLE::CNormalGuildBattleStateInBattle *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@4
  unsigned int v5; // eax@4
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v6; // rax@4
  __int64 v8; // [sp+0h] [bp-58h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v9; // [sp+20h] [bp-38h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v10; // [sp+28h] [bp-30h]@4
  GUILD_BATTLE::CNormalGuildBattleField *v11; // [sp+30h] [bp-28h]@4
  GUILD_BATTLE::CNormalGuildBattleField *pkField; // [sp+38h] [bp-20h]@4
  GUILD_BATTLE::CNormalGuildBattleField *v13; // [sp+40h] [bp-18h]@4
  unsigned int uiMapID; // [sp+48h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattleStateInBattle *v15; // [sp+60h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *pkBattlea; // [sp+68h] [bp+10h]@1

  pkBattlea = pkBattle;
  v15 = this;
  v2 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = GUILD_BATTLE::CNormalGuildBattle::GetRed(pkBattle);
  v10 = GUILD_BATTLE::CNormalGuildBattle::GetBlue(pkBattlea);
  v11 = GUILD_BATTLE::CNormalGuildBattle::GetField(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleField::CreateFieldObject(v11);
  pkField = GUILD_BATTLE::CNormalGuildBattle::GetField(pkBattlea);
  v4 = GUILD_BATTLE::CNormalGuildBattle::GetID(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleGuild::MoveMap(v9, v4, pkField);
  v13 = GUILD_BATTLE::CNormalGuildBattle::GetField(pkBattlea);
  v5 = GUILD_BATTLE::CNormalGuildBattle::GetID(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleGuild::MoveMap(v10, v5, v13);
  GUILD_BATTLE::CGuildBattleStateList::Clear((GUILD_BATTLE::CGuildBattleStateList *)&v15->m_kRountStateList.vfptr);
  GUILD_BATTLE::CGuildBattleStateList::SetReady((GUILD_BATTLE::CGuildBattleStateList *)&v15->m_kRountStateList.vfptr);
  GUILD_BATTLE::CGuildBattleStateList::Next((GUILD_BATTLE::CGuildBattleStateList *)&v15->m_kRountStateList.vfptr, 0);
  uiMapID = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v11);
  v6 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
  GUILD_BATTLE::CCurrentGuildBattleInfoManager::Set(v6, uiMapID, pkBattlea);
  GUILD_BATTLE::CNormalGuildBattle::SendWebBattleStartInfo(pkBattlea);
  GUILD_BATTLE::CNormalGuildBattleState::Log(
    (GUILD_BATTLE::CNormalGuildBattleState *)&v15->vfptr,
    pkBattlea,
    "Enter : Start");
  return 0i64;
}
