/*
 * Function: ?LeaveGuild@CNormalGuildBattle@GUILD_BATTLE@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x1403E6070
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::LeaveGuild(GUILD_BATTLE::CNormalGuildBattle *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@16
  char *v6; // rax@32
  __int64 v7; // [sp+0h] [bp-78h]@1
  char *v8; // [sp+20h] [bp-58h]@16
  char *v9; // [sp+28h] [bp-50h]@16
  const char *v10; // [sp+30h] [bp-48h]@32
  char v11; // [sp+40h] [bp-38h]@4
  char v12; // [sp+41h] [bp-37h]@4
  unsigned int dwSerial; // [sp+44h] [bp-34h]@4
  char v14; // [sp+48h] [bp-30h]@14
  char v15; // [sp+49h] [bp-2Fh]@17
  int v16; // [sp+4Ch] [bp-2Ch]@16
  char *v17; // [sp+50h] [bp-28h]@16
  const char *v18; // [sp+58h] [bp-20h]@24
  char *v19; // [sp+60h] [bp-18h]@27
  const char *v20; // [sp+68h] [bp-10h]@30
  GUILD_BATTLE::CNormalGuildBattle *v21; // [sp+80h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+88h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v21 = this;
  v2 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = 1;
  v12 = 0;
  dwSerial = pkPlayer->m_dwObjSerial;
  if ( !GUILD_BATTLE::CNormalGuildBattleGuild::IsMember(&v21->m_k1P, dwSerial) )
  {
    if ( !GUILD_BATTLE::CNormalGuildBattleGuild::IsMember(&v21->m_k2P, dwSerial) )
      return -111;
    v11 = 0;
  }
  if ( pkPlayera->m_bInGuildBattle )
  {
    if ( pkPlayera->m_bTakeGravityStone )
    {
      v14 = GUILD_BATTLE::CNormalGuildBattleField::DropBall(v21->m_pkField, pkPlayera);
      if ( v14 )
      {
        v16 = (unsigned __int8)v14;
        v17 = CPlayerDB::GetCharNameW(&pkPlayera->m_Param);
        v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        LODWORD(v9) = v16;
        v8 = v17;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v5,
          "CNormalGuildBattle::LeaveGuild( %u ) : (%u) m_pkField->DropBall( %s ) Fail(%u)!",
          dwSerial,
          dwSerial);
      }
      else
      {
        GUILD_BATTLE::CNormalGuildBattle::NotifyDestoryBall(v21, pkPlayera->m_dwObjSerial);
        v12 = 1;
      }
    }
    v15 = 1;
    if ( v11 )
    {
      GUILD_BATTLE::CNormalGuildBattleGuild::LeaveGuild(
        &v21->m_k1P,
        dwSerial,
        pkPlayera->m_bInGuildBattle,
        &v21->m_kLogger);
      if ( GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v21->m_k1P) == 1 )
        v15 = 0;
    }
    else
    {
      GUILD_BATTLE::CNormalGuildBattleGuild::LeaveGuild(
        &v21->m_k2P,
        dwSerial,
        pkPlayera->m_bInGuildBattle,
        &v21->m_kLogger);
      if ( GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v21->m_k2P) == 1 )
        v15 = 0;
    }
    CPlayer::pc_SetInGuildBattle(pkPlayera, 0, -1);
    if ( v12 )
      v18 = "Drop Ball";
    else
      v18 = "None";
    if ( v11 )
      v19 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v21->m_k1P);
    else
      v19 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v21->m_k2P);
    if ( v15 )
      v20 = "Left";
    else
      v20 = "Right";
    v6 = CPlayerDB::GetCharNameW(&pkPlayera->m_Param);
    v10 = v18;
    v9 = v6;
    v8 = v19;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      &v21->m_kLogger,
      "CNormalGuildBattle::LeaveGuild( %u ) : %s %s %s %s",
      dwSerial,
      v20);
    result = 0;
  }
  else
  {
    if ( v11 )
      GUILD_BATTLE::CNormalGuildBattleGuild::LeaveGuild(
        &v21->m_k1P,
        dwSerial,
        pkPlayera->m_bInGuildBattle,
        &v21->m_kLogger);
    else
      GUILD_BATTLE::CNormalGuildBattleGuild::LeaveGuild(
        &v21->m_k2P,
        dwSerial,
        pkPlayera->m_bInGuildBattle,
        &v21->m_kLogger);
    result = 0;
  }
  return result;
}
