/*
 * Function: ?Check_Loaded_Event_Status@CashItemRemoteStore@@QEAAXE@Z
 * Address: 0x1402F8140
 */

void __fastcall CashItemRemoteStore::Check_Loaded_Event_Status(CashItemRemoteStore *this, char byEventType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // ebp@23
  unsigned int v5; // eax@23
  __int64 v6; // [sp+0h] [bp-138h]@1
  int v7; // [sp+20h] [bp-118h]@23
  int v8; // [sp+28h] [bp-110h]@23
  int v9; // [sp+30h] [bp-108h]@23
  int v10; // [sp+38h] [bp-100h]@23
  int v11; // [sp+40h] [bp-F8h]@23
  int v12; // [sp+48h] [bp-F0h]@23
  int v13; // [sp+50h] [bp-E8h]@23
  int v14; // [sp+58h] [bp-E0h]@23
  int v15; // [sp+60h] [bp-D8h]@23
  int v16; // [sp+68h] [bp-D0h]@23
  __time32_t Time; // [sp+74h] [bp-C4h]@6
  int v18; // [sp+84h] [bp-B4h]@10
  char szEventName; // [sp+A0h] [bp-98h]@23
  CLogFile *v20; // [sp+F0h] [bp-48h]@23
  unsigned __int64 v21; // [sp+F8h] [bp-40h]@4
  CashItemRemoteStore *v22; // [sp+140h] [bp+8h]@1
  char v23; // [sp+148h] [bp+10h]@1

  v23 = byEventType;
  v22 = this;
  v2 = &v6;
  for ( i = 66i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v21 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( v22->m_cash_event[(unsigned __int8)byEventType].m_ini.m_bUseCashEvent )
  {
    _time32(&Time);
    if ( Time <= v22->m_cash_event[(unsigned __int8)v23].m_ini.m_EventTime[1] )
    {
      if ( Time < v22->m_cash_event[(unsigned __int8)v23].m_ini.m_EventTime[0]
        || Time > v22->m_cash_event[(unsigned __int8)v23].m_ini.m_EventTime[1] )
      {
        if ( Time < v22->m_cash_event[(unsigned __int8)v23].m_ini.m_EventTime[0] )
        {
          CashItemRemoteStore::Set_CashEvent_Status(v22, v23, 1);
          Get_CashEvent_Name(v23, &szEventName);
          v4 = v22->m_cash_event[(unsigned __int8)v23].m_event_status;
          v16 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byMinute[1];
          v15 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byHour[1];
          v14 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byDay[1];
          v13 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byMonth[1];
          v12 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_wYear[1];
          v11 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byMinute[0];
          v10 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byHour[0];
          v9 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byDay[0];
          v8 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byMonth[0];
          v7 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_wYear[0];
          CLogFile::Write(
            &v22->m_cash_event[(unsigned __int8)v23].m_event_log,
            "[ %s CashEvent Loaded] [EventState : %d] [EventTime : %d/%d/%d %d:%d  ~ %d/%d/%d %d:%d ]",
            &szEventName,
            v4);
          v5 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byRepeatDay;
          v20 = &v22->m_cash_event[(unsigned __int8)v23].m_event_log;
          CLogFile::Write(
            v20,
            "Repeat Information >> Repeat:%d , RepeatDay:%d",
            v22->m_cash_event[(unsigned __int8)v23].m_ini.m_bRepeat,
            v5);
          v9 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byMinute[2];
          v8 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byHour[2];
          v7 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byDay[2];
          CLogFile::Write(
            &v22->m_cash_event[(unsigned __int8)v23].m_event_log,
            "Expire Date >> [%d/%d/%d %d:%d]",
            v22->m_cash_event[(unsigned __int8)v23].m_ini.m_wYear[2],
            v22->m_cash_event[(unsigned __int8)v23].m_ini.m_byMonth[2]);
        }
      }
      else
      {
        v18 = v22->m_cash_event[(unsigned __int8)v23].m_ini.m_EventTime[1] - Time;
        if ( v18 > 0 )
        {
          if ( v18 <= 0 || v18 > v22->m_cash_event[(unsigned __int8)v23].m_event_inform_before[1] )
          {
            if ( v18 <= v22->m_cash_event[(unsigned __int8)v23].m_event_inform_before[1]
              || v18 > v22->m_cash_event[(unsigned __int8)v23].m_event_inform_before[0] )
            {
              if ( v18 <= v22->m_cash_event[(unsigned __int8)v23].m_event_inform_before[0] )
                CashItemRemoteStore::Set_CashEvent_Status(v22, v23, 0);
              else
                CashItemRemoteStore::Set_CashEvent_Status(v22, v23, 2);
            }
            else
            {
              CashItemRemoteStore::Set_CashEvent_Status(v22, v23, 3);
            }
          }
          else
          {
            CashItemRemoteStore::Set_CashEvent_Status(v22, v23, 4);
          }
        }
        else
        {
          CashItemRemoteStore::Set_CashEvent_Status(v22, v23, 0);
        }
      }
    }
    else
    {
      CashItemRemoteStore::Set_CashEvent_Status(v22, v23, 0);
    }
    CashItemRemoteStore::SetNextEventTime(v22, v23);
  }
  else
  {
    CashItemRemoteStore::Set_CashEvent_Status(v22, byEventType, 0);
  }
}
