#pragma once
#include "MemoryPatcher.h"
#include "Utils/AddressResolver.h"

namespace NexusPro {
    
    class HookManager {
    private:
        std::unique_ptr<AddressResolver> addressResolver_;
        std::unique_ptr<MemoryPatcher> memoryPatcher_;
        std::unordered_map<std::wstring, HookInfo> installedHooks_;
        std::mutex hooksMutex_;
        std::atomic<bool> isActive_;
        AddressResolver::RFAddresses rfAddresses_;
        
        // Original function pointers
        RFTypes::LoginFunc originalLoginFunc_;
        RFTypes::CheckSessionFunc originalCheckSessionFunc_;
        RFTypes::SendMsgFunc originalSendMsgFunc_;
        RFTypes::RecvMsgFunc originalRecvMsgFunc_;
        RFTypes::CreateMonsterFunc originalCreateMonsterFunc_;
        RFTypes::MonsterLoopFunc originalMonsterLoopFunc_;
        
    public:
        HookManager();
        ~HookManager();
        
        // Initialization
        bool Initialize();
        void Shutdown();
        bool IsActive() const { return isActive_; }
        
        // Hook management
        bool InstallHooks();
        bool UninstallHooks();
        bool InstallHook(const std::wstring& name, LPVOID targetFunction, LPVOID hookFunction, ModuleType moduleType = ModuleType::Core);
        bool UninstallHook(const std::wstring& name);
        
        // Hook status
        HookStatus GetHookStatus(const std::wstring& name);
        std::vector<HookInfo> GetAllHooks();
        size_t GetHookCount() const;
        
        // Module-specific hook installation
        bool InstallAuthenticationHooks();
        bool InstallNetworkHooks();
        bool InstallMonsterHooks();
        bool InstallBugFixHooks();
        bool InstallEnhancementHooks();
        
        // Hook functions (these will be called instead of original functions)
        static BOOL __fastcall Hook_LoginCBillingManager(void* thisPtr, void* userDB);
        static BOOL __fastcall Hook_OnCheckSession_FirstVerify(void* thisPtr, HANDLE session);
        static BOOL __fastcall Hook_CNetworkEX_SendMsg(void* thisPtr, void* data, DWORD size);
        static BOOL __fastcall Hook_CNetworkEX_RecvMsg(void* thisPtr, void* data, DWORD size);
        static void* __fastcall Hook_CreateCMonster(void* thisPtr, void* createData);
        static BOOL __fastcall Hook_CMonster_Loop(void* thisPtr);
        
        // Access to original functions
        RFTypes::LoginFunc GetOriginalLoginFunc() const { return originalLoginFunc_; }
        RFTypes::CheckSessionFunc GetOriginalCheckSessionFunc() const { return originalCheckSessionFunc_; }
        RFTypes::SendMsgFunc GetOriginalSendMsgFunc() const { return originalSendMsgFunc_; }
        RFTypes::RecvMsgFunc GetOriginalRecvMsgFunc() const { return originalRecvMsgFunc_; }
        RFTypes::CreateMonsterFunc GetOriginalCreateMonsterFunc() const { return originalCreateMonsterFunc_; }
        RFTypes::MonsterLoopFunc GetOriginalMonsterLoopFunc() const { return originalMonsterLoopFunc_; }
        
        // Utility methods
        AddressResolver* GetAddressResolver() const { return addressResolver_.get(); }
        MemoryPatcher* GetMemoryPatcher() const { return memoryPatcher_.get(); }
        const AddressResolver::RFAddresses& GetRFAddresses() const { return rfAddresses_; }
        
        // Hook validation
        bool ValidateHooks();
        bool IsHookValid(const std::wstring& name);
        
        // Statistics
        struct HookStatistics {
            size_t totalHooks;
            size_t installedHooks;
            size_t failedHooks;
            size_t authenticationHooks;
            size_t networkHooks;
            size_t bugFixHooks;
            size_t enhancementHooks;
            DWORD installationTime;
            
            HookStatistics() { memset(this, 0, sizeof(HookStatistics)); }
        };
        
        HookStatistics GetStatistics();
        
    private:
        bool ResolveAddresses();
        bool ValidateAddresses();
        void LogHookInstallation(const std::wstring& name, bool success, const std::wstring& reason = L"");
        void LogHookUninstallation(const std::wstring& name, bool success);
        
        // Hook installation helpers
        template<typename T>
        bool InstallSingleHook(const std::wstring& name, LPVOID targetAddress, T hookFunction, T& originalFunction, ModuleType moduleType = ModuleType::Core) {
            if (!targetAddress) {
                LogHookInstallation(name, false, L"Target address is null");
                return false;
            }
            
            if (!addressResolver_->IsAddressValid(targetAddress)) {
                LogHookInstallation(name, false, L"Target address is invalid");
                return false;
            }
            
            // Store original function
            originalFunction = reinterpret_cast<T>(targetAddress);
            
            // Install hook
            if (InstallHook(name, targetAddress, reinterpret_cast<LPVOID>(hookFunction), moduleType)) {
                LogHookInstallation(name, true);
                return true;
            } else {
                LogHookInstallation(name, false, L"Hook installation failed");
                originalFunction = nullptr;
                return false;
            }
        }
        
        // Hook validation helpers
        bool IsAddressHooked(LPVOID address);
        std::wstring GetHookNameByAddress(LPVOID address);
    };
    
    // Global hook manager instance (for use in hook functions)
    extern HookManager* g_hookManager;
}
