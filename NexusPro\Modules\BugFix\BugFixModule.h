#pragma once

namespace NexusPro {
namespace Modules {
    
    class BugFixModule {
    private:
        bool isInitialized_;
        bool patchesApplied_;
        std::mutex moduleMutex_;
        
        // Patch tracking
        std::vector<std::wstring> appliedPatches_;
        std::unordered_map<std::wstring, bool> patchStatus_;
        
    public:
        BugFixModule();
        ~BugFixModule();
        
        // Module lifecycle
        bool Initialize();
        void Shutdown();
        bool IsInitialized() const { return isInitialized_; }
        
        // Patch management
        bool ApplyAllPatches();
        bool RemoveAllPatches();
        bool ApplyPatch(const std::wstring& patchName);
        bool RemovePatch(const std::wstring& patchName);
        bool IsPatchApplied(const std::wstring& patchName);
        
        // Specific bug fixes
        bool FixMonsterLimitBug();
        bool FixItemDuplicationBug();
        bool FixMemoryLeaks();
        bool FixCrashBugs();
        bool FixNetworkBugs();
        bool FixDatabaseBugs();
        
        // Monster-related fixes
        bool FixMonsterSpawnLimits();
        bool FixMonsterAIBugs();
        bool FixMonsterDropBugs();
        bool FixMonsterRespawnBugs();
        
        // Item-related fixes
        bool FixItemStackBugs();
        bool FixItemTradeBugs();
        bool FixItemUpgradeBugs();
        bool FixItemStorageBugs();
        
        // Player-related fixes
        bool FixPlayerMovementBugs();
        bool FixPlayerStatsBugs();
        bool FixPlayerSkillBugs();
        bool FixPlayerInventoryBugs();
        
        // Guild-related fixes
        bool FixGuildBattleBugs();
        bool FixGuildManagementBugs();
        bool FixGuildStorageBugs();
        
        // PvP-related fixes
        bool FixPvPCalculationBugs();
        bool FixPvPRewardBugs();
        bool FixPvPZoneBugs();
        
        // Economy-related fixes
        bool FixEconomyBugs();
        bool FixAuctionBugs();
        bool FixTradeBugs();
        
        // Statistics
        struct BugFixStatistics {
            DWORD totalPatches;
            DWORD appliedPatches;
            DWORD failedPatches;
            DWORD monsterFixes;
            DWORD itemFixes;
            DWORD playerFixes;
            DWORD networkFixes;
            DWORD memoryFixes;
            
            BugFixStatistics() { memset(this, 0, sizeof(BugFixStatistics)); }
        };
        
        BugFixStatistics GetStatistics();
        std::vector<std::wstring> GetAppliedPatches();
        std::vector<std::wstring> GetFailedPatches();
        
        // Configuration
        void UpdateConfiguration();
        
    private:
        // Internal patch implementations
        bool PatchMonsterLimitCheck();
        bool PatchItemDupeExploit();
        bool PatchMemoryLeak_MonsterCleanup();
        bool PatchMemoryLeak_ItemCleanup();
        bool PatchMemoryLeak_PlayerCleanup();
        bool PatchCrash_NullPointerCheck();
        bool PatchCrash_BufferOverflow();
        bool PatchCrash_StackOverflow();
        
        // Utility methods
        bool ApplyBytePatch(const std::wstring& name, LPVOID address, const std::vector<BYTE>& newBytes);
        bool ApplyJumpPatch(const std::wstring& name, LPVOID fromAddress, LPVOID toAddress);
        bool ApplyNopPatch(const std::wstring& name, LPVOID address, SIZE_T size);
        
        // Validation
        bool ValidatePatchTarget(LPVOID address, SIZE_T size);
        bool ValidatePatchIntegrity(const std::wstring& patchName);
        
        // Logging
        void LogPatchApplication(const std::wstring& patchName, bool success, const std::wstring& reason = L"");
        void LogBugFixEvent(const std::wstring& event, const std::wstring& details);
    };
    
}}  // namespace NexusPro::Modules
