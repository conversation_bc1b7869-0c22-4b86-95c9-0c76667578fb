/*
 * Function: ?pc_ChatGuildRequest@CPlayer@@QEAAXKPEAD@Z
 * Address: 0x140091B30
 */

void __fastcall CPlayer::pc_ChatGuildRequest(CPlayer *this, unsigned int dwDstSerial, char *pwszChatData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@13
  unsigned __int16 v6; // ax@14
  CChatStealSystem *v7; // rax@22
  __int64 v8; // [sp+0h] [bp-218h]@1
  char v9; // [sp+30h] [bp-1E8h]@4
  CPlayer *v10; // [sp+38h] [bp-1E0h]@4
  _guild_member_info *v11; // [sp+40h] [bp-1D8h]@7
  _announ_message_receipt_udp Dst; // [sp+60h] [bp-1B8h]@13
  char pbyType; // [sp+194h] [bp-84h]@13
  char v14; // [sp+195h] [bp-83h]@13
  int v15; // [sp+1A4h] [bp-74h]@13
  int j; // [sp+1A8h] [bp-70h]@15
  _guild_member_info *v17; // [sp+1B0h] [bp-68h]@18
  char szMsg; // [sp+1C4h] [bp-54h]@23
  char v19; // [sp+1E4h] [bp-34h]@23
  char v20; // [sp+1E5h] [bp-33h]@23
  unsigned __int64 v21; // [sp+200h] [bp-18h]@4
  CPlayer *pPlayer; // [sp+220h] [bp+8h]@1
  const char *Str; // [sp+230h] [bp+18h]@1

  Str = pwszChatData;
  pPlayer = this;
  v3 = &v8;
  for ( i = 132i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v21 = (unsigned __int64)&v8 ^ _security_cookie;
  v9 = 0;
  v10 = 0i64;
  if ( pPlayer->m_Param.m_pGuild )
  {
    if ( dwDstSerial != -1 )
    {
      v11 = CGuild::GetMemberFromSerial(pPlayer->m_Param.m_pGuild, dwDstSerial);
      if ( v11 )
      {
        if ( v11->pPlayer )
          v10 = v11->pPlayer;
        else
          v9 = -49;
      }
      else
      {
        v9 = -54;
      }
    }
  }
  else
  {
    v9 = -54;
  }
  if ( v9 )
  {
    szMsg = v9;
    v19 = 2;
    v20 = 100;
    CNetProcess::LoadSendMsg(unk_1414F2088, pPlayer->m_ObjID.m_wIndex, &v19, &szMsg, 1u);
  }
  else
  {
    _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
    Dst.bySenderRace = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
    Dst.dwSenderSerial = pPlayer->m_dwObjSerial;
    v5 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
    strcpy_0(Dst.wszSenderName, v5);
    Dst.byPvpGrade = pPlayer->m_Param.m_byPvPGrade;
    Dst.bySize = strlen_0(Str);
    memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
    Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
    pbyType = 2;
    v14 = 11;
    v15 = _announ_message_receipt_udp::size(&Dst);
    if ( v10 )
    {
      Dst.byMessageType = 8;
      v6 = _announ_message_receipt_udp::size(&Dst);
      CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v6);
    }
    else
    {
      Dst.byMessageType = 7;
      for ( j = 0; j < 50; ++j )
      {
        v17 = &pPlayer->m_Param.m_pGuild->m_MemberData[j];
        if ( _guild_member_info::IsFill(v17) )
        {
          if ( v17->pPlayer )
            CNetProcess::LoadSendMsg(unk_1414F2088, v17->pPlayer->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v15);
        }
      }
    }
    v7 = CChatStealSystem::Instance();
    CChatStealSystem::StealChatMsg(v7, pPlayer, Dst.byMessageType, (char *)Str);
  }
}
