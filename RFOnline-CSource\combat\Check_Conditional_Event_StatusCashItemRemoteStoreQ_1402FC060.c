/*
 * Function: ?Check_Conditional_Event_Status@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402FC060
 */

void __fastcall CashItemRemoteStore::Check_Conditional_Event_Status(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  char v4; // [sp+20h] [bp-38h]@4
  __time32_t Time; // [sp+34h] [bp-24h]@4
  int j; // [sp+44h] [bp-14h]@8
  int v7; // [sp+48h] [bp-10h]@22
  char v8; // [sp+4Ch] [bp-Ch]@15
  CashItemRemoteStore *v9; // [sp+60h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = CashItemRemoteStore::Get_Conditional_Event_Status(v9);
  _time32(&Time);
  if ( v9->m_cde.m_ini.m_cdeTime[0] > Time && v9->m_cde.m_ini.m_cdeTime[0] - Time < 300 && v9->m_con_event.m_bConEvent )
  {
    CashItemRemoteStore::Set_Conditional_Evnet_Status(v9, 0);
    CashItemRemoteStore::Inform_ConditionalEvent_Status_All(
      v9,
      v9->m_con_event.m_ini.m_byEventKind,
      4,
      v9->m_con_event.m_ini.m_szEndMsg);
    v9->m_con_event.m_bConEvent = 0;
  }
  else
  {
    for ( j = 0; j < 3; ++j )
    {
      if ( v9->m_cash_event[j].m_ini.m_EventTime[0] > Time
        && v9->m_cash_event[j].m_ini.m_EventTime[0] - Time < 300
        && v9->m_con_event.m_bConEvent )
      {
        CashItemRemoteStore::Set_Conditional_Evnet_Status(v9, 0);
        CashItemRemoteStore::Inform_ConditionalEvent_Status_All(
          v9,
          v9->m_con_event.m_ini.m_byEventKind,
          4,
          v9->m_con_event.m_ini.m_szEndMsg);
        v9->m_con_event.m_bConEvent = 0;
        return;
      }
    }
    v8 = v4;
    if ( v4 == 1 )
    {
      if ( Time >= v9->m_con_event.m_eventtime.m_EventTime[0] )
      {
        CashItemRemoteStore::Set_Conditional_Evnet_Status(v9, 2);
        CashItemRemoteStore::Inform_ConditionalEvent_Status_All(
          v9,
          v9->m_con_event.m_ini.m_byEventKind,
          2,
          v9->m_con_event.m_ini.m_szStartMsg);
      }
    }
    else if ( v8 == 2 )
    {
      v7 = (v9->m_con_event.m_eventtime.m_EventTime[1] - v9->m_con_event.m_eventtime.m_EventTime[0]) / 2;
      if ( v7 >= v9->m_con_event.m_eventtime.m_EventTime[1] - Time )
      {
        CashItemRemoteStore::Set_Conditional_Evnet_Status(v9, 3);
        CashItemRemoteStore::Inform_ConditionalEvent_Status_All(
          v9,
          v9->m_con_event.m_ini.m_byEventKind,
          3,
          v9->m_con_event.m_ini.m_szMiddletMsg);
      }
    }
    else if ( v8 == 3 && v9->m_con_event.m_eventtime.m_EventTime[1] <= Time )
    {
      CashItemRemoteStore::Set_Conditional_Evnet_Status(v9, 0);
      CashItemRemoteStore::Inform_ConditionalEvent_Status_All(
        v9,
        v9->m_con_event.m_ini.m_byEventKind,
        4,
        v9->m_con_event.m_ini.m_szEndMsg);
      v9->m_con_event.m_bConEvent = 0;
    }
  }
}
