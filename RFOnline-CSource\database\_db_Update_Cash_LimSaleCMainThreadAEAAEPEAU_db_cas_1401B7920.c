/*
 * Function: ?_db_Update_Cash_LimSale@CMainThread@@AEAAEPEAU_db_cash_limited_sale@@0@Z
 * Address: 0x1401B7920
 */

char __fastcall CMainThread::_db_Update_Cash_LimSale(CMainThread *this, _db_cash_limited_sale *pNewData, _db_cash_limited_sale *pOldData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  size_t v5; // rax@15
  char result; // al@16
  __int64 v7; // [sp+0h] [bp-8F8h]@1
  char Dest; // [sp+30h] [bp-8C8h]@4
  char v9; // [sp+31h] [bp-8C7h]@4
  char v10; // [sp+CFh] [bp-829h]@16
  char DstBuf; // [sp+D0h] [bp-828h]@4
  char v12; // [sp+D1h] [bp-827h]@4
  int v13; // [sp+8D4h] [bp-24h]@4
  unsigned int j; // [sp+8D8h] [bp-20h]@8
  unsigned __int64 v15; // [sp+8E8h] [bp-10h]@4
  CMainThread *v16; // [sp+900h] [bp+8h]@1
  _db_cash_limited_sale *v17; // [sp+908h] [bp+10h]@1
  _db_cash_limited_sale *v18; // [sp+910h] [bp+18h]@1

  v18 = pOldData;
  v17 = pNewData;
  v16 = this;
  v3 = &v7;
  for ( i = 572i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v7 ^ _security_cookie;
  Dest = 0;
  memset(&v9, 0, 0x7Fui64);
  DstBuf = 0;
  memset(&v12, 0, 0x7FFui64);
  sprintf_s(&DstBuf, 0x800ui64, "UPDATE tbl_Cash_LimSale Set ");
  v13 = strlen_0(&DstBuf);
  if ( v17->byDck != v18->byDck )
  {
    sprintf(&Dest, "DCK=%d,", v17->byDck);
    strcat_s(&DstBuf, 0x800ui64, &Dest);
  }
  if ( v17->byLimited_sale_num != v18->byLimited_sale_num )
  {
    sprintf(&Dest, "LimSaleNum=%d,", v17->byLimited_sale_num);
    strcat_s(&DstBuf, 0x800ui64, &Dest);
  }
  for ( j = 0; (signed int)j < v17->byLimited_sale_num; ++j )
  {
    if ( v17->List[j].nLimcode != v18->List[j].nLimcode )
    {
      sprintf(&Dest, "Code_K%d=%d,", j, v17->List[j].nLimcode);
      strcat_s(&DstBuf, 0x800ui64, &Dest);
    }
    if ( v17->List[j].nLimcount != v18->List[j].nLimcount )
    {
      sprintf(&Dest, "Num%d=%d,", j, v17->List[j].nLimcount);
      strcat_s(&DstBuf, 0x800ui64, &Dest);
    }
  }
  v5 = strlen_0(&DstBuf);
  if ( v5 <= v13 )
  {
    memset_0(&DstBuf, 0, v13);
    result = 0;
  }
  else
  {
    sprintf_s(&Dest, 0x80ui64, "WHERE [index] = 1");
    *(&v10 + strlen_0(&DstBuf)) = 32;
    strcat_s(&DstBuf, 0x800ui64, &Dest);
    CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v16->m_pWorldDB->vfptr, &DstBuf, 1);
    result = 0;
  }
  return result;
}
