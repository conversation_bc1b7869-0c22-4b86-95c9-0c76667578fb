/*
 * Function: ??1CNormalGuildBattleStateRoundProcess@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403F1890
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleStateRoundProcess::~CNormalGuildBattleStateRoundProcess(GUILD_BATTLE::CNormalGuildBattleStateRoundProcess *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@6
  __int64 v4; // [sp+0h] [bp-48h]@1
  CMyTimer *v5; // [sp+20h] [bp-28h]@5
  CMyTimer *v6; // [sp+28h] [bp-20h]@5
  __int64 v7; // [sp+30h] [bp-18h]@4
  __int64 v8; // [sp+38h] [bp-10h]@6
  GUILD_BATTLE::CNormalGuildBattleStateRoundProcess *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = -2i64;
  v9->vfptr = (GUILD_BATTLE::CGuildBattleStateVtbl *)&GUILD_BATTLE::CNormalGuildBattleStateRoundProcess::`vftable';
  if ( v9->m_pkTimer )
  {
    v6 = v9->m_pkTimer;
    v5 = v6;
    if ( v6 )
    {
      LODWORD(v3) = ((int (__fastcall *)(CMyTimer *, signed __int64))v5->vfptr->__vecDelDtor)(v5, 1i64);
      v8 = v3;
    }
    else
    {
      v8 = 0i64;
    }
    v9->m_pkTimer = 0i64;
  }
  GUILD_BATTLE::CNormalGuildBattleStateRound::~CNormalGuildBattleStateRound((GUILD_BATTLE::CNormalGuildBattleStateRound *)&v9->vfptr);
}
