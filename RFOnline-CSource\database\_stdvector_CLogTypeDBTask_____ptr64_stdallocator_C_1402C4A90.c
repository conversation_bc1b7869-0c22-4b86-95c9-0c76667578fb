/*
 * Function: _std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::reserve_::_1_::catch$0
 * Address: 0x1402C4A90
 */

void __fastcall __noreturn std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::reserve_::_1_::catch_0(__int64 a1, __int64 a2)
{
  std::allocator<CLogTypeDBTask *>::deallocate(
    (std::allocator<CLogTypeDBTask *> *)(*(_QWORD *)(a2 + 160) + 8i64),
    *(CLogTypeDBTask ***)(a2 + 32),
    *(_QWORD *)(a2 + 168));
  CxxThrowException_0(0i64, 0i64);
}
