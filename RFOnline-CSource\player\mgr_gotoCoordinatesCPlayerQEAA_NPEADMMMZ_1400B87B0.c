/*
 * Function: ?mgr_gotoCoordinates@CPlayer@@QEAA_NPEADMMM@Z
 * Address: 0x1400B87B0
 */

char __fastcall CPlayer::mgr_gotoCoordinates(CPlayer *this, char *pszMapCode, float fX, float fY, float fZ)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v8; // rax@10
  __int64 v9; // [sp+0h] [bp-78h]@1
  float *pfStartPos; // [sp+20h] [bp-58h]@10
  CMapData *pIntoMap; // [sp+30h] [bp-48h]@4
  float fPos; // [sp+48h] [bp-30h]@8
  float v13; // [sp+4Ch] [bp-2Ch]@8
  float v14; // [sp+50h] [bp-28h]@8
  CPlayer *v15; // [sp+80h] [bp+8h]@1

  v15 = this;
  v5 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  pIntoMap = CMapOperation::GetMap(&g_MapOper, pszMapCode);
  if ( pIntoMap )
  {
    if ( pIntoMap->m_pMapSet->m_nMapType )
    {
      result = 0;
    }
    else
    {
      fPos = fX;
      v13 = fY;
      v14 = fZ;
      if ( CMapData::IsMapIn(pIntoMap, &fPos) )
      {
        CPlayer::OutOfMap(v15, pIntoMap, 0, 4, &fPos);
        v8 = (char *)pIntoMap->m_pMapSet;
        LOBYTE(pfStartPos) = 4;
        CPlayer::SendMsg_GotoRecallResult(v15, 0, *v8, &fPos, 4);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
