/*
 * Function: ?AdvanceRegenState@CNormalGuildBattleStateInBattle@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403EB290
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleStateInBattle::AdvanceRegenState(GUILD_BATTLE::CNormalGuildBattleStateInBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleStateInBattle *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( GUILD_BATTLE::CNormalGuildBattleStateInBattle::IsInBattleRegenState(v6) )
  {
    v5 = GUILD_BATTLE::CGuildBattleStateList::Next(
           (GUILD_BATTLE::CGuildBattleStateList *)&v6->m_kRountStateList.vfptr,
           0) == 1;
    result = v5;
  }
  else
  {
    result = 0;
  }
  return result;
}
