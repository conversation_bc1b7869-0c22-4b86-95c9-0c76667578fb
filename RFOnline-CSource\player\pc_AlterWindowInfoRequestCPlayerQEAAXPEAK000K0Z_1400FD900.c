/*
 * Function: ?pc_AlterWindowInfoRequest@CPlayer@@QEAAXPEAK000K0@Z
 * Address: 0x1400FD900
 */

void __fastcall CPlayer::pc_AlterWindowInfoRequest(CPlayer *this, unsigned int *pdwSkill, unsigned int *pdwForce, unsigned int *pdwChar, unsigned int *pdwAnimus, unsigned int dwInven, unsigned int *pdwInvenBag)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-48h]@1
  unsigned int *v10; // [sp+20h] [bp-28h]@5
  unsigned int v11; // [sp+28h] [bp-20h]@5
  unsigned int *v12; // [sp+30h] [bp-18h]@5
  CPlayer *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v7 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( v13->m_pUserDB )
  {
    v12 = pdwInvenBag;
    v11 = dwInven;
    v10 = pdwAnimus;
    CUserDB::Update_WindowInfo(v13->m_pUserDB, pdwSkill, pdwForce, pdwChar, pdwAnimus, dwInven, pdwInvenBag);
  }
}
