/*
 * Function: ?_db_Load_CryMsg@CMainThread@@AEAAEKPEAU_CRYMSG_DB_BASE@@@Z
 * Address: 0x1401A97E0
 */

char __fastcall CMainThread::_db_Load_CryMsg(CMainThread *this, unsigned int dwSerial, _CRYMSG_DB_BASE *pBossCry)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-2E8h]@1
  char Dst[660]; // [sp+30h] [bp-2B8h]@4
  char v8; // [sp+2C4h] [bp-24h]@4
  int j; // [sp+2C8h] [bp-20h]@11
  unsigned __int64 v10; // [sp+2D8h] [bp-10h]@4
  CMainThread *v11; // [sp+2F0h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+2F8h] [bp+10h]@1
  _CRYMSG_DB_BASE *v13; // [sp+300h] [bp+18h]@1

  v13 = pBossCry;
  dwSeriala = dwSerial;
  v11 = this;
  v3 = &v6;
  for ( i = 184i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v6 ^ _security_cookie;
  memset_0(Dst, 0, 0x28Aui64);
  v8 = CRFWorldDatabase::Select_BossCryMsg(v11->m_pWorldDB, dwSeriala, (_worlddb_crymsg_info *)Dst);
  if ( v8 == 1 )
    return 24;
  if ( v8 != 2 )
    goto LABEL_17;
  if ( !CRFWorldDatabase::Insert_BossCryRecord(v11->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_BossCryMsg(v11->m_pWorldDB, dwSeriala, (_worlddb_crymsg_info *)Dst) )
  {
    result = 24;
  }
  else
  {
LABEL_17:
    for ( j = 0; j < 10; ++j )
      strcpy_0((char *)v13 + 65 * j, &Dst[65 * j]);
    result = 0;
  }
  return result;
}
