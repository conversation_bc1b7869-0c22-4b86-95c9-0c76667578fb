/*
 * Function: ?_db_Update_CryMsg@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401B0910
 */

char __fastcall CMainThread::_db_Update_CryMsg(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pwszQuery)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  size_t v7; // rax@9
  __int64 v9; // [sp+0h] [bp-268h]@1
  char Source; // [sp+30h] [bp-238h]@4
  char v11; // [sp+31h] [bp-237h]@4
  char *Dest; // [sp+238h] [bp-30h]@4
  size_t Size; // [sp+240h] [bp-28h]@4
  unsigned __int64 v14; // [sp+250h] [bp-18h]@4
  unsigned int v15; // [sp+278h] [bp+10h]@1
  _AVATOR_DATA *v16; // [sp+280h] [bp+18h]@1
  _AVATOR_DATA *v17; // [sp+288h] [bp+20h]@1

  v17 = pOldData;
  v16 = pNewData;
  v15 = dwSerial;
  v5 = &v9;
  for ( i = 152i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v14 = (unsigned __int64)&v9 ^ _security_cookie;
  Source = 0;
  memset(&v11, 0, 0x1FFui64);
  Dest = pwszQuery;
  sprintf(pwszQuery, "UPDATE tbl_CryMsg SET ");
  for ( Size = (unsigned int)strlen_0(Dest); SHIDWORD(Size) < 10; ++HIDWORD(Size) )
  {
    if ( strcmp_0(
           (const char *)&v16->dbBossCry + 65 * SHIDWORD(Size),
           (const char *)&v17->dbBossCry + 65 * SHIDWORD(Size)) )
    {
      sprintf(
        &Source,
        "CryMsg%d='%s',",
        (unsigned int)(HIDWORD(Size) + 1),
        (char *)&v16->dbBossCry + 65 * SHIDWORD(Size));
      strcat_0(Dest, &Source);
    }
  }
  v7 = strlen_0(Dest);
  if ( v7 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE Serial=%d", v15);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
