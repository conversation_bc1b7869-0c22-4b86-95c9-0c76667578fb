# Visual Studio files
*.vcxproj.user
*.vcxproj.filters
*.sdf
*.opensdf
*.suo
*.user
*.aps
*.pch
*.vspscc
*_i.c
*_p.c
*.ncb
*.tlb
*.tlh
*.bak
*.cache
*.ilk
*.log
*.lib
*.sbr
*.scc
[Bb]in/
[Dd]ebug*/
[Rr]elease*/
[Oo]bj/
[Tt]emp/
[Tt]mp/
x64/
x86/
.vs/

# Build output
Build/Debug/
Build/Release/
Build/*/Intermediate/
*.dll
*.exe
*.pdb
*.exp
*.iobj
*.ipdb

# Logs
*.log
NexusPro.log

# Configuration (keep template)
Config/nexuspro.ini.user

# Documentation build
Docs/_build/
Docs/.doctrees/

# IDE files
.vscode/
*.code-workspace

# Temporary files
*~
*.swp
*.tmp

# OS files
Thumbs.db
.DS_Store
desktop.ini

# Test files
Tests/temp/
Tests/output/

# Backup files
*.bak
*.backup
*.old

# Package files
*.zip
*.rar
*.7z
