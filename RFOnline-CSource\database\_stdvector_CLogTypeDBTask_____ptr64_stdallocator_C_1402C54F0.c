/*
 * Function: _std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::insert_::_1_::dtor$3
 * Address: 0x1402C54F0
 */

void __fastcall std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::insert_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>((std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)(a2 + 96));
}
