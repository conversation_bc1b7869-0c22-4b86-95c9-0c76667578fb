/*
 * Function: ?_db_Load_Unit@CMainThread@@AEAAEKPEAU_UNIT_DB_BASE@@@Z
 * Address: 0x1401A77C0
 */

char __fastcall CMainThread::_db_Load_Unit(CMainThread *this, unsigned int dwSerial, _UNIT_DB_BASE *pCon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-168h]@1
  char Dst[4]; // [sp+30h] [bp-138h]@4
  int v8; // [sp+34h] [bp-134h]@15
  char v9; // [sp+38h] [bp-130h]@15
  char v10; // [sp+39h] [bp-12Fh]@15
  char v11; // [sp+3Ah] [bp-12Eh]@15
  char v12; // [sp+3Bh] [bp-12Dh]@15
  char v13; // [sp+3Ch] [bp-12Ch]@15
  char v14[3]; // [sp+3Dh] [bp-12Bh]@15
  int v15; // [sp+40h] [bp-128h]@15
  int v16[10]; // [sp+44h] [bp-124h]@15
  int v17; // [sp+6Ch] [bp-FCh]@15
  int v18[53]; // [sp+70h] [bp-F8h]@15
  char v19; // [sp+144h] [bp-24h]@4
  int j; // [sp+148h] [bp-20h]@11
  int k; // [sp+14Ch] [bp-1Ch]@15
  unsigned __int64 v22; // [sp+158h] [bp-10h]@4
  CMainThread *v23; // [sp+170h] [bp+8h]@1
  unsigned int dwCharacterSerial; // [sp+178h] [bp+10h]@1
  _UNIT_DB_BASE *v25; // [sp+180h] [bp+18h]@1

  v25 = pCon;
  dwCharacterSerial = dwSerial;
  v23 = this;
  v3 = &v6;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v22 = (unsigned __int64)&v6 ^ _security_cookie;
  memset_0(Dst, 0, 0x110ui64);
  v19 = CRFWorldDatabase::Select_Unit(v23->m_pWorldDB, dwCharacterSerial, (_worlddb_unit_info_array *)Dst);
  if ( v19 == 1 )
    return 24;
  if ( v19 != 2 )
    goto LABEL_22;
  if ( !CRFWorldDatabase::Insert_Unit(v23->m_pWorldDB, dwCharacterSerial) )
    return 24;
  if ( CRFWorldDatabase::Select_Unit(v23->m_pWorldDB, dwCharacterSerial, (_worlddb_unit_info_array *)Dst) )
  {
    result = 24;
  }
  else
  {
LABEL_22:
    for ( j = 0; j < 4; ++j )
    {
      v25->m_List[j].byFrame = Dst[68 * j];
      if ( v25->m_List[j].byFrame != 255 )
      {
        v25->m_List[j].byPart[0] = *(&v9 + 68 * j);
        v25->m_List[j].byPart[1] = *(&v10 + 68 * j);
        v25->m_List[j].byPart[2] = *(&v11 + 68 * j);
        v25->m_List[j].byPart[3] = *(&v12 + 68 * j);
        v25->m_List[j].byPart[4] = *(&v13 + 68 * j);
        v25->m_List[j].byPart[5] = v14[68 * j];
        v25->m_List[j].dwGauge = *(&v8 + 17 * j);
        v25->m_List[j].dwBullet[0] = *(&v15 + 17 * j);
        v25->m_List[j].dwBullet[1] = v16[17 * j];
        v25->m_List[j].nPullingFee = *(&v17 + 17 * j);
        v25->m_List[j].dwCutTime = v18[17 * j];
        for ( k = 0; k < 8; ++k )
          v25->m_List[j].dwSpare[k] = *((_DWORD *)&Dst[68 * j + 24] + k);
      }
    }
    result = 0;
  }
  return result;
}
