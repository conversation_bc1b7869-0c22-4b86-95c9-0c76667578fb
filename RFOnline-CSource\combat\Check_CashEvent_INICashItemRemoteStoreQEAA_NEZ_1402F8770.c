/*
 * Function: ?Check_CashEvent_INI@CashItemRemoteStore@@QEAA_NE@Z
 * Address: 0x1402F8770
 */

char __fastcall CashItemRemoteStore::Check_CashEvent_INI(CashItemRemoteStore *this, char byEventType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-748h]@1
  _cash_event_ini pIni; // [sp+30h] [bp-718h]@4
  _FILETIME pft; // [sp+5D8h] [bp-170h]@4
  char szEventName; // [sp+600h] [bp-148h]@4
  char v9; // [sp+704h] [bp-44h]@7
  __time32_t Time; // [sp+714h] [bp-34h]@7
  int v11; // [sp+724h] [bp-24h]@15
  unsigned __int64 v12; // [sp+730h] [bp-18h]@4
  CashItemRemoteStore *v13; // [sp+750h] [bp+8h]@1
  char v14; // [sp+758h] [bp+10h]@1

  v14 = byEventType;
  v13 = this;
  v2 = &v5;
  for ( i = 464i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  _cash_event_ini::_cash_event_ini(&pIni);
  Get_CashEvent_Name(v14, &szEventName);
  CashItemRemoteStore::Load_Event_INI(v13, &pIni, &pft, &szEventName);
  if ( pft.dwHighDateTime != v13->m_cash_event[(unsigned __int8)v14].m_event_ini_file_time.dwHighDateTime
    || pft.dwLowDateTime != v13->m_cash_event[(unsigned __int8)v14].m_event_ini_file_time.dwLowDateTime )
  {
    v13->m_cash_event[(unsigned __int8)v14].m_event_ini_file_time.dwHighDateTime = pft.dwHighDateTime;
    v13->m_cash_event[(unsigned __int8)v14].m_event_ini_file_time.dwLowDateTime = pft.dwLowDateTime;
    CLogFile::Write(&v13->m_cash_event[(unsigned __int8)v14].m_event_log, "Event INI File Changed (Server Runing)");
    v9 = 0;
    _time32(&Time);
    if ( pIni.m_bUseCashEvent )
    {
      if ( Time >= pIni.m_EventTime[0] )
      {
        if ( Time < pIni.m_EventTime[0] || Time > pIni.m_EventTime[1] )
        {
          if ( Time > pIni.m_EventTime[1] )
          {
            v9 = 5;
            if ( v13->m_cash_event[(unsigned __int8)v14].m_ini.m_bUseCashEvent )
            {
              if ( CashItemRemoteStore::IsEventTime(v13, v14) )
                CashItemRemoteStore::Inform_CashEvent_Status_All(v13, v14, v9, &pIni);
            }
          }
        }
        else
        {
          v11 = pIni.m_EventTime[1] - Time;
          if ( pIni.m_EventTime[1] == Time )
          {
            v9 = 0;
          }
          else if ( v11 && (unsigned int)v11 <= v13->m_cash_event[(unsigned __int8)v14].m_event_inform_before[1] )
          {
            v9 = 4;
          }
          else if ( (unsigned int)v11 <= v13->m_cash_event[(unsigned __int8)v14].m_event_inform_before[1]
                 || (unsigned int)v11 > v13->m_cash_event[(unsigned __int8)v14].m_event_inform_before[0] )
          {
            v9 = (unsigned int)v11 > v13->m_cash_event[(unsigned __int8)v14].m_event_inform_before[0];
          }
          else
          {
            v9 = 3;
          }
          if ( v13->m_cash_event[(unsigned __int8)v14].m_ini.m_bUseCashEvent
            && CashItemRemoteStore::IsEventTime(v13, v14)
            && v9 )
          {
            CashItemRemoteStore::Inform_CashEvent_Status_All(v13, v14, v9, &pIni);
          }
        }
      }
      else
      {
        v9 = 1;
        if ( v13->m_cash_event[(unsigned __int8)v14].m_ini.m_bUseCashEvent )
        {
          if ( CashItemRemoteStore::IsEventTime(v13, v14) )
            CashItemRemoteStore::Inform_CashEvent_Status_All(v13, v14, v9, &pIni);
        }
      }
    }
    else if ( v13->m_cash_event[(unsigned __int8)v14].m_ini.m_bUseCashEvent && !pIni.m_bUseCashEvent && !v14 )
    {
      v9 = 5;
      CashItemRemoteStore::Inform_CashEvent_Status_All(v13, 0, 5, &pIni);
    }
    CashItemRemoteStore::Set_CashEvent_Status(v13, v14, v9);
    CashItemRemoteStore::Update_INI(v13, &pIni, v14);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
