#include "pch.h"
#include "Core/HookManager.h"
#include "Core/ConfigManager.h"

using namespace NexusPro;

// Global instances
std::unique_ptr<HookManager> g_hookManager;
std::unique_ptr<ConfigManager> g_configManager;

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        // Disable DLL thread notifications for performance
        DisableThreadLibraryCalls(hModule);
        
        // Initialize logger first
        Logger::Initialize(L"NexusPro.log");
        NEXUS_INFO(L"NexusPro DLL loaded - Version " NEXUSPRO_VERSION L" Build " NEXUSPRO_BUILD);
        
        try {
            // Initialize configuration manager
            g_configManager = std::make_unique<ConfigManager>();
            if (!g_configManager->Initialize()) {
                NEXUS_ERROR(L"Failed to initialize configuration manager");
                return FALSE;
            }
            
            // Initialize hook manager
            g_hookManager = std::make_unique<HookManager>();
            if (!g_hookManager->Initialize()) {
                NEXUS_ERROR(L"Failed to initialize hook manager");
                return FALSE;
            }
            
            // Install hooks
            if (!g_hookManager->InstallHooks()) {
                NEXUS_ERROR(L"Failed to install hooks");
                return FALSE;
            }
            
            NEXUS_INFO(L"NexusPro initialization completed successfully");
        }
        catch (const std::exception& e) {
            NEXUS_ERROR(L"Exception during initialization: " + std::wstring(e.what(), e.what() + strlen(e.what())));
            return FALSE;
        }
        break;
    }
    
    case DLL_PROCESS_DETACH:
    {
        NEXUS_INFO(L"NexusPro DLL unloading...");
        
        try {
            // Uninstall hooks
            if (g_hookManager) {
                g_hookManager->UninstallHooks();
                g_hookManager.reset();
            }
            
            // Cleanup configuration
            if (g_configManager) {
                g_configManager.reset();
            }
            
            NEXUS_INFO(L"NexusPro cleanup completed");
        }
        catch (const std::exception& e) {
            NEXUS_ERROR(L"Exception during cleanup: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        }
        
        // Shutdown logger last
        Logger::Shutdown();
        break;
    }
    
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }
    
    return TRUE;
}

// Export functions for external access
extern "C" {
    NEXUSPRO_API BOOL NexusProGetVersion(LPWSTR buffer, DWORD bufferSize) {
        if (!buffer || bufferSize < 32) return FALSE;
        wcscpy_s(buffer, bufferSize, NEXUSPRO_VERSION L"." NEXUSPRO_BUILD);
        return TRUE;
    }
    
    NEXUSPRO_API BOOL NexusProIsActive() {
        return (g_hookManager != nullptr && g_hookManager->IsActive());
    }
    
    NEXUSPRO_API BOOL NexusProReloadConfig() {
        if (!g_configManager) return FALSE;
        return g_configManager->Reload();
    }
}
