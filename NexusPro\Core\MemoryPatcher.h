#pragma once

namespace NexusPro {
    
    class MemoryPatcher {
    private:
        struct PatchInfo {
            LPVOID address;
            std::vector<BYTE> originalBytes;
            std::vector<BYTE> patchedBytes;
            bool isApplied;
            std::wstring description;
            DWORD timestamp;
            
            PatchInfo() : address(nullptr), isApplied(false), timestamp(0) {}
        };
        
        std::unordered_map<std::wstring, PatchInfo> patches_;
        std::mutex patchesMutex_;
        
    public:
        MemoryPatcher();
        ~MemoryPatcher();
        
        // Basic patching operations
        bool PatchBytes(const std::wstring& name, LPVOID address, const std::vector<BYTE>& newBytes, const std::wstring& description = L"");
        bool RestorePatch(const std::wstring& name);
        bool IsPatchApplied(const std::wstring& name);
        
        // Advanced patching operations
        bool PatchFunction(const std::wstring& name, LPVOID targetFunction, LPVOID hookFunction, const std::wstring& description = L"");
        bool CreateJumpPatch(const std::wstring& name, LPVOID fromAddress, LPVOID toAddress, const std::wstring& description = L"");
        bool CreateCallPatch(const std::wstring& name, LPVOID fromAddress, LPVOID toAddress, const std::wstring& description = L"");
        
        // NOP operations
        bool NopBytes(const std::wstring& name, LPVOID address, SIZE_T size, const std::wstring& description = L"");
        bool RestoreNop(const std::wstring& name);
        
        // Memory protection
        bool ChangeMemoryProtection(LPVOID address, SIZE_T size, DWORD newProtection, DWORD& oldProtection);
        bool RestoreMemoryProtection(LPVOID address, SIZE_T size, DWORD oldProtection);
        
        // Patch management
        std::vector<std::wstring> GetAllPatchNames();
        PatchInfo GetPatchInfo(const std::wstring& name);
        bool RemovePatch(const std::wstring& name);
        void RemoveAllPatches();
        
        // Validation
        bool ValidatePatch(const std::wstring& name);
        bool ValidateAllPatches();
        
        // Utility methods
        static std::vector<BYTE> CreateJumpBytes(LPVOID fromAddress, LPVOID toAddress);
        static std::vector<BYTE> CreateCallBytes(LPVOID fromAddress, LPVOID toAddress);
        static std::vector<BYTE> CreateNopBytes(SIZE_T count);
        static SIZE_T GetJumpSize() { return 5; } // JMP instruction size
        static SIZE_T GetCallSize() { return 5; } // CALL instruction size
        
        // Memory reading/writing
        bool ReadMemory(LPVOID address, void* buffer, SIZE_T size);
        bool WriteMemory(LPVOID address, const void* buffer, SIZE_T size);
        bool WriteProtectedMemory(LPVOID address, const void* buffer, SIZE_T size);
        
        // Pattern replacement
        bool ReplacePattern(const std::wstring& name, LPVOID startAddress, SIZE_T searchSize,
                           const std::vector<BYTE>& searchPattern, const std::string& searchMask,
                           const std::vector<BYTE>& replacePattern, const std::wstring& description = L"");
        
        // Statistics
        struct PatchStatistics {
            size_t totalPatches;
            size_t appliedPatches;
            size_t failedPatches;
            size_t jumpPatches;
            size_t callPatches;
            size_t nopPatches;
            size_t bytePatches;
            
            PatchStatistics() { memset(this, 0, sizeof(PatchStatistics)); }
        };
        
        PatchStatistics GetStatistics();
        
        // RF Online specific patches
        bool ApplyRFBugFixes();
        bool ApplyRFEnhancements();
        
    private:
        bool BackupOriginalBytes(PatchInfo& patch, SIZE_T size);
        bool ApplyPatchInternal(PatchInfo& patch);
        bool RestorePatchInternal(PatchInfo& patch);
        bool IsValidAddress(LPVOID address, SIZE_T size);
        
        // RF Online specific patch implementations
        bool PatchMonsterLimitBug();
        bool PatchItemDupeBug();
        bool PatchMemoryLeaks();
        bool PatchCrashBugs();
        bool EnhanceMonsterAI();
        bool EnhanceGuildSystem();
        bool EnhancePvPSystem();
        
        // Helper methods
        DWORD CalculateRelativeAddress(LPVOID fromAddress, LPVOID toAddress);
        bool IsRelativeJumpPossible(LPVOID fromAddress, LPVOID toAddress);
        std::vector<BYTE> Int32ToBytes(INT32 value);
        INT32 BytesToInt32(const std::vector<BYTE>& bytes);
    };
}
