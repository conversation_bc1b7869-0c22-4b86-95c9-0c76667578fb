/*
 * Function: ?_db_Update_Base@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD_N@Z
 * Address: 0x1401A9D90
 */

char __fastcall CMainThread::_db_Update_Base(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pSzQuery, bool bCheck<PERSON>owHigh)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int16 v8; // ax@16
  __int16 v9; // ax@19
  __int16 v10; // ax@20
  __int16 v11; // ax@37
  unsigned int v12; // eax@39
  __int64 v14; // [sp+0h] [bp-138h]@1
  unsigned int v15; // [sp+20h] [bp-118h]@37
  unsigned int v16; // [sp+28h] [bp-110h]@37
  unsigned int v17; // [sp+30h] [bp-108h]@37
  unsigned int v18; // [sp+38h] [bp-100h]@37
  unsigned int v19; // [sp+40h] [bp-F8h]@37
  unsigned __int64 v20; // [sp+48h] [bp-F0h]@37
  char Source; // [sp+60h] [bp-D8h]@4
  char v22; // [sp+61h] [bp-D7h]@4
  char *Dest; // [sp+E8h] [bp-50h]@4
  unsigned int j; // [sp+F0h] [bp-48h]@12
  unsigned int v25; // [sp+F4h] [bp-44h]@24
  unsigned int v26; // [sp+F8h] [bp-40h]@24
  unsigned int v27; // [sp+FCh] [bp-3Ch]@33
  int v28; // [sp+108h] [bp-30h]@19
  __int64 v29; // [sp+110h] [bp-28h]@37
  __int64 v30; // [sp+118h] [bp-20h]@37
  unsigned __int64 v31; // [sp+120h] [bp-18h]@4
  unsigned int v32; // [sp+148h] [bp+10h]@1
  _AVATOR_DATA *v33; // [sp+150h] [bp+18h]@1
  _AVATOR_DATA *v34; // [sp+158h] [bp+20h]@1

  v34 = pOldData;
  v33 = pNewData;
  v32 = dwSerial;
  v6 = &v14;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v31 = (unsigned __int64)&v14 ^ _security_cookie;
  Source = 0;
  memset(&v22, 0, 0x7Fui64);
  Dest = pSzQuery;
  sprintf(pSzQuery, "UPDATE tbl_base SET ");
  if ( v34->dbAvator.m_byLevel != v33->dbAvator.m_byLevel )
  {
    sprintf(&Source, "Lv=%d,", v33->dbAvator.m_byLevel);
    strcat_0(Dest, &Source);
  }
  if ( strcmp_0(v34->dbAvator.m_szClassCode, v33->dbAvator.m_szClassCode) )
  {
    v33->dbAvator.m_szClassCode[4] = 0;
    sprintf(&Source, "class='%s',", v33->dbAvator.m_szClassCode);
    strcat_0(Dest, &Source);
  }
  if ( v34->dbAvator.m_dwDalant != v33->dbAvator.m_dwDalant )
  {
    sprintf(&Source, "Dalant=%d,", v33->dbAvator.m_dwDalant);
    strcat_0(Dest, &Source);
  }
  if ( v34->dbAvator.m_dwGold != v33->dbAvator.m_dwGold )
  {
    sprintf(&Source, "Gold=%d,", v33->dbAvator.m_dwGold);
    strcat_0(Dest, &Source);
  }
  for ( j = 0; (signed int)j < 8; ++j )
  {
    if ( _EQUIPKEY::IsFilled(&v33->dbAvator.m_EquipKey[j]) )
    {
      if ( _EQUIPKEY::IsFilled(&v34->dbAvator.m_EquipKey[j]) )
      {
        v28 = _EQUIPKEY::CovDBKey(&v33->dbAvator.m_EquipKey[j]);
        v9 = _EQUIPKEY::CovDBKey(&v34->dbAvator.m_EquipKey[j]);
        if ( v28 != v9 )
        {
          v10 = _EQUIPKEY::CovDBKey(&v33->dbAvator.m_EquipKey[j]);
          sprintf(&Source, "EK%d=%d,", j, (unsigned int)v10);
          strcat_0(Dest, &Source);
        }
        if ( v33->dbAvator.m_dwFixEquipLv[j] != v34->dbAvator.m_dwFixEquipLv[j] )
        {
          sprintf(&Source, "EU%d=%d,", j, v33->dbAvator.m_dwFixEquipLv[j]);
          strcat_0(Dest, &Source);
        }
        if ( v33->dbAvator.m_byCsMethod[j] )
        {
          v25 = 0;
          v26 = 0;
          if ( v33->dbAvator.m_byCsMethod[j] == 1 )
          {
            v25 = v33->dbAvator.m_dwET[j] - Time;
            v26 = v34->dbAvator.m_dwET[j] - Time;
          }
          else if ( v33->dbAvator.m_byCsMethod[j] == 2 )
          {
            v25 = v33->dbAvator.m_dwET[j];
            v26 = v34->dbAvator.m_dwET[j];
          }
          if ( v25 != v26 )
          {
            sprintf(&Source, "ET%d=%d,", j, v25);
            strcat_0(Dest, &Source);
          }
        }
        if ( v33->dbAvator.m_lnUID[j] != v34->dbAvator.m_lnUID[j] )
        {
          sprintf(&Source, "ES%d=%I64d,", j, v33->dbAvator.m_lnUID[j]);
          strcat_0(Dest, &Source);
        }
      }
      else
      {
        v27 = 0;
        if ( v33->dbAvator.m_byCsMethod[j] == 1 )
        {
          v27 = v33->dbAvator.m_dwET[j] - Time;
        }
        else if ( v33->dbAvator.m_byCsMethod[j] == 2 )
        {
          v27 = v33->dbAvator.m_dwET[j];
        }
        v29 = (signed int)j;
        v30 = (signed int)j;
        v11 = _EQUIPKEY::CovDBKey(&v33->dbAvator.m_EquipKey[j]);
        v20 = v33->dbAvator.m_lnUID[v29];
        v19 = j;
        v18 = v27;
        v17 = j;
        v16 = v33->dbAvator.m_dwFixEquipLv[v30];
        v15 = j;
        sprintf(&Source, "EK%d=%d,EU%d=%d,ET%d=%d,ES%d=%I64d,", j, (unsigned int)v11);
        strcat_0(Dest, &Source);
      }
    }
    else if ( _EQUIPKEY::IsFilled(&v34->dbAvator.m_EquipKey[j]) )
    {
      v8 = _EQUIPKEY::CovDBKey(&v33->dbAvator.m_EquipKey[j]);
      sprintf(&Source, "EK%d=%d,", j, (unsigned int)v8);
      strcat_0(Dest, &Source);
    }
  }
  sprintf(&Source, "Slot=%d,", v33->dbAvator.m_bySlotIndex);
  strcat_0(Dest, &Source);
  v12 = GetKorLocalTime();
  sprintf(&Source, "LastConnTime=%d WHERE Serial=%d", v12, v32);
  strcat_0(Dest, &Source);
  return 1;
}
