/*
 * Function: ?pc_LinkBoardRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400FEFC0
 */

void __fastcall CPlayer::pc_LinkBoardRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CPlayer *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v4->m_bLinkBoardDownload )
  {
    CPlayer::SendMsg_LinkBoardDownloadResult(v4);
    v4->m_bLinkBoardDownload = 1;
  }
}
