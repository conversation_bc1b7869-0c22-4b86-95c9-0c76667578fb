/*
 * Function: ?Add@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAAEIKKPEAPEAVCGuildBattleSchedule@2@AEAI@Z
 * Address: 0x1403D9180
 */

char __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::Add(GUILD_BATTLE::CGuildBattleScheduleManager *this, unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, GUILD_BATTLE::CGuildBattleSchedule **ppkSchedule, unsigned int *uiSLID)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CGuildBattleSchedule **v10; // [sp+20h] [bp-18h]@4
  unsigned int *v11; // [sp+28h] [bp-10h]@4
  GUILD_BATTLE::CGuildBattleScheduleManager *v12; // [sp+40h] [bp+8h]@1

  v12 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11 = uiSLID;
  v10 = ppkSchedule;
  return GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Add(
           v12->m_pkTomorrowSchedule,
           uiFieldInx,
           dwStartTimeInx,
           dwElapseTimeCnt,
           ppkSchedule,
           uiSLID);
}
