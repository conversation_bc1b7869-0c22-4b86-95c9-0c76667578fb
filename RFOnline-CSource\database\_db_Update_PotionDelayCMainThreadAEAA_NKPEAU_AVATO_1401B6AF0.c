/*
 * Function: ?_db_Update_PotionDelay@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEADH@Z
 * Address: 0x1401B6AF0
 */

char __fastcall CMainThread::_db_Update_PotionDelay(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szPotionDelayQuery, int nSize)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  bool v8; // zf@9
  size_t v9; // rax@13
  __int64 v11; // [sp+0h] [bp-F8h]@1
  unsigned int v12; // [sp+20h] [bp-D8h]@11
  char DstBuf; // [sp+40h] [bp-B8h]@4
  char v14; // [sp+41h] [bp-B7h]@4
  int v15; // [sp+C4h] [bp-34h]@4
  DWORD v16; // [sp+C8h] [bp-30h]@4
  unsigned int v17; // [sp+CCh] [bp-2Ch]@9
  int nIndex; // [sp+D0h] [bp-28h]@4
  unsigned __int64 v19; // [sp+E0h] [bp-18h]@4
  unsigned int v20; // [sp+108h] [bp+10h]@1
  _AVATOR_DATA *v21; // [sp+110h] [bp+18h]@1

  v21 = pNewData;
  v20 = dwSerial;
  v6 = &v11;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v19 = (unsigned __int64)&v11 ^ _security_cookie;
  DstBuf = 0;
  memset(&v14, 0, 0x7Fui64);
  sprintf_s(szPotionDelayQuery, nSize, "UPDATE tbl_potion_delay Set ");
  v15 = strlen_0(szPotionDelayQuery);
  v16 = timeGetTime();
  for ( nIndex = 0; nIndex < 38; ++nIndex )
  {
    if ( CPotionMgr::IsPotionDelayUseIndex(&g_PotionMgr, nIndex) && v21->dbPotionNextUseTime.dwPotionNextUseTime[nIndex] )
    {
      if ( v21->dbPotionNextUseTime.dwPotionNextUseTime[nIndex] < v16 )
      {
        v21->dbPotionNextUseTime.dwPotionNextUseTime[nIndex] = 0;
        v17 = 0;
      }
      else
      {
        v8 = v21->dbPotionNextUseTime.dwPotionNextUseTime[nIndex] == 0;
        v17 = v21->dbPotionNextUseTime.dwPotionNextUseTime[nIndex] - v16;
      }
      v12 = v17;
      sprintf_s(&DstBuf, 0x80ui64, "PD%d=%u,", (unsigned int)nIndex);
      strcat_s(szPotionDelayQuery, nSize, &DstBuf);
    }
  }
  v9 = strlen_0(szPotionDelayQuery);
  if ( v9 <= v15 )
  {
    memset_0(szPotionDelayQuery, 0, v15);
  }
  else
  {
    sprintf_s(&DstBuf, 0x80ui64, "WHERE Serial = %d", v20);
    szPotionDelayQuery[strlen_0(szPotionDelayQuery) - 1] = 32;
    strcat_s(szPotionDelayQuery, nSize, &DstBuf);
  }
  return 1;
}
