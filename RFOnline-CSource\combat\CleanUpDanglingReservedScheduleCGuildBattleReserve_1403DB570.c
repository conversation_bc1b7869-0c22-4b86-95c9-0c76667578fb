/*
 * Function: ?CleanUpDanglingReservedSchedule@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403DB570
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::CleanUpDanglingReservedSchedule(GUILD_BATTLE::CGuildBattleReservedSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@9
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  unsigned int v7; // [sp+24h] [bp-14h]@9
  GUILD_BATTLE::CGuildBattleReservedSchedule *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 0x17; ++j )
  {
    if ( v8->m_pkSchedule[j] )
    {
      if ( !v8->m_bUseField[j] )
      {
        GUILD_BATTLE::CGuildBattleSchedule::Clear(v8->m_pkSchedule[j]);
        v7 = GUILD_BATTLE::CGuildBattleSchedule::GetSID(v8->m_pkSchedule[j]);
        v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v3,
          "CGuildBattleReservedSchedule::CleanUpDanglingReservedSchedule() : m_pkSchedule[%u]->Clear() ID(%u)!",
          j,
          v7);
      }
    }
  }
  return 1;
}
