/*
 * Function: ?_db_load_punishment@CMainThread@@AEAAEKPEAU_AVATOR_DATA@@@Z
 * Address: 0x1401A9970
 */

char __fastcall CMainThread::_db_load_punishment(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pCon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  CMainThread *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CRFWorldDatabase::Select_Punishment(
         v8->m_pWorldDB,
         dwSerial,
         pCon->dbAvator.m_dwElectSerial,
         pCon->dbAvator.m_dwPunishment);
  if ( v7 == 1 )
    result = 24;
  else
    result = 0;
  return result;
}
