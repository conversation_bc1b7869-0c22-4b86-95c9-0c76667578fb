/*
 * Function: ?Attack<PERSON>en@CAttack@@QEAAXPEAU_attack_param@@_N1@Z
 * Address: 0x140169520
 */

void __usercall CAttack::AttackGen(CAttack *this@<rcx>, _attack_param *pParam@<rdx>, bool bMustMiss@<r8b>, bool bUseEffBullet@<r9b>, float a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@8
  float v8; // xmm0_4@14
  float v9; // xmm0_4@14
  CCharacter *v10; // r10@21
  int v11; // eax@21
  int v12; // eax@30
  float v13; // xmm0_4@30
  CPvpUserAndGuildRankingSystem *v14; // rax@36
  float v15; // xmm0_4@45
  float v16; // xmm0_4@45
  float v17; // xmm0_4@45
  float v18; // xmm0_4@45
  float v19; // xmm0_4@47
  float v20; // xmm0_4@48
  CCharacter **v21; // rax@55
  CCharacter **v22; // rcx@55
  CCharacter **v23; // rdx@55
  CCharacter **v24; // r8@55
  CCharacter **v25; // rax@56
  CCharacter **v26; // rcx@56
  CCharacter **v27; // rdx@56
  CCharacter **v28; // r8@56
  CCharacter **v29; // rdx@59
  float *v30; // rcx@62
  __int64 v31; // [sp+0h] [bp-D8h]@1
  bool bUnit[8]; // [sp+20h] [bp-B8h]@45
  bool bBackAttack; // [sp+28h] [bp-B0h]@55
  char v34; // [sp+30h] [bp-A8h]@4
  char v35; // [sp+31h] [bp-A7h]@5
  float v36; // [sp+34h] [bp-A4h]@14
  float v37; // [sp+38h] [bp-A0h]@14
  float v38; // [sp+3Ch] [bp-9Ch]@30
  float v39; // [sp+40h] [bp-98h]@30
  CCharacter *v40; // [sp+48h] [bp-90h]@35
  char v41; // [sp+50h] [bp-88h]@36
  CCharacter *v42; // [sp+58h] [bp-80h]@43
  float v43; // [sp+60h] [bp-78h]@45
  float v44; // [sp+64h] [bp-74h]@45
  float v45; // [sp+68h] [bp-70h]@45
  float v46; // [sp+6Ch] [bp-6Ch]@45
  float v47; // [sp+70h] [bp-68h]@45
  float v48; // [sp+74h] [bp-64h]@8
  float v49; // [sp+78h] [bp-60h]@14
  float v50; // [sp+7Ch] [bp-5Ch]@14
  int v51; // [sp+80h] [bp-58h]@21
  CCharacter **v52; // [sp+88h] [bp-50h]@21
  CGameObjectVtbl *v53; // [sp+90h] [bp-48h]@21
  unsigned int dwSerial; // [sp+98h] [bp-40h]@36
  int v55; // [sp+9Ch] [bp-3Ch]@36
  char v56; // [sp+A0h] [bp-38h]@36
  float v57; // [sp+A4h] [bp-34h]@45
  float v58; // [sp+A8h] [bp-30h]@45
  float v59; // [sp+ACh] [bp-2Ch]@45
  float v60; // [sp+B0h] [bp-28h]@45
  float v61; // [sp+B4h] [bp-24h]@45
  int v62; // [sp+B8h] [bp-20h]@49
  int nAttPnt; // [sp+BCh] [bp-1Ch]@55
  int v64; // [sp+C0h] [bp-18h]@56
  CCharacter **v65; // [sp+C8h] [bp-10h]@62
  CAttack *v66; // [sp+E0h] [bp+8h]@1
  _attack_param *v67; // [sp+E8h] [bp+10h]@1
  bool v68; // [sp+F0h] [bp+18h]@1
  bool v69; // [sp+F8h] [bp+20h]@1

  v69 = bUseEffBullet;
  v68 = bMustMiss;
  v67 = pParam;
  v66 = this;
  v5 = &v31;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v66->m_nDamagedObjNum = 0;
  v66->m_bIsCrtAtt = 0;
  v66->m_pp = pParam;
  v34 = 1;
  v66->m_nDamagedObjNum = 0;
  CCharacter::BreakStealth(v66->m_pAttChar);
  if ( v66->m_pp->pDst )
  {
    v35 = 0;
    if ( _effect_parameter::GetEff_State(&v66->m_pp->pDst->m_EP, 14) )
    {
      v35 = 1;
    }
    else
    {
      _effect_parameter::GetEff_Plus(&v66->m_pp->pDst->m_EP, 27);
      if ( a5 > 0.0 )
      {
        v7 = rand();
        a5 = (float)(v7 % 100);
        v48 = (float)(v7 % 100);
        _effect_parameter::GetEff_Plus(&v66->m_pp->pDst->m_EP, 27);
        if ( a5 > v48 )
          v35 = 1;
      }
    }
    if ( v35 )
    {
      if ( !v66->m_pp->bPassCount
        && !v66->m_pp->nClass
        && !((int (__fastcall *)(CCharacter *))v66->m_pp->pDst->vfptr->GetWeaponClass)(v66->m_pp->pDst) )
      {
        ((void (__fastcall *)(CCharacter *))v66->m_pp->pDst->vfptr->GetAttackRange)(v66->m_pp->pDst);
        v49 = a5;
        ((void (__fastcall *)(CCharacter *))v66->m_pAttChar->vfptr->GetWidth)(v66->m_pAttChar);
        v8 = v49 + (float)(a5 / 2.0);
        v50 = v8;
        _effect_parameter::GetEff_Plus(&v66->m_pp->pDst->m_EP, 4);
        v9 = v50 + v8;
        v36 = v9;
        GetSqrt(v66->m_pp->pDst->m_fCurPos, v66->m_pAttChar->m_fCurPos);
        v37 = v9;
        a5 = v36;
        if ( v36 >= v37 )
        {
          v66->m_DamList[0].m_pChar = v66->m_pp->pDst;
          v66->m_DamList[0].m_nDamage = -1;
          v66->m_nDamagedObjNum = 1;
          CCharacter::SendMsg_AttackActEffect(v66->m_pAttChar, 0, v66->m_pp->pDst);
          return;
        }
      }
      _effect_parameter::GetEff_Plus(&v66->m_pp->pDst->m_EP, 27);
      if ( a5 > 0.0 )
      {
        v66->m_DamList[0].m_pChar = v66->m_pp->pDst;
        v66->m_DamList[0].m_nDamage = 0;
        v66->m_nDamagedObjNum = 1;
        return;
      }
    }
    if ( v66->m_pp->bMatchless )
    {
      v66->m_DamList[0].m_pChar = v66->m_pp->pDst;
      v66->m_DamList[0].m_nDamage = ((int (__fastcall *)(CCharacter *))v66->m_pp->pDst->vfptr->GetHP)(v66->m_pp->pDst);
      v66->m_nDamagedObjNum = 1;
      return;
    }
    if ( _effect_parameter::GetEff_State(&v66->m_pp->pDst->m_EP, 8) )
    {
      v34 = 0;
    }
    else
    {
      v51 = rand() % 100;
      v52 = &v66->m_pp->pDst;
      v10 = v66->m_pAttChar;
      v53 = v66->m_pAttChar->vfptr;
      v11 = ((int (__fastcall *)(CCharacter *, CCharacter *, _QWORD, _QWORD))v53->GetGenAttackProb)(
              v10,
              *v52,
              *((_DWORD *)v52 + 2),
              *((_BYTE *)v52 + 100));
      if ( v51 >= v11 )
        v34 = 0;
    }
    if ( v34 && v68 )
      v34 = 0;
    if ( !v34 )
    {
      v66->m_DamList[0].m_pChar = v66->m_pp->pDst;
      v66->m_DamList[0].m_nDamage = 0;
      v66->m_nDamagedObjNum = 1;
      return;
    }
  }
  v38 = (float)(v66->m_pp->nAddAttPnt + CAttack::_CalcGenAttPnt(v66, 0));
  v12 = CAttack::_CalcGenAttPnt(v66, v69);
  v13 = (float)(v66->m_pp->nAddAttPnt + v12);
  v39 = (float)(v66->m_pp->nAddAttPnt + v12);
  if ( !v66->m_pAttChar->m_ObjID.m_byID
    && (CHolyStoneSystem::GetDestroyerSerial(&g_HolySys) == v66->m_pAttChar->m_dwObjSerial
     || CPlayer::IsLastAttBuff((CPlayer *)v66->m_pAttChar)) )
  {
    v38 = v38 * 1.3;
    v13 = v39 * 1.3;
    v39 = v39 * 1.3;
  }
  if ( !v66->m_pAttChar->m_ObjID.m_byID )
  {
    v40 = v66->m_pAttChar;
    if ( !BYTE2(v40[1].m_fCurPos[2]) )
    {
      dwSerial = CPlayerDB::GetCharSerial((CPlayerDB *)&v40[1].m_fOldPos[2]);
      v55 = CPlayerDB::GetRaceCode((CPlayerDB *)&v40[1].m_fOldPos[2]);
      v14 = CPvpUserAndGuildRankingSystem::Instance();
      v41 = CPvpUserAndGuildRankingSystem::GetBossType(v14, v55, dwSerial);
      v56 = v41;
      if ( v41 )
      {
        if ( v56 == 2 || v56 == 6 )
        {
          v38 = v38 * 1.2;
          v13 = v39 * 1.2;
          v39 = v39 * 1.2;
        }
      }
      else
      {
        v38 = v38 * 1.3;
        v13 = v39 * 1.3;
        v39 = v39 * 1.3;
      }
    }
  }
  if ( v66->m_pAttChar->m_ObjID.m_byID == 3 )
  {
    v42 = v66->m_pAttChar;
    if ( v42 )
    {
      if ( *(_QWORD *)&v42[1].m_bLive )
      {
        bUnit[0] = 0;
        CAttack::GetAttackFC(v66, *(CPlayer **)&v42[1].m_bLive, 2, 1, 0);
        v57 = v13;
        _effect_parameter::GetEff_Rate((_effect_parameter *)(*(_QWORD *)&v42[1].m_bLive + 1800i64), 57);
        v15 = v57 * (float)(v13 - 1.0);
        v43 = v15;
        bUnit[0] = 0;
        CAttack::GetAttackFC(v66, *(CPlayer **)&v42[1].m_bLive, 0, 1, 0);
        v58 = v15;
        _effect_parameter::GetEff_Rate((_effect_parameter *)(*(_QWORD *)&v42[1].m_bLive + 1800i64), 58);
        v16 = v58 * (float)(v15 - 1.0);
        v44 = v16;
        bUnit[0] = 0;
        CAttack::GetAttackFC(v66, *(CPlayer **)&v42[1].m_bLive, 0, 0, 0);
        v59 = v16;
        _effect_parameter::GetEff_Rate((_effect_parameter *)(*(_QWORD *)&v42[1].m_bLive + 1800i64), 59);
        v17 = v59 * (float)(v16 - 1.0);
        v45 = v17;
        bUnit[0] = 0;
        CAttack::GetAttackFC(v66, *(CPlayer **)&v42[1].m_bLive, 1, 1, 0);
        v60 = v17;
        _effect_parameter::GetEff_Rate((_effect_parameter *)(*(_QWORD *)&v42[1].m_bLive + 1800i64), 60);
        v18 = v60 * (float)(v17 - 1.0);
        v46 = v18;
        bUnit[0] = 0;
        CAttack::GetAttackFC(v66, *(CPlayer **)&v42[1].m_bLive, 1, 0, 0);
        v61 = v18;
        _effect_parameter::GetEff_Rate((_effect_parameter *)(*(_QWORD *)&v42[1].m_bLive + 1800i64), 61);
        v47 = v61 * (float)(v18 - 1.0);
        v38 = (float)((float)((float)((float)(v38 + v43) + v44) + v45) + v46) + v47;
        v13 = (float)((float)((float)((float)(v39 + v43) + v44) + v45) + v46) + v47;
        v39 = (float)((float)((float)((float)(v39 + v43) + v44) + v45) + v46) + v47;
      }
    }
  }
  if ( v66->m_pp->nWpType == 7 )
  {
    _effect_parameter::GetEff_Rate(&v66->m_pAttChar->m_EP, 29);
    v19 = v38 * v13;
    v38 = v19;
    _effect_parameter::GetEff_Rate(&v66->m_pAttChar->m_EP, 29);
    v39 = v39 * v19;
  }
  else
  {
    _effect_parameter::GetEff_Rate(&v66->m_pAttChar->m_EP, v66->m_pp->nClass);
    v20 = v38 * v13;
    v38 = v20;
    _effect_parameter::GetEff_Rate(&v66->m_pAttChar->m_EP, v66->m_pp->nClass);
    v39 = v39 * v20;
  }
  v62 = v67->nAttactType;
  if ( v62 < 0 )
    goto LABEL_68;
  if ( v62 <= 2 )
  {
    v66->m_DamList[0].m_pChar = v66->m_pp->pDst;
    if ( v69 )
    {
      v21 = &v66->m_pp->pDst;
      v22 = &v66->m_pp->pDst;
      v23 = &v66->m_pp->pDst;
      v24 = &v66->m_pp->pDst;
      nAttPnt = (signed int)ffloor(v39);
      bBackAttack = *((_BYTE *)v21 + 100);
      *(_QWORD *)bUnit = *v22;
      v66->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                      v66->m_pAttChar,
                                      nAttPnt,
                                      *((_DWORD *)v24 + 2),
                                      *((_DWORD *)v23 + 3),
                                      *(CCharacter **)bUnit,
                                      bBackAttack);
    }
    else
    {
      v25 = &v66->m_pp->pDst;
      v26 = &v66->m_pp->pDst;
      v27 = &v66->m_pp->pDst;
      v28 = &v66->m_pp->pDst;
      v64 = (signed int)ffloor(v38);
      bBackAttack = *((_BYTE *)v25 + 100);
      *(_QWORD *)bUnit = *v26;
      v66->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                      v66->m_pAttChar,
                                      v64,
                                      *((_DWORD *)v28 + 2),
                                      *((_DWORD *)v27 + 3),
                                      *(CCharacter **)bUnit,
                                      bBackAttack);
    }
    v66->m_nDamagedObjNum = 1;
    goto LABEL_65;
  }
  if ( v62 == 5 )
  {
    if ( v66->m_pp->nExtentRange > 0 )
    {
      v29 = &v66->m_pp->pDst;
      bBackAttack = v69;
      *(_DWORD *)bUnit = (signed int)ffloor(v39);
      CAttack::FlashDamageProc(v66, *((_DWORD *)v29 + 9), (signed int)ffloor(v38), 90, *(int *)bUnit, v69);
    }
    goto LABEL_65;
  }
  if ( v62 == 6 )
  {
    if ( v66->m_pp->nExtentRange > 0 )
    {
      v30 = v66->m_pp->fArea;
      v65 = &v66->m_pp->pDst;
      bBackAttack = v69;
      *(_DWORD *)bUnit = (signed int)ffloor(v39);
      CAttack::AreaDamageProc(v66, *((_DWORD *)v65 + 9), (signed int)ffloor(v38), v30, *(int *)bUnit, v69);
    }
  }
  else
  {
LABEL_68:
    v66->m_DamList[0].m_pChar = v66->m_pp->pDst;
    v66->m_DamList[0].m_nDamage = 0;
    v66->m_nDamagedObjNum = 1;
  }
LABEL_65:
  CAttack::CalcAvgDamage(v66);
}
