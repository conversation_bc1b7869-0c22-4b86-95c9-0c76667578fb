/*
 * Function: ??0CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403CD260
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::CGuildBattleReservedScheduleListManager(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_uiMapCnt = 0;
  `eh vector constructor iterator'(
    v4->m_kList,
    0x10ui64,
    2,
    (void (__cdecl *)(void *))GUILD_BATTLE::CReservedGuildScheduleDayGroup::CReservedGuildScheduleDayGroup,
    (void (__cdecl *)(void *))GUILD_BATTLE::CReservedGuildScheduleDayGroup::~CReservedGuildScheduleDayGroup);
  v4->m_pkToday = v4->m_kList;
  v4->m_pkTomorrow = &v4->m_kList[1];
}
