/*
 * Function: ?Clear@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403CD970
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Clear(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  GUILD_BATTLE::CReservedGuildScheduleDayGroup::Clear(v4->m_pkToday);
  GUILD_BATTLE::CReservedGuildScheduleDayGroup::Clear(v4->m_pkTomorrow);
}
