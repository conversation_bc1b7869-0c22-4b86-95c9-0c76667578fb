/*
 * Function: ?_db_Update_PvpOrder<PERSON>iew@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD1@Z
 * Address: 0x1401B4EA0
 */

char __fastcall CMainThread::_db_Update_PvpOrderView(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szQuery, char *szError)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v9; // eax@7
  int v10; // ecx@7
  size_t v11; // rax@31
  __int64 v12; // [sp+0h] [bp-108h]@1
  int v13; // [sp+20h] [bp-E8h]@7
  int v14; // [sp+28h] [bp-E0h]@7
  int v15; // [sp+30h] [bp-D8h]@7
  int v16; // [sp+38h] [bp-D0h]@7
  char Dest; // [sp+50h] [bp-B8h]@4
  char v18; // [sp+51h] [bp-B7h]@4
  size_t Size; // [sp+D4h] [bp-34h]@4
  unsigned int j; // [sp+E0h] [bp-28h]@20
  unsigned __int64 v21; // [sp+F0h] [bp-18h]@4
  unsigned int v22; // [sp+118h] [bp+10h]@1
  _AVATOR_DATA *v23; // [sp+120h] [bp+18h]@1
  _AVATOR_DATA *v24; // [sp+128h] [bp+20h]@1

  v24 = pOldData;
  v23 = pNewData;
  v22 = dwSerial;
  v6 = &v12;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v21 = (unsigned __int64)&v12 ^ _security_cookie;
  Dest = 0;
  memset(&v18, 0, 0x7Fui64);
  sprintf(szQuery, "update [dbo].[tbl_pvporderview] set ");
  LODWORD(Size) = strlen_0(szQuery);
  if ( v23->dbPvpOrderView.tUpdatedate != v24->dbPvpOrderView.tUpdatedate )
  {
    *(size_t *)((char *)&Size + 4) = (size_t)localtime_2(&v23->dbPvpOrderView.tUpdatedate);
    if ( !*(size_t *)((char *)&Size + 4) )
    {
      sprintf(szError, "localtime( dbPvpOrderView.tUpdatedate(%d) ) NULL!", v23->dbPvpOrderView.tUpdatedate);
      return 0;
    }
    v9 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 16) + 1;
    v10 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 20);
    v16 = **(_DWORD **)((char *)&Size + 4);
    v15 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 4);
    v14 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 8);
    v13 = *(_DWORD *)(*(size_t *)((char *)&Size + 4) + 12);
    sprintf(&Dest, "[UpdateDate]='%04u-%02u-%02u %02u:%02u:%02u',", (unsigned int)(v10 + 1900), (unsigned int)v9);
    strcat_0(szQuery, &Dest);
  }
  if ( v23->dbPvpOrderView.nDeath != v24->dbPvpOrderView.nDeath )
  {
    sprintf(&Dest, "[Death]= %d,", v23->dbPvpOrderView.nDeath);
    strcat_0(szQuery, &Dest);
  }
  if ( v23->dbPvpOrderView.nKill != v24->dbPvpOrderView.nKill )
  {
    sprintf(&Dest, "[Kill]= %d,", v23->dbPvpOrderView.nKill);
    strcat_0(szQuery, &Dest);
  }
  if ( v23->dbPvpOrderView.dTodayStacked != v24->dbPvpOrderView.dTodayStacked )
  {
    sprintf(&Dest, "[TodayStacked]= %.10f,", v23->dbPvpOrderView.dTodayStacked);
    strcat_0(szQuery, &Dest);
  }
  if ( v23->dbPvpOrderView.dPvpPoint != v24->dbPvpOrderView.dPvpPoint )
  {
    sprintf(&Dest, "[PvpPoint]= %.10f,", v23->dbPvpOrderView.dPvpPoint);
    strcat_0(szQuery, &Dest);
  }
  if ( v23->dbPvpOrderView.dPvpTempCash != v24->dbPvpOrderView.dPvpTempCash )
  {
    sprintf(&Dest, "[PvpTempCash]= %.10f,", v23->dbPvpOrderView.dPvpTempCash);
    strcat_0(szQuery, &Dest);
  }
  if ( v23->dbPvpOrderView.dPvpCash != v24->dbPvpOrderView.dPvpCash )
  {
    sprintf(&Dest, "[PvpCash]= %.10f,", v23->dbPvpOrderView.dPvpCash);
    strcat_0(szQuery, &Dest);
  }
  for ( j = 0; (signed int)j < 10; ++j )
  {
    if ( v23->dbPvpOrderView.dwKillerSerial[j] != v24->dbPvpOrderView.dwKillerSerial[j] )
    {
      sprintf(&Dest, "[KillerSerial%d] = %d,", j, v23->dbPvpOrderView.dwKillerSerial[j]);
      strcat_0(szQuery, &Dest);
    }
  }
  if ( v23->dbPvpOrderView.byContHaveCash != v24->dbPvpOrderView.byContHaveCash )
  {
    sprintf(&Dest, "[ContHaveCash] = %d,", v23->dbPvpOrderView.byContHaveCash);
    strcat_0(szQuery, &Dest);
  }
  if ( v23->dbPvpOrderView.byContLoseCash != v24->dbPvpOrderView.byContLoseCash )
  {
    sprintf(&Dest, "[ContLoseCash] = %d,", v23->dbPvpOrderView.byContLoseCash);
    strcat_0(szQuery, &Dest);
  }
  if ( v23->dbPvpOrderView.bRaceWarRecvr != v24->dbPvpOrderView.bRaceWarRecvr )
  {
    sprintf(&Dest, "[RaceWarRecvr] = %d ", v23->dbPvpOrderView.bRaceWarRecvr);
    strcat_0(szQuery, &Dest);
  }
  v11 = strlen_0(szQuery);
  if ( v11 <= (unsigned int)Size )
  {
    memset_0(szQuery, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Dest, "where serial=%d", v22);
    szQuery[strlen_0(szQuery) - 1] = 32;
    strcat_0(szQuery, &Dest);
  }
  return 1;
}
