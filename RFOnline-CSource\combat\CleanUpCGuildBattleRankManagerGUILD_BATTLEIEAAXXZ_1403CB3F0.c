/*
 * Function: ?CleanUp@CGuildBattleRankManager@GUILD_BATTLE@@IEAAXXZ
 * Address: 0x1403CB3F0
 */

void __fastcall GUILD_BATTLE::CGuildBattleRankManager::CleanUp(GUILD_BATTLE::CGuildBattleRankManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@5
  void *v5; // [sp+28h] [bp-20h]@8
  void *v6; // [sp+30h] [bp-18h]@10
  GUILD_BATTLE::CGuildBattleRankManager *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_ppkList )
  {
    for ( j = 0; j < 3; ++j )
    {
      if ( v7->m_ppkList[j] )
      {
        v5 = v7->m_ppkList[j];
        operator delete[](v5);
      }
    }
    v6 = v7->m_ppkList;
    operator delete[](v6);
  }
}
