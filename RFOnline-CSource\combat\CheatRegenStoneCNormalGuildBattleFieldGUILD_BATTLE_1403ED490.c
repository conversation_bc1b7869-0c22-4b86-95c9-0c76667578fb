/*
 * Function: ?CheatRegenStone@CNormalGuildBattleField@GUILD_BATTLE@@QEAAHI@Z
 * Address: 0x1403ED490
 */

int __fastcall GUILD_BATTLE::CNormalGuildBattleField::CheatRegenStone(GUILD_BATTLE::CNormalGuildBattleField *this, unsigned int uiPos)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@7
  GUILD_BATTLE::CNormalGuildBattleField *v7; // [sp+40h] [bp+8h]@1
  unsigned int v8; // [sp+48h] [bp+10h]@1

  v8 = uiPos;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_bInit && v7->m_uiRegenPosCnt > uiPos )
  {
    for ( j = 0; j < v7->m_uiRegenPosCnt; ++j )
      CGravityStoneRegener::CheatClearRegenState(&v7->m_pkRegenPos[j]);
    result = CGravityStoneRegener::Regen(&v7->m_pkRegenPos[v8]);
  }
  else
  {
    result = -1;
  }
  return result;
}
