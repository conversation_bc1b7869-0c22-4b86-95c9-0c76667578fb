/*
 * Function: ?_db_Update_GoldBoxItem@CMainThread@@AEAAEHPEAU_db_golden_box_item@@0@Z
 * Address: 0x1401B87D0
 */

char __fastcall CMainThread::_db_Update_GoldBoxItem(CMainThread *this, int nDBSerial, _db_golden_box_item *pNewData, _db_golden_box_item *pOldData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CGoldenBoxItemMgr *v6; // rax@9
  char v7; // al@9
  size_t v8; // rax@24
  char result; // al@25
  __int64 v10; // [sp+0h] [bp-908h]@1
  int v11; // [sp+20h] [bp-8E8h]@19
  char Dest; // [sp+40h] [bp-8C8h]@4
  char v13; // [sp+41h] [bp-8C7h]@4
  char v14; // [sp+DFh] [bp-829h]@25
  char DstBuf; // [sp+E0h] [bp-828h]@4
  char v16; // [sp+E1h] [bp-827h]@4
  int v17; // [sp+8E4h] [bp-24h]@4
  int j; // [sp+8E8h] [bp-20h]@8
  unsigned int k; // [sp+8ECh] [bp-1Ch]@16
  unsigned __int64 v20; // [sp+8F8h] [bp-10h]@4
  CMainThread *v21; // [sp+910h] [bp+8h]@1
  int v22; // [sp+918h] [bp+10h]@1
  _db_golden_box_item *v23; // [sp+920h] [bp+18h]@1
  _db_golden_box_item *v24; // [sp+928h] [bp+20h]@1

  v24 = pOldData;
  v23 = pNewData;
  v22 = nDBSerial;
  v21 = this;
  v4 = &v10;
  for ( i = 576i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v20 = (unsigned __int64)&v10 ^ _security_cookie;
  Dest = 0;
  memset(&v13, 0, 0x7Fui64);
  DstBuf = 0;
  memset(&v16, 0, 0x7FFui64);
  sprintf_s(&DstBuf, 0x800ui64, "UPDATE tbl_GoldBoxItem Set ");
  v17 = strlen_0(&DstBuf);
  if ( v23->bydck != v24->bydck )
  {
    sprintf(&Dest, "DCK=%d,", v23->bydck);
    strcat_s(&DstBuf, 0x800ui64, &Dest);
  }
  if ( v23->dwStarterBoxCnt != v24->dwStarterBoxCnt )
  {
    sprintf(&Dest, "StarterBoxMax=%d,", v23->dwStarterBoxCnt);
    strcat_s(&DstBuf, 0x800ui64, &Dest);
  }
  for ( j = 0; ; ++j )
  {
    v6 = CGoldenBoxItemMgr::Instance();
    v7 = CGoldenBoxItemMgr::GetLoopCount(v6);
    if ( j >= (unsigned __int8)v7 )
      break;
    if ( v23->nBoxcode[j] != v24->nBoxcode[j] )
    {
      sprintf(&Dest, "BoxItemK_%d=%ld,", (unsigned int)(j + 1), v23->nBoxcode[j]);
      strcat_s(&DstBuf, 0x800ui64, &Dest);
    }
    if ( v23->wBoxMax[j] != v24->wBoxMax[j] )
    {
      sprintf(&Dest, "BoxItemMax_%d=%d,", (unsigned int)(j + 1), v23->wBoxMax[j]);
      strcat_s(&DstBuf, 0x800ui64, &Dest);
    }
    if ( v23->bygolden_item_num[j] != v24->bygolden_item_num[j] )
    {
      sprintf(&Dest, "LimItemNum_%d=%d,", (unsigned int)(j + 1), v23->bygolden_item_num[j]);
      strcat_s(&DstBuf, 0x800ui64, &Dest);
    }
    for ( k = 0; (signed int)k < v23->bygolden_item_num[j]; ++k )
    {
      if ( v23->List[j][k].ncode != v24->List[j][k].ncode )
      {
        v11 = v23->List[j][k].ncode;
        sprintf(&Dest, "K%d_%d=%d,", (unsigned int)(j + 1), k);
        strcat_s(&DstBuf, 0x800ui64, &Dest);
      }
      if ( v23->List[j][k].wcount != v24->List[j][k].wcount )
      {
        v11 = v23->List[j][k].wcount;
        sprintf(&Dest, "N%d_%d=%d,", (unsigned int)(j + 1), k);
        strcat_s(&DstBuf, 0x800ui64, &Dest);
      }
    }
  }
  v8 = strlen_0(&DstBuf);
  if ( v8 <= v17 )
  {
    memset_0(&DstBuf, 0, v17);
    result = 0;
  }
  else
  {
    *(&v14 + strlen_0(&DstBuf)) = 32;
    sprintf_s(&Dest, 0x80ui64, "WHERE serial=%d", (unsigned int)v22);
    strcat_s(&DstBuf, 0x800ui64, &Dest);
    CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v21->m_pWorldDB->vfptr, &DstBuf, 1);
    result = 0;
  }
  return result;
}
