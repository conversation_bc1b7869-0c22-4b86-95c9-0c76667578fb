/*
 * Function: ?_db_Update_NpcData@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@PEAD@Z
 * Address: 0x1401AE3F0
 */

char __fastcall CMainThread::_db_Update_NpcData(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pAvatorData, char *pSzNpcQuery)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-138h]@1
  unsigned int v8; // [sp+20h] [bp-118h]@4
  unsigned int v9; // [sp+28h] [bp-110h]@4
  unsigned int v10; // [sp+30h] [bp-108h]@4
  unsigned int v11; // [sp+38h] [bp-100h]@4
  int v12; // [sp+40h] [bp-F8h]@4
  unsigned int v13; // [sp+58h] [bp-E0h]@4
  unsigned int v14; // [sp+5Ch] [bp-DCh]@4
  unsigned int v15; // [sp+60h] [bp-D8h]@4
  unsigned int v16; // [sp+64h] [bp-D4h]@4
  unsigned int v17; // [sp+68h] [bp-D0h]@4
  unsigned int v18; // [sp+6Ch] [bp-CCh]@4
  char v19; // [sp+90h] [bp-A8h]@4
  char v20; // [sp+91h] [bp-A7h]@4
  char *Dest; // [sp+118h] [bp-20h]@4
  unsigned __int64 v22; // [sp+128h] [bp-10h]@4
  CMainThread *v23; // [sp+140h] [bp+8h]@1
  int dwSeriala; // [sp+148h] [bp+10h]@1
  _AVATOR_DATA *v25; // [sp+150h] [bp+18h]@1
  char *v26; // [sp+158h] [bp+20h]@1

  v26 = pSzNpcQuery;
  v25 = pAvatorData;
  dwSeriala = dwSerial;
  v23 = this;
  v4 = &v7;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v22 = (unsigned __int64)&v7 ^ _security_cookie;
  v13 = 0;
  memset(&v14, 0, 0x14ui64);
  v13 = CCheckSum::EncodeValue(&v23->m_CheckSum, 0, dwSerial, pAvatorData->dbAvator.m_dwDalant);
  v14 = CCheckSum::EncodeValue(&v23->m_CheckSum, 1, dwSeriala, v25->dbAvator.m_dwGold);
  v15 = CCheckSum::EncodeValue(&v23->m_CheckSum, 2, dwSeriala, v25->dbAvator.m_byLevel);
  v16 = CCheckSum::EncodeValue(&v23->m_CheckSum, 3, dwSeriala, v25->dbStat.m_dwDamWpCnt[0]);
  v17 = CCheckSum::EncodeValue(&v23->m_CheckSum, 4, dwSeriala, v25->dbStat.m_dwDefenceCnt);
  v18 = CCheckSum::EncodeValue(&v23->m_CheckSum, 5, dwSeriala, v25->dbStat.m_dwDamWpCnt[1]);
  v19 = 0;
  memset(&v20, 0, 0x7Fui64);
  Dest = v26;
  v12 = dwSeriala;
  v11 = v18;
  v10 = v17;
  v9 = v16;
  v8 = v15;
  sprintf(
    v26,
    "update tbl_NpcData set Npc0 = %d, Npc1 = %d, Npc2 = %d, Npc3 = %d, Npc4 = %d, Npc5 = %d where Serial=%d ",
    v13,
    v14);
  return 1;
}
