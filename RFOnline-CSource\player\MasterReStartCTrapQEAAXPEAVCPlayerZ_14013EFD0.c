/*
 * Function: ?MasterReStart@CTrap@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14013EFD0
 */

void __usercall CTrap::MasterReStart(CTrap *this@<rcx>, CPlayer *pMaster@<rdx>, long double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CTrap *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6->m_pMaster = pMaster;
  CPlayerDB::GetPvPPoint(&pMaster->m_Param);
  v6->m_dMasterPvPPoint = a3;
}
