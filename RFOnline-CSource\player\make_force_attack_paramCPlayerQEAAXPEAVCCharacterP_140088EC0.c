/*
 * Function: ?make_force_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAU_force_fld@@PEAU_db_con@_STORAGE_LIST@@PEAMPEAU_attack_param@@2M@Z
 * Address: 0x140088EC0
 */

void __fastcall CPlayer::make_force_attack_param(CPlayer *this, CCharacter *pDst, _force_fld *pForceFld, _STORAGE_LIST::_db_con *pForceItem, float *pTar, _attack_param *pAP, _STORAGE_LIST::_db_con *pEffBulletItem, float fAddEffBtFc)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  float v10; // xmm0_4@9
  float v11; // xmm0_4@9
  float v12; // xmm0_4@10
  float v13; // xmm0_4@10
  __int64 v14; // [sp+0h] [bp-38h]@1
  float v15; // [sp+20h] [bp-18h]@9
  float v16; // [sp+24h] [bp-14h]@9
  float v17; // [sp+28h] [bp-10h]@10
  float v18; // [sp+2Ch] [bp-Ch]@10
  CPlayer *pTarget; // [sp+40h] [bp+8h]@1
  CMonster *v20; // [sp+48h] [bp+10h]@1
  _force_fld *v21; // [sp+50h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v22; // [sp+58h] [bp+20h]@1

  v22 = pForceItem;
  v21 = pForceFld;
  v20 = (CMonster *)pDst;
  pTarget = this;
  v8 = &v14;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  pAP->pDst = pDst;
  if ( pDst )
    pAP->nPart = CCharacter::GetAttackRandomPart(pDst);
  else
    pAP->nPart = CCharacter::GetAttackRandomPart((CCharacter *)&pTarget->vfptr);
  pAP->nTol = v21->m_nProperty;
  pAP->nClass = pTarget->m_pmWpn.byWpClass;
  if ( strncmp(pTarget->m_pmWpn.strEffBulletType, "-1", 2ui64) && pEffBulletItem )
  {
    v15 = (float)pTarget->m_pmWpn.nMaMinAF;
    v10 = v15;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    pAP->nMinAFPlus = (signed int)ffloor((float)((float)(v15 * v10) * fAddEffBtFc) + (float)pTarget->m_pmMst.m_mtyStaff);
    v16 = (float)pTarget->m_pmWpn.nMaMaxAF;
    v11 = v16;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    pAP->nMaxAFPlus = (signed int)ffloor((float)((float)(v16 * v11) * fAddEffBtFc) + (float)pTarget->m_pmMst.m_mtyStaff);
  }
  v17 = (float)pTarget->m_pmWpn.nMaMinAF;
  v12 = v17;
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
  pAP->nMinAF = (signed int)ffloor((float)(v17 * v12) + (float)pTarget->m_pmMst.m_mtyStaff);
  v18 = (float)pTarget->m_pmWpn.nMaMaxAF;
  v13 = v18;
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
  pAP->nMaxAF = (signed int)ffloor((float)(v18 * v13) + (float)pTarget->m_pmMst.m_mtyStaff);
  pAP->nMinSel = pTarget->m_pmWpn.byMaMinSel;
  pAP->nMaxSel = pTarget->m_pmWpn.byMaMaxSel;
  pAP->pFld = (_base_fld *)v21;
  pAP->byEffectCode = 1;
  pAP->nLevel = GetSFLevel(v21->m_nLv, v22->m_dwDur);
  pAP->nMastery = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 4, v21->m_nMastIndex);
  if ( v20 )
    memcpy_0(pAP->fArea, v20->m_fCurPos, 0xCui64);
  else
    memcpy_0(pAP->fArea, pTar, 0xCui64);
  pAP->nMaxAttackPnt = pTarget->m_nMaxAttackPnt;
  if ( v20 && v20->m_ObjID.m_byKind == 1 && !CMonster::IsViewArea(v20, (CCharacter *)&pTarget->vfptr) )
    pAP->bBackAttack = 1;
  if ( pEffBulletItem )
    pAP->nEffShotNum = 1;
}
