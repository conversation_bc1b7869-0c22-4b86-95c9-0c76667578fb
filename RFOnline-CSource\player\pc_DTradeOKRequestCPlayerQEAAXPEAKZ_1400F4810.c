/*
 * Function: ?pc_DTradeOKRequest@CPlayer@@QEAAXPEAK@Z
 * Address: 0x1400F4810
 */

void __fastcall CPlayer::pc_DTradeOKRequest(CPlayer *this, unsigned int *pdwKey)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@22
  unsigned int v5; // eax@23
  unsigned __int64 v6; // rax@33
  signed __int64 v7; // rcx@33
  double v8; // xmm0_8@33
  unsigned __int64 v9; // rax@39
  signed __int64 v10; // rcx@39
  double v11; // xmm0_8@39
  int v12; // eax@67
  __int64 v13; // rdx@68
  __int64 v14; // xmm0_8@95
  char *v15; // rax@95
  __int64 v16; // rcx@95
  __int64 v17; // rdx@95
  __int64 v18; // r10@95
  __int64 v19; // r11@95
  int v20; // esi@95
  int v21; // eax@96
  __int64 v22; // [sp+0h] [bp-808h]@1
  unsigned int bUpdate[2]; // [sp+20h] [bp-7E8h]@67
  bool bSend[8]; // [sp+28h] [bp-7E0h]@67
  _STORAGE_LIST::_db_con *pInItem; // [sp+30h] [bp-7D8h]@95
  int nInItemNum; // [sp+38h] [bp-7D0h]@95
  unsigned int dwInDalant[2]; // [sp+40h] [bp-7C8h]@95
  unsigned int dwInGold; // [sp+48h] [bp-7C0h]@95
  char *pszDstName; // [sp+50h] [bp-7B8h]@95
  unsigned int dwDstSerial; // [sp+58h] [bp-7B0h]@95
  char *pszDstID; // [sp+60h] [bp-7A8h]@95
  unsigned int dwSumDalant; // [sp+68h] [bp-7A0h]@95
  unsigned int dwSumGold; // [sp+70h] [bp-798h]@95
  char *pMapCode; // [sp+78h] [bp-790h]@95
  float *pfPos; // [sp+80h] [bp-788h]@95
  char *pszFileName; // [sp+88h] [bp-780h]@95
  char v37; // [sp+90h] [bp-778h]@4
  CPlayer *v38; // [sp+A8h] [bp-760h]@4
  CPlayer *p_pDst; // [sp+B0h] [bp-758h]@4
  char __t[1528]; // [sp+E0h] [bp-728h]@4
  int nOutItemNum; // [sp+6D8h] [bp-130h]@4
  int v42; // [sp+6DCh] [bp-12Ch]@4
  int j; // [sp+6E4h] [bp-124h]@20
  int v44; // [sp+6E8h] [bp-120h]@32
  double v45; // [sp+6F0h] [bp-118h]@32
  double v46; // [sp+6F8h] [bp-110h]@32
  int v47; // [sp+700h] [bp-108h]@44
  int k; // [sp+704h] [bp-104h]@44
  bool *v49; // [sp+708h] [bp-100h]@47
  _STORAGE_LIST *v50; // [sp+710h] [bp-F8h]@48
  void *Src; // [sp+718h] [bp-F0h]@48
  int v52; // [sp+720h] [bp-E8h]@62
  int v53; // [sp+724h] [bp-E4h]@64
  int l; // [sp+728h] [bp-E0h]@64
  int m; // [sp+72Ch] [bp-DCh]@71
  unsigned __int16 v56; // [sp+730h] [bp-D8h]@78
  int n; // [sp+734h] [bp-D4h]@85
  int ii; // [sp+738h] [bp-D0h]@88
  double v59; // [sp+740h] [bp-C8h]@95
  double v60; // [sp+748h] [bp-C0h]@95
  __int64 v61; // [sp+750h] [bp-B8h]@95
  __int64 v62; // [sp+758h] [bp-B0h]@22
  __int64 v63; // [sp+760h] [bp-A8h]@23
  __int64 v64; // [sp+768h] [bp-A0h]@67
  __int64 v65; // [sp+770h] [bp-98h]@67
  __int64 v66; // [sp+778h] [bp-90h]@68
  __int64 v67; // [sp+780h] [bp-88h]@87
  char *v68; // [sp+788h] [bp-80h]@95
  float *v69; // [sp+790h] [bp-78h]@95
  char *v70; // [sp+798h] [bp-70h]@95
  unsigned int v71; // [sp+7A0h] [bp-68h]@95
  unsigned int v72; // [sp+7A4h] [bp-64h]@95
  char *v73; // [sp+7A8h] [bp-60h]@95
  unsigned int v74; // [sp+7B0h] [bp-58h]@95
  char *v75; // [sp+7B8h] [bp-50h]@96
  CGameObjectVtbl *v76; // [sp+7C0h] [bp-48h]@96
  int v77; // [sp+7C8h] [bp-40h]@96
  char *v78; // [sp+7D0h] [bp-38h]@96
  CGameObjectVtbl *v79; // [sp+7D8h] [bp-30h]@96
  CPlayer *lp_pOne; // [sp+810h] [bp+8h]@1
  unsigned int *Buf2; // [sp+818h] [bp+10h]@1

  Buf2 = pdwKey;
  lp_pOne = this;
  v2 = &v22;
  for ( i = 506i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v37 = 0;
  v38 = lp_pOne;
  p_pDst = 0i64;
  `vector constructor iterator'(__t, 0x32ui64, 30, (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
  nOutItemNum = 0;
  v42 = 0;
  if ( lp_pOne->m_pUserDB )
  {
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, lp_pOne->m_id.wIndex) == 99 )
    {
      v37 = 23;
    }
    else if ( DTradeEqualPerson(lp_pOne, &p_pDst) )
    {
      if ( lp_pOne->m_pCurMap->m_pMapSet->m_nMapType == 1 )
      {
        v37 = 6;
      }
      else if ( lp_pOne->m_pmTrd.bDTradeLock && p_pDst->m_pmTrd.bDTradeLock )
      {
        if ( lp_pOne->m_pmTrd.bDTradeOK )
        {
          v37 = 3;
        }
        else if ( !memcmp_0(lp_pOne->m_pmTrd.dwKey, Buf2, 0x10ui64) )
        {
          if ( lp_pOne->m_pmTrd.bySellItemNum <= p_pDst->m_pmTrd.byEmptyInvenNum )
          {
            for ( j = 0; j < 2; ++j )
            {
              v62 = (__int64)*(&v38 + j);
              v4 = CPlayerDB::GetDalant(&(*(&v38 + j))->m_Param);
              if ( *(_DWORD *)(v62 + 44192) > v4
                || (v63 = (__int64)*(&v38 + j),
                    v5 = CPlayerDB::GetGold(&(*(&v38 + j))->m_Param),
                    *(_DWORD *)(v63 + 44196) > v5) )
              {
                v37 = 5;
                goto $RESULT_79;
              }
              if ( (*(&v38 + j))->m_pmTrd.dwDTrade_Dalant > 0x77359400 || (*(&v38 + j))->m_pmTrd.dwDTrade_Gold > 0x7A120 )
              {
                v37 = 5;
                goto $RESULT_79;
              }
            }
            if ( p_pDst->m_pmTrd.bDTradeOK )
            {
              for ( j = 0; j < 2; ++j )
              {
                v44 = (j + 1) % 2;
                v45 = (double)(signed int)(*(&v38 + v44))->m_pmTrd.dwDTrade_Dalant
                    - (double)(signed int)(*(&v38 + j))->m_pmTrd.dwDTrade_Dalant;
                v46 = (double)(signed int)(*(&v38 + v44))->m_pmTrd.dwDTrade_Gold
                    - (double)(signed int)(*(&v38 + j))->m_pmTrd.dwDTrade_Gold;
                if ( v45 > 0.0 )
                {
                  v6 = CPlayerDB::GetDalant(&(*(&v38 + j))->m_Param);
                  v7 = 0i64;
                  v8 = v45;
                  if ( v45 > 9.223372036854776e18 )
                  {
                    v8 = v45 - 9.223372036854776e18;
                    v7 = 0x7FFFFFFFFFFFFFFFi64;
                    if ( v45 - 9.223372036854776e18 > 9.223372036854776e18 )
                    {
                      v8 = v8 - 9.223372036854776e18;
                      v7 = -2i64;
                    }
                  }
                  if ( !CanAddMoneyForMaxLimMoney(v7 + (unsigned int)(signed int)floor(v8), v6) )
                  {
                    v37 = 6;
                    break;
                  }
                }
                if ( v46 > 0.0 )
                {
                  v9 = CPlayerDB::GetGold(&(*(&v38 + j))->m_Param);
                  v10 = 0i64;
                  v11 = v46;
                  if ( v46 > 9.223372036854776e18 )
                  {
                    v11 = v46 - 9.223372036854776e18;
                    v10 = 0x7FFFFFFFFFFFFFFFi64;
                    if ( v46 - 9.223372036854776e18 > 9.223372036854776e18 )
                    {
                      v11 = v11 - 9.223372036854776e18;
                      v10 = -2i64;
                    }
                  }
                  if ( !CanAddMoneyForMaxLimGold(v10 + (unsigned int)(signed int)floor(v11), v9) )
                  {
                    v37 = 6;
                    break;
                  }
                }
                v47 = 0;
                for ( k = 0; k < 15; ++k )
                {
                  v49 = &(*(&v38 + j))->m_pmTrd.DItemNode[k].bLoad;
                  if ( *v49 )
                  {
                    v50 = (*(&v38 + j))->m_Param.m_pStoragePtr[v49[1]];
                    Src = _STORAGE_LIST::GetPtrFromSerial(v50, *((_WORD *)v49 + 2));
                    if ( !Src )
                    {
                      v37 = 4;
                      goto $RESULT_79;
                    }
                    if ( *((_BYTE *)Src + 19) )
                    {
                      v37 = 9;
                      goto $RESULT_79;
                    }
                    if ( *((_BYTE *)Src + 1) == 19 )
                    {
                      v37 = 9;
                      goto $RESULT_79;
                    }
                    memcpy_0(&__t[750 * j] + 50 * v47, Src, 0x32ui64);
                    if ( IsOverLapItem(*((_BYTE *)Src + 1)) && *(_QWORD *)((char *)Src + 5) > (unsigned __int64)v49[8] )
                      *(_QWORD *)(&__t[750 * j + 5] + 50 * v47) = v49[8];
                    ++v47;
                  }
                }
                *(&nOutItemNum + j) = v47;
              }
            }
          }
          else
          {
            v37 = 100;
          }
        }
        else
        {
          v37 = 13;
        }
      }
      else
      {
        v37 = 2;
      }
    }
    else
    {
      v37 = 1;
    }
$RESULT_79:
    if ( v37 )
    {
      _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
      CPlayer::SendMsg_DTradeCloseInform(lp_pOne, 0);
      if ( p_pDst )
      {
        _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
        CPlayer::SendMsg_DTradeCloseInform(p_pDst, 0);
      }
    }
    else
    {
      CPlayer::SendMsg_DTradeOKResult(lp_pOne, 0);
      CPlayer::SendMsg_DTradeOKInform(p_pDst);
      lp_pOne->m_pmTrd.bDTradeOK = 1;
      if ( p_pDst->m_pmTrd.bDTradeOK && lp_pOne->m_pmTrd.bDTradeOK )
      {
        v52 = 0;
LABEL_63:
        if ( v52 >= 2 )
        {
          CPlayer::pc_UpdateDataForTrade(v38, p_pDst);
          v75 = CPlayerDB::GetCharNameA(&p_pDst->m_Param);
          v76 = p_pDst->vfptr;
          v77 = ((int (__fastcall *)(CPlayer *))v76->GetLevel)(p_pDst);
          v78 = CPlayerDB::GetCharNameA(&v38->m_Param);
          v79 = v38->vfptr;
          v21 = ((int (__fastcall *)(CPlayer *))v79->GetLevel)(v38);
          dwDstSerial = p_pDst->m_pmTrd.dwDTrade_Gold;
          LODWORD(pszDstName) = p_pDst->m_pmTrd.dwDTrade_Dalant;
          dwInGold = p_pDst->m_dwObjSerial;
          *(_QWORD *)dwInDalant = v75;
          nInItemNum = v77;
          LODWORD(pInItem) = v38->m_pmTrd.dwDTrade_Gold;
          *(_DWORD *)bSend = v38->m_pmTrd.dwDTrade_Dalant;
          bUpdate[0] = v38->m_dwObjSerial;
          CLogFile::Write(&stru_1799C9210, "[%d]%s(%d): D(%d) G(%d) # [%d]%s(%d): D(%d) G(%d)", (unsigned int)v21, v78);
          _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
          _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
        }
        else
        {
          v53 = (((unsigned __int64)(v52 + 1) >> 32) ^ ((_BYTE)v52 + 1) & 1) - ((unsigned __int64)(v52 + 1) >> 32);
          for ( l = 0; l < *(&nOutItemNum + v52); ++l )
          {
            if ( IsOverLapItem((unsigned __int8)*(&__t[750 * v52 + 1] + 50 * l)) )
            {
              v12 = -(signed __int128)*(_QWORD *)(&__t[750 * v52 + 5] + 50 * l);
              v64 = *(_QWORD *)(&__t[750 * v52 + 41] + 50 * l);
              v65 = v52;
              bSend[0] = 0;
              LOBYTE(bUpdate[0]) = 1;
              CPlayer::Emb_AlterDurPoint(
                *(&v38 + v52),
                *(_BYTE *)(v64 + 8),
                *(&__t[750 * v52 + 49] + 50 * l),
                v12,
                1,
                0);
            }
            else
            {
              v13 = *(_QWORD *)(&__t[750 * v52 + 41] + 50 * l);
              v66 = v52;
              *(_QWORD *)bSend = "CPlayer::pc_DTradeOKRequest()";
              LOBYTE(bUpdate[0]) = 1;
              if ( !CPlayer::Emb_DelStorage(
                      *(&v38 + v52),
                      *(_BYTE *)(v13 + 8),
                      *(&__t[750 * v52 + 49] + 50 * l),
                      0,
                      1,
                      "CPlayer::pc_DTradeOKRequest()")
                && !v52
                && nOutItemNum > 0 )
              {
                for ( m = 0; m < l; ++m )
                {
                  LOBYTE(bUpdate[0]) = 1;
                  CPlayer::Emb_AddStorage(
                    *(&v38 + v52),
                    0,
                    (_STORAGE_LIST::_storage_con *)(&__t[750 * v52] + 50 * m),
                    0,
                    1);
                  CPlayer::SendMsg_RewardAddItem(*(&v38 + v52), (_STORAGE_LIST::_db_con *)&__t[750 * v52] + m, 0);
                }
                _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
                CPlayer::SendMsg_DTradeCloseInform(lp_pOne, 0);
                if ( p_pDst )
                {
                  _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
                  CPlayer::SendMsg_DTradeCloseInform(p_pDst, 0);
                }
                return;
              }
            }
          }
          v56 = -1;
          for ( l = 0; ; ++l )
          {
            if ( l >= *(&nOutItemNum + v53) )
            {
              v59 = (double)(signed int)(*(&v38 + v53))->m_pmTrd.dwDTrade_Dalant
                  - (double)(signed int)(*(&v38 + v52))->m_pmTrd.dwDTrade_Dalant;
              v60 = (double)(signed int)(*(&v38 + v53))->m_pmTrd.dwDTrade_Gold
                  - (double)(signed int)(*(&v38 + v52))->m_pmTrd.dwDTrade_Gold;
              *(double *)&v14 = v60;
              TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, lp_pOne->m_id.wIndex);
              v61 = v14;
              v59 = v59 * *(double *)&v14;
              v60 = v60 * *(double *)&v14;
              CPlayer::AlterDalant(*(&v38 + v52), v59);
              CPlayer::AlterGold(*(&v38 + v52), v60);
              CPlayer::SendMsg_DTradeAccomplishInform(*(&v38 + v52), 1, v56);
              v68 = (*(&v38 + v52))->m_szItemHistoryFileName;
              v69 = (*(&v38 + v52))->m_fCurPos;
              v70 = (*(&v38 + v52))->m_pCurMap->m_pMapSet->m_strCode;
              v71 = CPlayerDB::GetGold(&(*(&v38 + v52))->m_Param);
              v72 = CPlayerDB::GetDalant(&(*(&v38 + v52))->m_Param);
              v73 = (*(&v38 + v53))->m_pUserDB->m_szAccountID;
              v74 = CPlayerDB::GetCharSerial(&(*(&v38 + v53))->m_Param);
              v15 = CPlayerDB::GetCharNameA(&(*(&v38 + v53))->m_Param);
              v16 = (__int64)*(&v38 + v53);
              v17 = (__int64)*(&v38 + v53);
              v18 = (__int64)*(&v38 + v52);
              v19 = (__int64)*(&v38 + v52);
              v20 = (*(&v38 + v52))->m_ObjID.m_wIndex;
              pszFileName = v68;
              pfPos = v69;
              pMapCode = v70;
              dwSumGold = v71;
              dwSumDalant = v72;
              pszDstID = v73;
              dwDstSerial = v74;
              pszDstName = v15;
              dwInGold = *(_DWORD *)(v16 + 44196);
              dwInDalant[0] = *(_DWORD *)(v17 + 44192);
              nInItemNum = *(&nOutItemNum + v53);
              pInItem = (_STORAGE_LIST::_db_con *)&__t[750 * v53];
              *(_DWORD *)bSend = *(_DWORD *)(v18 + 44196);
              bUpdate[0] = *(_DWORD *)(v19 + 44192);
              CMgrAvatorItemHistory::trade(
                &CPlayer::s_MgrItemHistory,
                v20,
                (_STORAGE_LIST::_db_con *)&__t[750 * v52],
                *(&nOutItemNum + v52),
                bUpdate[0],
                *(unsigned int *)bSend,
                pInItem,
                nInItemNum,
                dwInDalant[0],
                dwInGold,
                v15,
                v74,
                v73,
                v72,
                v71,
                v70,
                v69,
                v68);
              ++v52;
              goto LABEL_63;
            }
            *((_WORD *)&__t[750 * v53 + 17] + 25 * l) = CPlayerDB::GetNewItemSerial(&(*(&v38 + v52))->m_Param);
            if ( v56 == 0xFFFF )
              v56 = *((_WORD *)&__t[750 * v53 + 17] + 25 * l);
            LOBYTE(bUpdate[0]) = 1;
            if ( !CPlayer::Emb_AddStorage(
                    *(&v38 + v52),
                    0,
                    (_STORAGE_LIST::_storage_con *)(&__t[750 * v53] + 50 * l),
                    0,
                    1)
              && !v52
              && *(&nOutItemNum + v53) > 0 )
            {
              break;
            }
          }
          for ( n = 0; n < l; ++n )
          {
            v67 = v52;
            *(_QWORD *)bSend = "CPlayer::pc_DTradeOKRequest() - Emb_Add Item Fail";
            LOBYTE(bUpdate[0]) = 1;
            CPlayer::Emb_DelStorage(
              *(&v38 + v52),
              0,
              *(&__t[750 * v53 + 49] + 50 * n),
              0,
              1,
              "CPlayer::pc_DTradeOKRequest() - Emb_Add Item Fail");
          }
          for ( ii = 0; ii < *(&nOutItemNum + v52); ++ii )
          {
            LOBYTE(bUpdate[0]) = 1;
            CPlayer::Emb_AddStorage(*(&v38 + v52), 0, (_STORAGE_LIST::_storage_con *)(&__t[750 * v52] + 50 * ii), 0, 1);
            CPlayer::SendMsg_RewardAddItem(*(&v38 + v52), (_STORAGE_LIST::_db_con *)&__t[750 * v52] + ii, 0);
          }
          _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
          CPlayer::SendMsg_DTradeCloseInform(lp_pOne, 0);
          if ( p_pDst )
          {
            _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
            CPlayer::SendMsg_DTradeCloseInform(p_pDst, 0);
          }
        }
      }
    }
  }
}
