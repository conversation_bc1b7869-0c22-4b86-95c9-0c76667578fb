/*
 * Function: ?_db_Update_Trunk@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401AE630
 */

char __fastcall CMainThread::_db_Update_Trunk(CMainThread *this, unsigned int dwAccountSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pwszQuery)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@20
  int v8; // eax@23
  int v9; // eax@24
  signed __int64 v10; // rdi@45
  int v11; // eax@45
  size_t v12; // rax@47
  __int64 v14; // [sp+0h] [bp-168h]@1
  unsigned int v15; // [sp+20h] [bp-148h]@45
  unsigned __int64 v16; // [sp+28h] [bp-140h]@45
  unsigned int v17; // [sp+30h] [bp-138h]@45
  unsigned int v18; // [sp+38h] [bp-130h]@45
  unsigned int v19; // [sp+40h] [bp-128h]@45
  int v20; // [sp+48h] [bp-120h]@45
  unsigned int v21; // [sp+50h] [bp-118h]@45
  unsigned __int64 v22; // [sp+58h] [bp-110h]@45
  unsigned int v23; // [sp+60h] [bp-108h]@45
  unsigned int v24; // [sp+68h] [bp-100h]@45
  char Source; // [sp+80h] [bp-E8h]@4
  char v26; // [sp+81h] [bp-E7h]@4
  char *Dest; // [sp+108h] [bp-60h]@4
  size_t Size; // [sp+110h] [bp-58h]@4
  unsigned int j; // [sp+118h] [bp-50h]@16
  unsigned int v30; // [sp+11Ch] [bp-4Ch]@34
  unsigned int v31; // [sp+120h] [bp-48h]@34
  unsigned int v32; // [sp+124h] [bp-44h]@41
  int v33; // [sp+130h] [bp-38h]@23
  __int64 v34; // [sp+138h] [bp-30h]@45
  int v35; // [sp+140h] [bp-28h]@45
  __int64 v36; // [sp+148h] [bp-20h]@45
  unsigned __int64 v37; // [sp+150h] [bp-18h]@4
  unsigned int v38; // [sp+178h] [bp+10h]@1
  _AVATOR_DATA *v39; // [sp+180h] [bp+18h]@1
  _AVATOR_DATA *v40; // [sp+188h] [bp+20h]@1

  v40 = pOldData;
  v39 = pNewData;
  v38 = dwAccountSerial;
  v5 = &v14;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v37 = (unsigned __int64)&v14 ^ _security_cookie;
  Source = 0;
  memset(&v26, 0, 0x7Fui64);
  Dest = pwszQuery;
  sprintf(pwszQuery, "UPDATE tbl_AccountTrunk SET ");
  LODWORD(Size) = strlen_0(Dest);
  BYTE4(Size) = v39->dbTrunk.bySlotNum;
  BYTE5(Size) = v39->dbAvator.m_byRaceSexCode >> 1;
  if ( strcmp_0(v39->dbTrunk.wszPasswd, v40->dbTrunk.wszPasswd) )
  {
    sprintf(&Source, "TrunkPass=cast('%s' as binary),", &v39->dbTrunk);
    strcat_0(Dest, &Source);
  }
  if ( v39->dbTrunk.dDalant != v40->dbTrunk.dDalant )
  {
    sprintf(&Source, "Dalant%d=%f,", BYTE5(Size), v39->dbTrunk.dDalant);
    strcat_0(Dest, &Source);
  }
  if ( v39->dbTrunk.dGold != v40->dbTrunk.dGold )
  {
    sprintf(&Source, "Gold%d=%f,", BYTE5(Size), v39->dbTrunk.dGold);
    strcat_0(Dest, &Source);
  }
  if ( v39->dbTrunk.byHintIndex != v40->dbTrunk.byHintIndex )
  {
    sprintf(&Source, "HintIndex=%d,", v39->dbTrunk.byHintIndex);
    strcat_0(Dest, &Source);
  }
  if ( strcmp_0(v39->dbTrunk.wszHintAnswer, v40->dbTrunk.wszHintAnswer) )
  {
    sprintf(&Source, "HintAnswer='%s',", v39->dbTrunk.wszHintAnswer);
    strcat_0(Dest, &Source);
  }
  if ( v39->dbTrunk.bySlotNum != v40->dbTrunk.bySlotNum )
  {
    sprintf(&Source, "EstSlot=%d,", v39->dbTrunk.bySlotNum);
    strcat_0(Dest, &Source);
  }
  for ( j = 0; (signed int)j < BYTE4(Size); ++j )
  {
    if ( _INVENKEY::IsFilled(&v39->dbTrunk.m_List[j].Key) )
    {
      if ( _INVENKEY::IsFilled(&v40->dbTrunk.m_List[j].Key) )
      {
        v33 = _INVENKEY::CovDBKey(&v39->dbTrunk.m_List[j].Key);
        v8 = _INVENKEY::CovDBKey(&v40->dbTrunk.m_List[j].Key);
        if ( v33 != v8 )
        {
          v9 = _INVENKEY::CovDBKey(&v39->dbTrunk.m_List[j].Key);
          sprintf(&Source, "K%d=%d,", j, (unsigned int)v9);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_List[j].dwDur != v40->dbTrunk.m_List[j].dwDur )
        {
          sprintf(&Source, "D%d=%I64d,", j, v39->dbTrunk.m_List[j].dwDur);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_List[j].dwUpt != v40->dbTrunk.m_List[j].dwUpt )
        {
          sprintf(&Source, "U%d=%d,", j, v39->dbTrunk.m_List[j].dwUpt);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_List[j].byRace != v40->dbTrunk.m_List[j].byRace )
        {
          sprintf(&Source, "R%d=%d,", j, v39->dbTrunk.m_List[j].byRace);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_List[j].lnUID != v40->dbTrunk.m_List[j].lnUID )
        {
          sprintf(&Source, "S%d=%I64d,", j, v39->dbTrunk.m_List[j].lnUID);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_List[j].byCsMethod )
        {
          v30 = 0;
          v31 = 0;
          if ( v39->dbTrunk.m_List[j].byCsMethod == 1 )
          {
            v30 = v39->dbTrunk.m_List[j].dwT - Time;
            v31 = v40->dbTrunk.m_List[j].dwT - Time;
          }
          else if ( v39->dbTrunk.m_List[j].byCsMethod == 2 )
          {
            v30 = v39->dbTrunk.m_List[j].dwT;
            v31 = v40->dbTrunk.m_List[j].dwT;
          }
          if ( v30 != v31 )
          {
            sprintf(&Source, "T%d=%d,", j, v30);
            strcat_0(Dest, &Source);
          }
        }
      }
      else
      {
        v32 = 0;
        if ( v39->dbTrunk.m_List[j].byCsMethod == 1 )
        {
          v32 = v39->dbTrunk.m_List[j].dwT - Time;
        }
        else if ( v39->dbTrunk.m_List[j].byCsMethod == 2 )
        {
          v32 = v39->dbTrunk.m_List[j].dwT;
        }
        v34 = 38i64 * (signed int)j;
        v35 = v39->dbTrunk.m_List[j].byRace;
        v36 = 38i64 * (signed int)j;
        v10 = (signed int)j;
        v11 = _INVENKEY::CovDBKey(&v39->dbTrunk.m_List[j].Key);
        v24 = v32;
        v23 = j;
        v22 = v39->dbTrunk.m_List[(unsigned __int64)v34 / 0x26].lnUID;
        v21 = j;
        v20 = v35;
        v19 = j;
        v18 = v39->dbTrunk.m_List[(unsigned __int64)v36 / 0x26].dwUpt;
        v17 = j;
        v16 = v39->dbTrunk.m_List[v10].dwDur;
        v15 = j;
        sprintf(&Source, "K%d=%d,D%d=%I64d,U%d=%d,R%d=%d,S%d=%I64d,T%d=%d,", j, (unsigned int)v11);
        strcat_0(Dest, &Source);
      }
    }
    else if ( _INVENKEY::IsFilled(&v40->dbTrunk.m_List[j].Key) )
    {
      v7 = _INVENKEY::CovDBKey(&v39->dbTrunk.m_List[j].Key);
      sprintf(&Source, "K%d=%d,", j, (unsigned int)v7);
      strcat_0(Dest, &Source);
    }
  }
  v12 = strlen_0(Dest);
  if ( v12 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE AccountSerial=%d", v38);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
