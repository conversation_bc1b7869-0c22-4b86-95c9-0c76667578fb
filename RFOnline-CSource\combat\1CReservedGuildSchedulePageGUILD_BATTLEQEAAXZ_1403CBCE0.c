/*
 * Function: ??1CReservedGuildSchedulePage@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403CBCE0
 */

void __fastcall GUILD_BATTLE::CReservedGuildSchedulePage::~CReservedGuildSchedulePage(GUILD_BATTLE::CReservedGuildSchedulePage *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  GUILD_BATTLE::CReservedGuildSchedulePage *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_pkList )
  {
    v4 = v5->m_pkList;
    operator delete(v4);
    v5->m_pkList = 0i64;
  }
}
