/*
 * Function: ?DestGuildIsAvailableBattleRequestState@CGuild@@QEAAEXZ
 * Address: 0x140257960
 */

char __fastcall CGuild::DestGuildIsAvailableBattleRequestState(CGuild *this)
{
  char *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // [sp+0h] [bp-18h]@1
  _guild_battle_suggest_matter::STATE v5; // [sp+4h] [bp-14h]@4
  CGuild *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 += 4;
  }
  v4 = 0;
  v5 = v6->m_GuildBattleSugestMatter.eState;
  if ( v5 )
  {
    switch ( v5 )
    {
      case 1:
        v4 = -91;
        break;
      case 2:
        v4 = -95;
        break;
      case 3:
        v4 = -94;
        break;
      case 4:
        v4 = -93;
        break;
      default:
        v4 = 110;
        break;
    }
  }
  else
  {
    v4 = 0;
  }
  return v4;
}
