/*
 * Function: ?pc_BuddyDownloadRequest@CPlayer@@QEAAXXZ
 * Address: 0x14008FC80
 */

void __usercall CPlayer::pc_BuddyDownloadRequest(CPlayer *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@14
  __int64 v6; // [sp-20h] [bp-2828h]@1
  _buddy_download_result_zocl v7; // [sp+20h] [bp-27E8h]@4
  int v8; // [sp+2744h] [bp-C4h]@4
  void *Dst; // [sp+2748h] [bp-C0h]@4
  char Src; // [sp+2754h] [bp-B4h]@4
  int j; // [sp+2764h] [bp-A4h]@4
  _BUDDY_LIST::__list *v12; // [sp+2768h] [bp-A0h]@7
  unsigned __int8 v13; // [sp+2774h] [bp-94h]@8
  char v14; // [sp+2794h] [bp-74h]@9
  char v15; // [sp+27B4h] [bp-54h]@10
  char pbyType; // [sp+27D4h] [bp-34h]@14
  char v17; // [sp+27D5h] [bp-33h]@14
  unsigned __int64 v18; // [sp+27F0h] [bp-18h]@4
  CPlayer *v19; // [sp+2810h] [bp+8h]@1

  v19 = this;
  v2 = alloca(a2);
  v3 = &v6;
  for ( i = 2568i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = (unsigned __int64)&v6 ^ _security_cookie;
  _buddy_download_result_zocl::_buddy_download_result_zocl(&v7);
  v8 = 0;
  Dst = v7.sData;
  Src = _BUDDY_LIST::GetBuddyNum(&v19->m_pmBuddy);
  memcpy_0(Dst, &Src, 1ui64);
  Dst = (char *)Dst + 1;
  ++v8;
  for ( j = 0; j < 50; ++j )
  {
    v12 = &v19->m_pmBuddy.m_List[j];
    if ( _BUDDY_LIST::__list::fill(v12) )
    {
      v13 = strlen_0(v12->wszName);
      memcpy_0(Dst, &v13, 1ui64);
      Dst = (char *)Dst + 1;
      ++v8;
      memcpy_0(Dst, v12->wszName, v13);
      Dst = (char *)Dst + v13;
      v8 += v13;
      memcpy_0(Dst, v12, 4ui64);
      Dst = (char *)Dst + 4;
      v8 += 4;
      if ( v12->pPtr )
      {
        v14 = 1;
        memcpy_0(Dst, &v14, 1ui64);
      }
      else
      {
        v15 = 0;
        memcpy_0(Dst, &v15, 1ui64);
      }
      Dst = (char *)Dst + 1;
      ++v8;
      if ( v12->pPtr )
      {
        memcpy_0(Dst, &v12->pPtr->m_wRegionMapIndex, 1ui64);
        Dst = (char *)Dst + 1;
        ++v8;
        memcpy_0(Dst, &v12->pPtr->m_wRegionIndex, 1ui64);
        Dst = (char *)Dst + 1;
        ++v8;
      }
    }
  }
  v7.wDataSize = v8;
  pbyType = 31;
  v17 = 4;
  v5 = _buddy_download_result_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v19->m_ObjID.m_wIndex, &pbyType, (char *)&v7, v5);
}
