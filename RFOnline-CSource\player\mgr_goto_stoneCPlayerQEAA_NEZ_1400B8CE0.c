/*
 * Function: ?mgr_goto_stone@CPlayer@@QEAA_NE@Z
 * Address: 0x1400B8CE0
 */

char __fastcall CPlayer::mgr_goto_stone(CPlayer *this, char byRaceCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v5; // rcx@6
  __int64 v6; // [sp+0h] [bp-48h]@1
  float *pfStartPos; // [sp+20h] [bp-28h]@6
  __holy_stone_data *v8; // [sp+30h] [bp-18h]@6
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (signed int)(unsigned __int8)byRaceCode < 3 )
  {
    v8 = &g_HolySys.m_HolyStoneData[(unsigned __int8)byRaceCode];
    pfStartPos = v8->CreateDummy.m_fCenterPos;
    CPlayer::OutOfMap(v9, v8->pCreateMap, 0, 4, v8->CreateDummy.m_fCenterPos);
    v5 = (char *)v8->pCreateMap->m_pMapSet;
    LOBYTE(pfStartPos) = 4;
    CPlayer::SendMsg_GotoRecallResult(v9, 0, *v5, v8->CreateDummy.m_fCenterPos, 4);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
