/*
 * Function: ?DropGravityStone@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAEKK@Z
 * Address: 0x1403D4A10
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::DropGravityStone(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattle *v7; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleManager *v8; // [sp+40h] [bp+8h]@1
  unsigned int dwCharacSeriala; // [sp+50h] [bp+18h]@1

  dwCharacSeriala = dwCharacSerial;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( dwGuildSerial == -1 )
  {
    result = -115;
  }
  else
  {
    v7 = 0i64;
    v7 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v8, dwGuildSerial);
    if ( v7 )
      result = GUILD_BATTLE::CNormalGuildBattle::DropGravityStone(v7, dwCharacSeriala);
    else
      result = -114;
  }
  return result;
}
