/*
 * Function: ?ClearElapsedSchedule@CGuildBattleReservedSchedule@GUILD_BATTLE@@AEAAXXZ
 * Address: 0x1403DB950
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::ClearElapsedSchedule(GUILD_BATTLE::CGuildBattleReservedSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  ATL::CTime *v3; // rax@10
  __int64 v4; // [sp+0h] [bp-58h]@1
  GUILD_BATTLE::CGuildBattleLogger *v5; // [sp+20h] [bp-38h]@4
  ATL::CTime result; // [sp+38h] [bp-20h]@4
  unsigned int j; // [sp+44h] [bp-14h]@4
  ATL::CTime v8; // [sp+48h] [bp-10h]@10
  GUILD_BATTLE::CGuildBattleReservedSchedule *v9; // [sp+60h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
  ATL::CTime::GetTickCount(&result);
  for ( j = 0; j < 0x17; ++j )
  {
    if ( v9->m_bUseField[j] && v9->m_pkSchedule[j] )
    {
      v3 = GUILD_BATTLE::CGuildBattleSchedule::GetTime(v9->m_pkSchedule[j], &v8);
      if ( ATL::CTime::operator>=(&result, (ATL::CTime)v3->m_time) )
      {
        GUILD_BATTLE::CGuildBattleSchedule::Clear(v9->m_pkSchedule[j]);
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v5,
          "CGuildBattleReservedSchedule::Flip() : SLID(%u) m_pkSchedule[%u]->Clear() Elapased Time!",
          v9->m_uiScheduleListID,
          j);
      }
    }
  }
}
