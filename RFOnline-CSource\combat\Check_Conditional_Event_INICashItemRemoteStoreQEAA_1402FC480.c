/*
 * Function: ?Check_Conditional_Event_INI@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402FC480
 */

void __fastcall CashItemRemoteStore::Check_Conditional_Event_INI(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  _con_event_ini pIni; // [sp+28h] [bp-70h]@4
  _FILETIME pft; // [sp+68h] [bp-30h]@4
  unsigned __int64 v6; // [sp+80h] [bp-18h]@4
  CashItemRemoteStore *v7; // [sp+A0h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v3 ^ _security_cookie;
  CashItemRemoteStore::load_con_event_ini(v7, &pIni, &pft);
  if ( pft.dwHighDateTime != v7->m_con_event.m_conevent_ini_file_time.dwHighDateTime
    || pft.dwLowDateTime != v7->m_con_event.m_conevent_ini_file_time.dwLowDateTime )
  {
    v7->m_con_event.m_conevent_ini_file_time.dwHighDateTime = pft.dwHighDateTime;
    v7->m_con_event.m_conevent_ini_file_time.dwLowDateTime = pft.dwLowDateTime;
    if ( v7->m_con_event.m_ini.m_bUseConEvent && CashItemRemoteStore::isConEventTime(v7) )
    {
      CLogFile::Write(&v7->m_con_event.m_conevent_log, "The New Ini File was disregarded. Other Event is on");
    }
    else
    {
      v7->m_con_event.m_ini.m_bUseConEvent = pIni.m_bUseConEvent;
      v7->m_con_event.m_ini.m_byEventKind = pIni.m_byEventKind;
      v7->m_con_event.m_ini.m_dwCashMin = pIni.m_dwCashMin;
      v7->m_con_event.m_ini.m_iEventTime = pIni.m_iEventTime;
      strcpy_0(v7->m_con_event.m_ini.m_szStartMsg, pIni.m_szStartMsg);
      strcpy_0(v7->m_con_event.m_ini.m_szMiddletMsg, pIni.m_szMiddletMsg);
      strcpy_0(v7->m_con_event.m_ini.m_szEndMsg, pIni.m_szEndMsg);
    }
  }
}
