@echo off
echo ========================================
echo NexusPro Build Script
echo ========================================

set SOLUTION_FILE=..\NexusPro.sln
set BUILD_CONFIG=Release
set PLATFORM=x64

echo.
echo Building NexusPro...
echo Solution: %SOLUTION_FILE%
echo Configuration: %BUILD_CONFIG%
echo Platform: %PLATFORM%
echo.

REM Check if Visual Studio is available
where msbuild >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: MSBuild not found in PATH
    echo Please run this script from a Visual Studio Developer Command Prompt
    pause
    exit /b 1
)

REM Clean previous build
echo Cleaning previous build...
msbuild "%SOLUTION_FILE%" /p:Configuration=%BUILD_CONFIG% /p:Platform=%PLATFORM% /t:Clean /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)

REM Build solution
echo Building solution...
msbuild "%SOLUTION_FILE%" /p:Configuration=%BUILD_CONFIG% /p:Platform=%PLATFORM% /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Output files:
echo - NexusPro.dll: %~dp0%BUILD_CONFIG%\NexusPro.dll
echo - Configuration: %~dp0..\Config\nexuspro.ini
echo.

REM Copy configuration file to output directory
if exist "%~dp0%BUILD_CONFIG%\NexusPro.dll" (
    if exist "%~dp0..\Config\nexuspro.ini" (
        copy "%~dp0..\Config\nexuspro.ini" "%~dp0%BUILD_CONFIG%\" >nul
        echo Configuration file copied to output directory.
    )
)

echo Build process completed.
pause
