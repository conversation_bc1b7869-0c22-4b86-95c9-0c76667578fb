/*
 * Function: ?BuyLimSale@CashItemRemoteStore@@QEAAGEK@Z
 * Address: 0x1402FD950
 */

unsigned __int16 __fastcall CashItemRemoteStore::BuyLimSale(CashItemRemoteStore *this, char byTableCode, unsigned int dwIndex)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CashItemRemoteStore *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = &j;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  for ( j = 0; j < v7->m_lim_event.m_byEventNum; ++j )
  {
    if ( v7->m_lim_event.m_EventItemInfo[j].byTableCode == (unsigned __int8)byTableCode
      && v7->m_lim_event.m_EventItemInfo[j].dwIndex == dwIndex )
    {
      return v7->m_lim_event.m_EventItemInfo[j].wCount;
    }
  }
  return 0;
}
