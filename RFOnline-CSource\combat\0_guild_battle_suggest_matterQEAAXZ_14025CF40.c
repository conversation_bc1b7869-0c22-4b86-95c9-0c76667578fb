/*
 * Function: ??0_guild_battle_suggest_matter@@QEAA@XZ
 * Address: 0x14025CF40
 */

void __fastcall _guild_battle_suggest_matter::_guild_battle_suggest_matter(_guild_battle_suggest_matter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _guild_battle_suggest_matter *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _guild_battle_suggest_matter::Clear(v4);
}
