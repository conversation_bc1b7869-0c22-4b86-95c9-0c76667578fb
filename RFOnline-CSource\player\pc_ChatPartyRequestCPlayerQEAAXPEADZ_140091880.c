/*
 * Function: ?pc_ChatPartyRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x140091880
 */

void __fastcall CPlayer::pc_ChatPartyRequest(CPlayer *this, char *pwszChatData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@8
  CChatStealSystem *v5; // rax@8
  unsigned __int16 v6; // ax@12
  __int64 v7; // [sp+0h] [bp-1B8h]@1
  _chat_message_receipt_udp Dst; // [sp+40h] [bp-178h]@8
  char pbyType; // [sp+174h] [bp-44h]@8
  char v10; // [sp+175h] [bp-43h]@8
  CPartyPlayer **v11; // [sp+188h] [bp-30h]@8
  int j; // [sp+190h] [bp-28h]@8
  unsigned __int64 v13; // [sp+1A0h] [bp-18h]@4
  CPlayer *pPlayer; // [sp+1C0h] [bp+8h]@1
  const char *Str; // [sp+1C8h] [bp+10h]@1

  Str = pwszChatData;
  pPlayer = this;
  v2 = &v7;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( (!pPlayer->m_pUserDB || !pPlayer->m_pUserDB->m_bChatLock && !CPlayer::IsPunished(pPlayer, 0, 1))
    && CPartyPlayer::IsPartyMode(pPlayer->m_pPartyMgr) )
  {
    _chat_message_receipt_udp::_chat_message_receipt_udp(&Dst);
    Dst.byMessageType = 3;
    Dst.dwSenderSerial = pPlayer->m_dwObjSerial;
    Dst.byRaceCode = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
    Dst.bFiltering = 0;
    Dst.bySize = strlen_0(Str);
    memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
    Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
    v4 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
    strcpy_0(Dst.wszSenderName, v4);
    Dst.byPvpGrade = -1;
    pbyType = 2;
    v10 = 10;
    v11 = CPartyPlayer::GetPtrPartyMember(pPlayer->m_pPartyMgr);
    v5 = CChatStealSystem::Instance();
    CChatStealSystem::StealChatMsg(v5, pPlayer, Dst.byMessageType, (char *)Str);
    for ( j = 0; j < 8; ++j )
    {
      if ( v11[j] )
      {
        v6 = _chat_message_receipt_udp::size(&Dst);
        CNetProcess::LoadSendMsg(unk_1414F2088, v11[j]->m_wZoneIndex, &pbyType, &Dst.byMessageType, v6);
      }
    }
  }
}
