/*
 * Function: ??0CashItemRemoteStore@@QEAA@XZ
 * Address: 0x1402F3800
 */

void __fastcall CashItemRemoteStore::CashItemRemoteStore(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  __int64 v4; // [sp+30h] [bp-18h]@4
  CashItemRemoteStore *ptr; // [sp+50h] [bp+8h]@1

  ptr = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  `eh vector constructor iterator'(
    ptr,
    0xB8ui64,
    2,
    (void (__cdecl *)(void *))CLogFile::CLogFile,
    (void (__cdecl *)(void *))CLogFile::~CLogFile);
  CRecordData::CRecordData(&ptr->_kRecGoods);
  CRecordData::CRecordData(&ptr->_kRecConEventMSG);
  _cash_discount_::_cash_discount_(&ptr->m_cde);
  `eh vector constructor iterator'(
    ptr->m_cash_event,
    0x668ui64,
    3,
    (void (__cdecl *)(void *))_cash_event::_cash_event,
    (void (__cdecl *)(void *))_cash_event::~_cash_event);
  _con_event_::_con_event_(&ptr->m_con_event);
  CMyTimer::CMyTimer(&ptr->m_TotalEventTimer);
  CLogFile::CLogFile(&ptr->_kSysLog);
  ptr->_bIsBuyCashItemByGold = 0;
}
