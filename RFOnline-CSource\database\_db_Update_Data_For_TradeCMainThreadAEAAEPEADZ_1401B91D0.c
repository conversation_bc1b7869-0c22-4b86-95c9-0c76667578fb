/*
 * Function: ?_db_Update_Data_For_Trade@CMainThread@@AEAAEPEAD@Z
 * Address: 0x1401B91D0
 */

char __fastcall CMainThread::_db_Update_Data_For_Trade(CMainThread *this, char *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *pSzQuery; // [sp+20h] [bp-28h]@10
  char *v7; // [sp+30h] [bp-18h]@4
  int j; // [sp+38h] [bp-10h]@4
  CMainThread *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pSheet;
  for ( j = 0; j < 2; ++j )
  {
    if ( !CRFWorldDatabase::Update_Dalant(v9->m_pWorldDB, *(_DWORD *)&v7[32 * j], *(_DWORD *)&v7[32 * j + 4]) )
    {
      CLogFile::Write(&v9->m_logSystemError, "Update_Dalant(sr:%d) ..failed ..", *(_DWORD *)&v7[32 * j]);
      return 24;
    }
    if ( !CRFWorldDatabase::Update_Gold(v9->m_pWorldDB, *(_DWORD *)&v7[32 * j], *(_DWORD *)&v7[32 * j + 8]) )
    {
      CLogFile::Write(&v9->m_logSystemError, "_db_Update_Glod(sr:%d) ..failed ..", *(_DWORD *)&v7[32 * j]);
      return 24;
    }
    pSzQuery = pszQuery_0;
    if ( !CMainThread::_db_Update_Inven(
            v9,
            *(_DWORD *)&v7[32 * j],
            *(_AVATOR_DATA **)&v7[32 * j + 16],
            *(_AVATOR_DATA **)&v7[32 * j + 24],
            pszQuery_0) )
    {
      CLogFile::Write(&v9->m_logSystemError, "_db_Update_Inven..failed ..", *(_DWORD *)&v7[32 * j]);
      return 24;
    }
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->m_pWorldDB->vfptr, pszQuery_0, 1) )
      return 24;
  }
  return 0;
}
