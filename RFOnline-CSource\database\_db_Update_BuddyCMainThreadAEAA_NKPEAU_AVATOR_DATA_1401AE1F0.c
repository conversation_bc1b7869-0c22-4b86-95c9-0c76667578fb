/*
 * Function: ?_db_Update_Buddy@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401AE1F0
 */

char __fastcall CMainThread::_db_Update_Buddy(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pwszQuery)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  size_t v7; // rax@9
  __int64 v9; // [sp+0h] [bp-E8h]@1
  char Source; // [sp+30h] [bp-B8h]@4
  char v11; // [sp+31h] [bp-B7h]@4
  char *Dest; // [sp+B8h] [bp-30h]@4
  size_t Size; // [sp+C0h] [bp-28h]@4
  unsigned __int64 v14; // [sp+D0h] [bp-18h]@4
  unsigned int v15; // [sp+F8h] [bp+10h]@1
  _AVATOR_DATA *v16; // [sp+100h] [bp+18h]@1
  _AVATOR_DATA *v17; // [sp+108h] [bp+20h]@1

  v17 = pOldData;
  v16 = pNewData;
  v15 = dwSerial;
  v5 = &v9;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v14 = (unsigned __int64)&v9 ^ _security_cookie;
  Source = 0;
  memset(&v11, 0, 0x7Fui64);
  Dest = pwszQuery;
  sprintf(pwszQuery, "UPDATE tbl_Buddy SET ");
  for ( Size = (unsigned int)strlen_0(Dest); SHIDWORD(Size) < 50; ++HIDWORD(Size) )
  {
    if ( v16->dbBuddy.m_List[SHIDWORD(Size)].dwSerial != v17->dbBuddy.m_List[SHIDWORD(Size)].dwSerial )
    {
      sprintf(&Source, "Serial%d=%d,", HIDWORD(Size), v16->dbBuddy.m_List[SHIDWORD(Size)].dwSerial);
      strcat_0(Dest, &Source);
    }
  }
  v7 = strlen_0(Dest);
  if ( v7 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE Serial=%d", v15);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
