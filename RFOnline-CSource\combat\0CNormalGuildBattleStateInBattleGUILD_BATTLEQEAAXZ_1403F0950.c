/*
 * Function: ??0CNormalGuildBattleStateInBattle@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403F0950
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleStateInBattle::CNormalGuildBattleStateInBattle(GUILD_BATTLE::CNormalGuildBattleStateInBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleStateInBattle *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  GUILD_BATTLE::CNormalGuildBattleState::CNormalGuildBattleState((GUILD_BATTLE::CNormalGuildBattleState *)&v5->vfptr);
  v5->vfptr = (GUILD_BATTLE::CGuildBattleStateVtbl *)&GUILD_BATTLE::CNormalGuildBattleStateInBattle::`vftable';
  ATL::CTimeSpan::CTimeSpan(&v5->m_kInBattleTime, 0i64);
  GUILD_BATTLE::CNormalGuildBattleStateRoundList::CNormalGuildBattleStateRoundList(&v5->m_kRountStateList);
}
