/*
 * Function: ?Clear@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E3660
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::Clear(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattle *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bInit = 0;
  GUILD_BATTLE::CNormalGuildBattleGuild::Clear(&v4->m_k1P);
  GUILD_BATTLE::CNormalGuildBattleGuild::Clear(&v4->m_k2P);
  v4->m_pkField = 0i64;
  v4->m_byWinResult = 0;
  v4->m_pkWin = 0i64;
  v4->m_pkLose = 0i64;
  v4->m_pkRed = 0i64;
  v4->m_pkBlue = 0i64;
  v4->m_byGuildBattleNumber = 0;
  v4->m_pkStateList = &NORMAL_GUILD_BATTLE_STATE_NULL_11;
}
