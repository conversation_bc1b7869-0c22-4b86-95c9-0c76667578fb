/*
 * Function: ?_db_load_raceboss@CMainThread@@AEAAEKPEAU_AVATOR_DATA@@@Z
 * Address: 0x1401A7160
 */

char __fastcall CMainThread::_db_load_raceboss(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pCon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  int v7; // [sp+20h] [bp-28h]@4
  bool *bOverlapVote; // [sp+28h] [bp-20h]@4
  unsigned int *dwCnt; // [sp+30h] [bp-18h]@6
  CMainThread *v10; // [sp+50h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+58h] [bp+10h]@1
  _AVATOR_DATA *v12; // [sp+60h] [bp+18h]@1

  v12 = pCon;
  dwSeriala = dwSerial;
  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  bOverlapVote = &pCon->dbAvator.m_bOverlapVote;
  v7 = CRFWorldDatabase::Select_PatriarchVoted(
         v10->m_pWorldDB,
         pCon->dbAvator.m_byRaceSexCode >> 1,
         dwSerial,
         &pCon->dbAvator.m_bOverlapVote);
  if ( v7 == 1 )
  {
    result = 24;
  }
  else
  {
    dwCnt = &v12->dbAvator.m_dwGivebackCount;
    v7 = CRFWorldDatabase::Select_PatriarchRefundCount(
           v10->m_pWorldDB,
           v12->dbAvator.m_byRaceSexCode >> 1,
           dwSeriala,
           &v12->dbAvator.m_dwGivebackCount);
    if ( v7 )
      v12->dbAvator.m_dwGivebackCount = 0;
    result = 0;
  }
  return result;
}
