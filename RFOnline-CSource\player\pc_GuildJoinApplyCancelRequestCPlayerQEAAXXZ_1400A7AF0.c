/*
 * Function: ?pc_GuildJoinApplyCancelRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400A7AF0
 */

void __fastcall CPlayer::pc_GuildJoinApplyCancelRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  char v4; // [sp+20h] [bp-18h]@4
  CGuild *v5; // [sp+28h] [bp-10h]@4
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = 0;
  v5 = v6->m_Param.m_pApplyGuild;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v6->m_id.wIndex) == 99 )
  {
    v4 = 106;
  }
  else if ( v6->m_Param.m_bGuildLock )
  {
    v4 = -51;
  }
  else if ( v5 )
  {
    if ( !CGuild::GetApplierFromSerial(v5, v6->m_dwObjSerial) )
      v4 = -52;
  }
  else
  {
    v4 = -52;
  }
  if ( !v4 )
  {
    CGuild::PopApplier(v5, v6->m_dwObjSerial, 2);
    v6->m_Param.m_pApplyGuild = 0i64;
  }
  CPlayer::SendMsg_GuildJoinApplyCancelResult(v6, v4);
}
