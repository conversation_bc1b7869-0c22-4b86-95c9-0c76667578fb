/*
 * Function: ?Add@CGuildBattleController@@QEAAEPEAVCGuild@@0KKEK@Z
 * Address: 0x1403D5DB0
 */

char __fastcall CGuildBattleController::Add(CGuildBattleController *this, CGuild *pSrcGuild, CGuild *pDestGuild, unsigned int dwStartTime, unsigned int dwElapseTimeCnt, char byNumber, unsigned int dwMapInx)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v9; // rax@4
  __int64 v11; // [sp+0h] [bp-48h]@1
  CGuild *pSrcGuilda; // [sp+58h] [bp+10h]@1
  CGuild *pDestGuilda; // [sp+60h] [bp+18h]@1
  unsigned int dwStartTimea; // [sp+68h] [bp+20h]@4

  pDestGuilda = pDestGuild;
  pSrcGuilda = pSrcGuild;
  v7 = &v11;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  dwStartTimea = dwStartTime + 1;
  v9 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  return GUILD_BATTLE::CNormalGuildBattleManager::Add(
           v9,
           pSrcGuilda,
           pDestGuilda,
           dwStartTimea,
           dwElapseTimeCnt,
           10 * (byNumber + 1),
           dwMapInx);
}
