/*
 * Function: ?_wait_tst_cash_total_selling_select@CashDbWorker@@IEAAHPEAVTask@@@Z
 * Address: 0x1402F0340
 */

__int64 __fastcall CashDbWorker::_wait_tst_cash_total_selling_select(Cash<PERSON>bWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int *iAvgCashSelling; // [sp+20h] [bp-18h]@4
  int v7; // [sp+28h] [bp-10h]@4
  CashDbWorker *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  iAvgCashSelling = (unsigned int *)Task::GetTaskBuf(pkTsk);
  v7 = CRFCashItemDatabase::CallProc_RFOnlineAvg_Event(v8->_pkDb, iAvgCashSelling);
  return v7 != 0;
}
