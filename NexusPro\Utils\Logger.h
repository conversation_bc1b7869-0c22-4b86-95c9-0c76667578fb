#pragma once

namespace NexusPro {

    // Log levels
    enum class LogLevel {
        Trace = 0,
        Debug = 1,
        Info = 2,
        Warning = 3,
        Error = 4,
        Critical = 5
    };

    // Constants
    namespace Constants {
        const size_t MAX_LOG_MESSAGE = 4096;
    }
    
    class Logger {
    private:
        static std::unique_ptr<Logger> instance_;
        static std::mutex instanceMutex_;
        
        std::wofstream logFile_;
        std::mutex logMutex_;
        LogLevel currentLevel_;
        bool consoleOutput_;
        bool fileOutput_;
        std::wstring logFilePath_;
        
        Logger() : currentLevel_(LogLevel::Info), consoleOutput_(false), fileOutput_(true) {}
        
    public:
        static void Initialize(const std::wstring& logFileName = L"NexusPro.log");
        static void Shutdown();
        static Logger& GetInstance();
        
        // Configuration
        void SetLogLevel(LogLevel level) { currentLevel_ = level; }
        void SetConsoleOutput(bool enabled) { consoleOutput_ = enabled; }
        void SetFileOutput(bool enabled) { fileOutput_ = enabled; }
        
        // Logging methods
        void Log(LogLevel level, const std::wstring& message);
        void Trace(const std::wstring& message) { Log(LogLevel::Trace, message); }
        void Debug(const std::wstring& message) { Log(LogLevel::Debug, message); }
        void Info(const std::wstring& message) { Log(LogLevel::Info, message); }
        void Warning(const std::wstring& message) { Log(LogLevel::Warning, message); }
        void Error(const std::wstring& message) { Log(LogLevel::Error, message); }
        void Critical(const std::wstring& message) { Log(LogLevel::Critical, message); }

        // Singleton pattern
        static Logger& GetInstance() {
            static Logger instance;
            return instance;
        }

        // Static convenience methods
        static void Trace(const std::wstring& message) { GetInstance().Trace(message); }
        static void Debug(const std::wstring& message) { GetInstance().Debug(message); }
        static void Info(const std::wstring& message) { GetInstance().Info(message); }
        static void Warning(const std::wstring& message) { GetInstance().Warning(message); }
        static void Error(const std::wstring& message) { GetInstance().Error(message); }
        static void Critical(const std::wstring& message) { GetInstance().Critical(message); }
        
        // Formatted logging
        template<typename... Args>
        void LogFormat(LogLevel level, const std::wstring& format, Args&&... args) {
            try {
                wchar_t buffer[Constants::MAX_LOG_MESSAGE];
                swprintf_s(buffer, format.c_str(), std::forward<Args>(args)...);
                Log(level, std::wstring(buffer));
            }
            catch (...) {
                Log(LogLevel::Error, L"Failed to format log message");
            }
        }

        template<typename... Args>
        static void InfoFormat(const std::wstring& format, Args&&... args) {
            GetInstance().LogFormat(LogLevel::Info, format, std::forward<Args>(args)...);
        }

        template<typename... Args>
        static void ErrorFormat(const std::wstring& format, Args&&... args) {
            GetInstance().LogFormat(LogLevel::Error, format, std::forward<Args>(args)...);
        }

        template<typename... Args>
        static void WarningFormat(const std::wstring& format, Args&&... args) {
            GetInstance().LogFormat(LogLevel::Warning, format, std::forward<Args>(args)...);
        }

        // Utility methods
        void FlushLog();
        std::wstring GetLogFilePath() const { return logFilePath_; }
        
    private:
        // Singleton pattern - private constructor and destructor
        Logger();
        ~Logger();
        Logger(const Logger&) = delete;
        Logger& operator=(const Logger&) = delete;

        std::wstring GetLevelString(LogLevel level);
        void WriteToFile(const std::wstring& message);
        void WriteToConsole(const std::wstring& message);

        // Member variables
        std::wstring logFilePath_;
        std::wofstream logFile_;
        std::mutex logMutex_;
        LogLevel currentLogLevel_;
        bool enableConsoleOutput_;
        bool enableFileOutput_;
    };
    
    // RAII logging helper for function entry/exit
    class FunctionLogger {
    private:
        std::wstring functionName_;
        std::chrono::high_resolution_clock::time_point startTime_;

    public:
        explicit FunctionLogger(const std::wstring& functionName);
        ~FunctionLogger();
    };
}

// Convenience macros for function logging
#ifdef _DEBUG
#define NEXUS_FUNCTION_LOG() NexusPro::FunctionLogger __func_logger__(__FUNCTIONW__)
#else
#define NEXUS_FUNCTION_LOG()
#endif
