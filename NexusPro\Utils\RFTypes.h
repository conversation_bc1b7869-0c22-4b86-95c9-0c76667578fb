#pragma once

// RF Online specific types and structures based on decompiled code analysis

namespace NexusPro {
namespace RFTypes {
    
    // ============================================================================
    // AUTHENTICATION STRUCTURES
    // ============================================================================
    
    // CBillingManager and related structures
    struct CBillingManager {
        void* vfptr;
        void* m_pBill;  // Points to actual billing implementation
        bool m_bOper;   // Operation flag
    };
    
    struct CUserDB {
        bool m_bActive;
        char m_szAccountID[32];
        DWORD m_dwIP;
        BYTE m_byUserDgr;
        bool m_bChatLock;
        
        struct BillingInfo {
            char szCMS[8];
            __int16 iType;
            _SYSTEMTIME stEndDate;
            int lRemainTime;
        } m_BillingInfo;
    };
    
    // HackShield structures
    struct BASE_HACKSHEILD_PARAM {
        void* vfptr;
        int m_nSocketIndex;
        BYTE m_byVerifyState;
        BYTE m_byGUIDClientInfo[16];
    };
    
    struct HACKSHEILD_PARAM_ANTICP : BASE_HACKSHEILD_PARAM {
        // Additional anti-cheat specific members
    };
    
    struct CHackShieldExSystem {
        void* vfptr;
        // HackShield system members
    };
    
    // ============================================================================
    // NETWORK STRUCTURES
    // ============================================================================
    
    // Network message header
    struct _MSG_HEADER {
        BYTE m_byType[2];   // Message type
        WORD m_wSize;       // Message size
    };
    
    // Network socket structure
    struct _socket {
        bool m_bAccept;
        bool m_bEnterCheck;
        char m_szID[32];
        struct sockaddr_in m_Addr;
        DWORD m_dwLastSendTime;
        DWORD m_dwLastRecvTime;
        DWORD m_dwRecvPopMissTime;
        
        // Speed hack detection
        DWORD m_dwSpeedHackKey[4];
        DWORD m_dwSendSpeedHackTime;
        DWORD m_dwSpeedHackCount;
        
        // FG (anti-cheat) context
        void* m_hFGContext;
    };
    
    // Network buffer
    struct _NET_BUFFER {
        char* m_pBuffer;
        DWORD m_dwSize;
        DWORD m_dwHead;
        DWORD m_dwTail;
    };
    
    // Network process class
    struct CNetProcess {
        void* vfptr;
        void* m_pNetwork;
        int m_nIndex;
        DWORD m_dwCurTime;
        bool m_bUseFG;
        
        struct NetType {
            bool m_bOddMsgWriteLog;
            bool m_bOddMsgDisconnect;
            DWORD m_dwProcessMsgNumPerLoop;
        } m_Type;
        
        void* m_LogFile[3];  // CLogFile array
        void* m_NetSocket;   // CNetSocket
        _NET_BUFFER* m_pRecvBuffer;
        int m_nOddMsgNum;
    };
    
    // ============================================================================
    // MONSTER STRUCTURES
    // ============================================================================
    
    // Monster creation data
    struct _monster_create_setdata {
        void* pParent;
        DWORD dwMapSerial;
        float fPosX, fPosY, fPosZ;
        WORD wMonsterCode;
        BYTE byLevel;
        // Additional creation parameters
    };
    
    // Monster record data
    struct _monster_fld {
        BYTE m_bMonsterCondition;
        WORD m_wMonsterCode;
        BYTE m_byLevel;
        // Monster field data
    };
    
    // Monster hierarchy
    struct CMonsterHierarchy {
        void* vfptr;
        // Hierarchy management data
    };
    
    // Monster aggro manager
    struct CMonsterAggroMgr {
        void* vfptr;
        // Aggro management data
    };
    
    // Monster AI
    struct CMonsterAI {
        void* vfptr;
        // AI state and behavior data
    };
    
    // Monster skill pool
    struct CMonsterSkillPool {
        void* vfptr;
        // Skill management data
    };
    
    // Effect parameter system
    struct _effect_parameter {
        // Effect state management
        DWORD m_dwEffectState[32];  // Bit flags for various effects
    };
    
    // Monster damage tolerance
    struct MonsterSFContDamageToleracne {
        void* vfptr;
        // Damage tolerance data
    };
    
    // Main monster class
    struct CMonster {
        void* vfptr;
        
        // Basic monster state
        bool m_bLive;
        bool m_bOper;
        bool m_bMove;
        
        // Monster identification
        struct ObjID {
            WORD m_wIndex;
        } m_ObjID;
        DWORD m_dwObjSerial;
        
        // Position and movement
        float m_fCurPos[3];
        
        // Monster data
        _monster_fld* m_pMonRec;
        BYTE m_byCreateDate[4];  // Month, Day, Hour, Min
        
        // AI and behavior
        CMonsterAI m_AI;
        CMonsterAggroMgr m_AggroMgr;
        CMonsterHierarchy m_MonHierarcy;
        CMonsterSkillPool m_MonsterSkillPool;
        MonsterSFContDamageToleracne m_SFContDamageTolerance;
        
        // Effects
        _effect_parameter m_EP;
        
        // Static counters
        static int s_nLiveNum;
    };
    
    // ============================================================================
    // PLAYER STRUCTURES
    // ============================================================================
    
    struct CPlayerDB {
        // Player database information
        char m_szCharName[32];
        BYTE m_byRace;
        // Additional player data
    };
    
    struct CPlayer {
        void* vfptr;
        
        // Object identification
        struct ObjID {
            WORD m_wIndex;
        } m_ObjID;
        DWORD m_dwObjSerial;
        
        // Position
        float m_fCurPos[3];
        
        // Database reference
        CUserDB* m_pUserDB;
        CPlayerDB m_Param;
    };
    
    // ============================================================================
    // FUNCTION POINTER TYPES
    // ============================================================================
    
    // Authentication function types
    typedef void(__fastcall* CBillingManager_LoginFunc)(CBillingManager* thisPtr, CUserDB* pUserDB);
    typedef bool(__fastcall* OnCheckSession_FirstVerifyFunc)(CHackShieldExSystem* thisPtr, int n);
    
    // Network function types
    typedef int(__fastcall* CNetProcess_LoadSendMsgFunc)(CNetProcess* thisPtr, unsigned int dwClientIndex, unsigned __int16 wType, char* szMsg, unsigned __int16 nLen);
    typedef void(__fastcall* CNetProcess_PopRecvMsgFunc)(CNetProcess* thisPtr, unsigned __int16 wSocketIndex);
    
    // Monster function types
    typedef char(__fastcall* CMonster_CreateFunc)(CMonster* thisPtr, _monster_create_setdata* pData);
    typedef void(__fastcall* CMonster_LoopFunc)(CMonster* thisPtr);
    
    // ============================================================================
    // CONSTANTS AND ENUMS
    // ============================================================================
    
    // Message types (from network analysis)
    enum MessageTypes {
        MSG_LOGIN = 29,
        MSG_SPEED_HACK_CHECK = 102,
        MSG_ERROR = 57,
        MSG_CHAT = 2,
        MSG_POSITION_FIX = 4
    };
    
    // Billing types
    enum BillingTypes {
        BILLING_FREE = 6,
        BILLING_PREMIUM = 7
    };
    
    // Effect states (for _effect_parameter)
    enum EffectStates {
        EFFECT_STUN = 6,
        EFFECT_FREEZE = 20,
        EFFECT_SLEEP = 28
    };
    
    // ============================================================================
    // UTILITY FUNCTIONS
    // ============================================================================
    
    // Helper functions for RF Online specific operations
    inline bool IsValidMonster(CMonster* pMonster) {
        return pMonster && pMonster->m_bLive && pMonster->m_bOper;
    }
    
    inline bool IsValidPlayer(CPlayer* pPlayer) {
        return pPlayer && pPlayer->m_pUserDB && pPlayer->m_pUserDB->m_bActive;
    }
    
    inline bool IsValidUserDB(CUserDB* pUserDB) {
        return pUserDB && pUserDB->m_bActive && strlen(pUserDB->m_szAccountID) > 0;
    }
    
    // Speed hack detection helpers
    inline DWORD GetCurrentTime() {
        return timeGetTime();
    }
    
    inline bool IsSpeedHackSuspicious(DWORD lastTime, DWORD currentTime, DWORD threshold = 100) {
        return (currentTime - lastTime) < threshold;
    }
    
}}  // namespace NexusPro::RFTypes
