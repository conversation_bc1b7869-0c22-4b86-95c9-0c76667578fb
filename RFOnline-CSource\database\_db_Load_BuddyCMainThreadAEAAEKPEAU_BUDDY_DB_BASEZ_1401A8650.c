/*
 * Function: ?_db_Load_Buddy@CMainThread@@AEAAEKPEAU_BUDDY_DB_BASE@@@Z
 * Address: 0x1401A8650
 */

char __fastcall CMainThread::_db_Load_Buddy(CMainThread *this, unsigned int dwSerial, _BUDDY_DB_BASE *pBuddy)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-508h]@1
  int Dst; // [sp+30h] [bp-4D8h]@4
  char Source[1200]; // [sp+34h] [bp-4D4h]@15
  char v9; // [sp+4E4h] [bp-24h]@4
  char v10; // [sp+4E5h] [bp-23h]@11
  int j; // [sp+4E8h] [bp-20h]@11
  unsigned __int64 v12; // [sp+4F8h] [bp-10h]@4
  CMainThread *v13; // [sp+510h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+518h] [bp+10h]@1
  _BUDDY_DB_BASE *v15; // [sp+520h] [bp+18h]@1

  v15 = pBuddy;
  dwSeriala = dwSerial;
  v13 = this;
  v3 = &v6;
  for ( i = 320i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  memset_0(&Dst, 0, 0x4B0ui64);
  v9 = CRFWorldDatabase::Select_Buddy(v13->m_pWorldDB, dwSeriala, (_worlddb_buddy_info *)&Dst);
  if ( v9 == 1 )
    return 24;
  if ( v9 != 2 )
    goto LABEL_19;
  if ( !CRFWorldDatabase::Insert_Buddy(v13->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_Buddy(v13->m_pWorldDB, dwSeriala, (_worlddb_buddy_info *)&Dst) )
  {
    result = 24;
  }
  else
  {
LABEL_19:
    v10 = 0;
    for ( j = 0; j < 50; ++j )
    {
      v15->m_List[j].dwSerial = *(&Dst + 6 * j);
      if ( *(&Dst + 6 * j) != -1 )
        strcpy_0(v15->m_List[j].wszName, &Source[24 * j]);
    }
    result = 0;
  }
  return result;
}
