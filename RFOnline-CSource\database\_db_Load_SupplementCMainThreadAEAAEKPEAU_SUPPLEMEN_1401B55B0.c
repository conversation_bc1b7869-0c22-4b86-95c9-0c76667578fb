/*
 * Function: ?_db_Load_Supplement@CMainThread@@AEAAEKPEAU_SUPPLEMENT_DB_BASE@@@Z
 * Address: 0x1401B55B0
 */

char __fastcall CMainThread::_db_Load_Supplement(CMainThread *this, unsigned int dwSerial, _SUPPLEMENT_DB_BASE *pDbSupplement)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  size_t v6; // rax@15
  __int64 v7; // [sp+0h] [bp-118h]@1
  _worlddb_character_supplement_info pSupplement; // [sp+28h] [bp-F0h]@4
  char v9; // [sp+64h] [bp-B4h]@4
  unsigned __int64 v10; // [sp+68h] [bp-B0h]@13
  char DstBuf; // [sp+80h] [bp-98h]@15
  char v12; // [sp+81h] [bp-97h]@15
  char Src; // [sp+89h] [bp-8Fh]@15
  char Dst; // [sp+D8h] [bp-40h]@15
  char v15; // [sp+D9h] [bp-3Fh]@15
  int j; // [sp+F4h] [bp-24h]@18
  unsigned __int64 v17; // [sp+100h] [bp-18h]@4
  CMainThread *v18; // [sp+120h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+128h] [bp+10h]@1
  _SUPPLEMENT_DB_BASE *v20; // [sp+130h] [bp+18h]@1

  v20 = pDbSupplement;
  dwSeriala = dwSerial;
  v18 = this;
  v3 = &v7;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = (unsigned __int64)&v7 ^ _security_cookie;
  v9 = CRFWorldDatabase::Select_Supplement(v18->m_pWorldDB, dwSerial, &pSupplement);
  if ( v9 == 1 )
    return 24;
  if ( v9 != 2 )
    goto LABEL_24;
  if ( !CRFWorldDatabase::Insert_Supplement(v18->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_Supplement(v18->m_pWorldDB, dwSeriala, &pSupplement) )
  {
    result = 24;
  }
  else
  {
LABEL_24:
    v20->dPvpPointLeak = pSupplement.dPvpPointLeak;
    v20->bLastAttBuff = pSupplement.bLastAttBuff;
    v20->dwBufPotionEndTime = pSupplement.dwBufPotionEndTime;
    v20->dwRaceBuffClear = pSupplement.dwRaceBuffClear;
    if ( CRFWorldDatabase::Select_Supplement_Ex(v18->m_pWorldDB, dwSeriala, &pSupplement) )
    {
      result = 24;
    }
    else
    {
      v20->byVoted = pSupplement.byVoted;
      v20->dwAccumPlayTime = pSupplement.dwAccumPlayTime;
      v20->dwLastResetDate = pSupplement.dwLastResetDate;
      v20->VoteEnable = pSupplement.VoteEnable;
      v10 = pSupplement.dwScanerCnt;
      if ( pSupplement.dwScanerCnt )
      {
        DstBuf = 0;
        memset(&v12, 0, 0x3Fui64);
        Dst = 0;
        memset(&v15, 0, 9ui64);
        sprintf_s(&DstBuf, 0x40ui64, "%I64u", v10);
        memcpy_0(&Dst, &DstBuf, 9ui64);
        v20->dwScanerGetDate = atoi(&Dst);
        memset_0(&Dst, 0, 0xAui64);
        v6 = strlen_0(&DstBuf);
        memcpy_0(&Dst, &Src, v6 - 9);
        v20->wScanerCnt = atoi(&Dst);
      }
      else
      {
        v20->wScanerCnt = 0;
        v20->dwScanerGetDate = 199001010;
      }
      if ( CRFWorldDatabase::Select_Supplement_ActPoint(v18->m_pWorldDB, dwSeriala, &pSupplement) )
      {
        result = 24;
      }
      else
      {
        for ( j = 0; j < 3; ++j )
          v20->dwActionPoint[j] = pSupplement.dwActionPoint[j];
        result = 0;
      }
    }
  }
  return result;
}
