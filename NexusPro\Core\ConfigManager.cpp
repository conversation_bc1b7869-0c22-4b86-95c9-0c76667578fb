#include "pch.h"
#include "ConfigManager.h"

namespace NexusPro {
    
    ConfigManager::ConfigManager() : stopWatcher_(false) {
        memset(&lastWriteTime_, 0, sizeof(lastWriteTime_));
    }
    
    ConfigManager::~ConfigManager() {
        Shutdown();
    }
    
    bool ConfigManager::Initialize(const std::wstring& configFileName) {
        NEXUS_FUNCTION_LOG();
        
        configFilePath_ = Utils::GetModuleDirectory() + configFileName;
        
        // Create default config if it doesn't exist
        if (!Utils::FileExists(configFilePath_)) {
            NEXUS_INFO(L"Config file not found, creating default: " + configFilePath_);
            
            // Set default values
            SetCoreConfig(CoreConfig());
            SetAuthenticationConfig(AuthenticationConfig());
            SetNetworkConfig(NetworkConfig());
            SetBugFixConfig(BugFixConfig());
            SetEnhancementConfig(EnhancementConfig());
            SetMonitoringConfig(MonitoringConfig());
            
            if (!SaveConfig()) {
                NEXUS_ERROR(L"Failed to create default config file");
                return false;
            }
        }
        
        if (!LoadConfig()) {
            NEXUS_ERROR(L"Failed to load config file");
            return false;
        }
        
        StartFileWatcher();
        
        NEXUS_INFO(L"ConfigManager initialized with file: " + configFilePath_);
        return true;
    }
    
    void ConfigManager::Shutdown() {
        StopFileWatcher();
    }
    
    bool ConfigManager::LoadConfig() {
        NEXUS_FUNCTION_LOG();
        
        std::lock_guard<std::mutex> lock(configMutex_);
        
        std::wifstream file(configFilePath_);
        if (!file.is_open()) {
            NEXUS_ERROR(L"Failed to open config file: " + configFilePath_);
            return false;
        }
        
        configValues_.clear();
        
        std::wstring line;
        while (std::getline(file, line)) {
            ParseConfigLine(line);
        }
        
        file.close();
        UpdateFileTime();
        
        NEXUS_INFO(L"Loaded " + std::to_wstring(configValues_.size()) + L" configuration values");
        return true;
    }
    
    void ConfigManager::ParseConfigLine(const std::wstring& line) {
        // Skip empty lines and comments
        std::wstring trimmed = line;
        trimmed.erase(0, trimmed.find_first_not_of(L" \t"));
        trimmed.erase(trimmed.find_last_not_of(L" \t") + 1);
        
        if (trimmed.empty() || trimmed[0] == L';' || trimmed[0] == L'#') {
            return;
        }
        
        // Find the equals sign
        size_t equalPos = trimmed.find(L'=');
        if (equalPos == std::wstring::npos) {
            return;
        }
        
        std::wstring key = trimmed.substr(0, equalPos);
        std::wstring value = trimmed.substr(equalPos + 1);
        
        // Trim key and value
        key.erase(0, key.find_first_not_of(L" \t"));
        key.erase(key.find_last_not_of(L" \t") + 1);
        value.erase(0, value.find_first_not_of(L" \t"));
        value.erase(value.find_last_not_of(L" \t") + 1);
        
        if (!key.empty()) {
            configValues_[key] = value;
        }
    }
    
    bool ConfigManager::SaveConfig() {
        NEXUS_FUNCTION_LOG();
        
        std::lock_guard<std::mutex> lock(configMutex_);
        
        std::wofstream file(configFilePath_);
        if (!file.is_open()) {
            NEXUS_ERROR(L"Failed to open config file for writing: " + configFilePath_);
            return false;
        }
        
        file << L"; NexusPro Configuration File" << std::endl;
        file << L"; Generated on " << Utils::GetCurrentTimeString() << std::endl;
        file << std::endl;
        
        // Write configuration sections
        file << L"[Core]" << std::endl;
        file << L"[Authentication]" << std::endl;
        file << L"[Network]" << std::endl;
        file << L"[BugFix]" << std::endl;
        file << L"[Enhancement]" << std::endl;
        file << L"[Monitoring]" << std::endl;
        file << std::endl;
        
        // Write all configuration values
        for (const auto& pair : configValues_) {
            file << FormatConfigLine(pair.first, pair.second) << std::endl;
        }
        
        file.close();
        UpdateFileTime();
        
        NEXUS_INFO(L"Saved configuration to: " + configFilePath_);
        return true;
    }
    
    std::wstring ConfigManager::FormatConfigLine(const std::wstring& key, const std::wstring& value) {
        return key + L" = " + value;
    }
    
    std::wstring ConfigManager::GetString(const std::wstring& key, const std::wstring& defaultValue) {
        std::lock_guard<std::mutex> lock(configMutex_);
        auto it = configValues_.find(key);
        return (it != configValues_.end()) ? it->second : defaultValue;
    }
    
    int ConfigManager::GetInt(const std::wstring& key, int defaultValue) {
        return StringToInt(GetString(key), defaultValue);
    }
    
    bool ConfigManager::GetBool(const std::wstring& key, bool defaultValue) {
        return StringToBool(GetString(key), defaultValue);
    }
    
    double ConfigManager::GetDouble(const std::wstring& key, double defaultValue) {
        return StringToDouble(GetString(key), defaultValue);
    }
    
    void ConfigManager::SetString(const std::wstring& key, const std::wstring& value) {
        std::lock_guard<std::mutex> lock(configMutex_);
        configValues_[key] = value;
    }
    
    void ConfigManager::SetInt(const std::wstring& key, int value) {
        SetString(key, IntToString(value));
    }
    
    void ConfigManager::SetBool(const std::wstring& key, bool value) {
        SetString(key, BoolToString(value));
    }
    
    void ConfigManager::SetDouble(const std::wstring& key, double value) {
        SetString(key, DoubleToString(value));
    }
    
    // Helper methods for type conversion
    int ConfigManager::StringToInt(const std::wstring& str, int defaultValue) {
        try {
            return std::stoi(str);
        } catch (...) {
            return defaultValue;
        }
    }
    
    bool ConfigManager::StringToBool(const std::wstring& str, bool defaultValue) {
        std::wstring lower = str;
        std::transform(lower.begin(), lower.end(), lower.begin(), ::towlower);
        
        if (lower == L"true" || lower == L"1" || lower == L"yes" || lower == L"on") {
            return true;
        } else if (lower == L"false" || lower == L"0" || lower == L"no" || lower == L"off") {
            return false;
        }
        
        return defaultValue;
    }
    
    double ConfigManager::StringToDouble(const std::wstring& str, double defaultValue) {
        try {
            return std::stod(str);
        } catch (...) {
            return defaultValue;
        }
    }
    
    std::wstring ConfigManager::BoolToString(bool value) {
        return value ? L"true" : L"false";
    }
    
    std::wstring ConfigManager::IntToString(int value) {
        return std::to_wstring(value);
    }
    
    std::wstring ConfigManager::DoubleToString(double value) {
        return std::to_wstring(value);
    }
    
    std::wstring ConfigManager::MakeSectionKey(const std::wstring& section, const std::wstring& key) {
        return section + L"." + key;
    }
    
    // Configuration section getters
    ConfigManager::CoreConfig ConfigManager::GetCoreConfig() {
        CoreConfig config;
        config.enableLogging = GetBool(MakeSectionKey(L"Core", L"EnableLogging"), config.enableLogging);
        config.logLevel = static_cast<LogLevel>(GetInt(MakeSectionKey(L"Core", L"LogLevel"), static_cast<int>(config.logLevel)));
        config.enableConsoleOutput = GetBool(MakeSectionKey(L"Core", L"EnableConsoleOutput"), config.enableConsoleOutput);
        config.enableFileOutput = GetBool(MakeSectionKey(L"Core", L"EnableFileOutput"), config.enableFileOutput);
        config.hookTimeout = GetInt(MakeSectionKey(L"Core", L"HookTimeout"), config.hookTimeout);
        config.enableDebugMode = GetBool(MakeSectionKey(L"Core", L"EnableDebugMode"), config.enableDebugMode);
        return config;
    }
    
    ConfigManager::AuthenticationConfig ConfigManager::GetAuthenticationConfig() {
        AuthenticationConfig config;
        config.enableAuthHooks = GetBool(MakeSectionKey(L"Authentication", L"EnableAuthHooks"), config.enableAuthHooks);
        config.enableLoginLogging = GetBool(MakeSectionKey(L"Authentication", L"EnableLoginLogging"), config.enableLoginLogging);
        config.enableSessionValidation = GetBool(MakeSectionKey(L"Authentication", L"EnableSessionValidation"), config.enableSessionValidation);
        config.enableAntiSpeedHack = GetBool(MakeSectionKey(L"Authentication", L"EnableAntiSpeedHack"), config.enableAntiSpeedHack);
        config.maxLoginAttempts = GetInt(MakeSectionKey(L"Authentication", L"MaxLoginAttempts"), config.maxLoginAttempts);
        config.sessionTimeout = GetInt(MakeSectionKey(L"Authentication", L"SessionTimeout"), config.sessionTimeout);
        return config;
    }
    
    // Configuration section setters
    void ConfigManager::SetCoreConfig(const CoreConfig& config) {
        SetBool(MakeSectionKey(L"Core", L"EnableLogging"), config.enableLogging);
        SetInt(MakeSectionKey(L"Core", L"LogLevel"), static_cast<int>(config.logLevel));
        SetBool(MakeSectionKey(L"Core", L"EnableConsoleOutput"), config.enableConsoleOutput);
        SetBool(MakeSectionKey(L"Core", L"EnableFileOutput"), config.enableFileOutput);
        SetInt(MakeSectionKey(L"Core", L"HookTimeout"), config.hookTimeout);
        SetBool(MakeSectionKey(L"Core", L"EnableDebugMode"), config.enableDebugMode);
    }
    
    void ConfigManager::SetAuthenticationConfig(const AuthenticationConfig& config) {
        SetBool(MakeSectionKey(L"Authentication", L"EnableAuthHooks"), config.enableAuthHooks);
        SetBool(MakeSectionKey(L"Authentication", L"EnableLoginLogging"), config.enableLoginLogging);
        SetBool(MakeSectionKey(L"Authentication", L"EnableSessionValidation"), config.enableSessionValidation);
        SetBool(MakeSectionKey(L"Authentication", L"EnableAntiSpeedHack"), config.enableAntiSpeedHack);
        SetInt(MakeSectionKey(L"Authentication", L"MaxLoginAttempts"), config.maxLoginAttempts);
        SetInt(MakeSectionKey(L"Authentication", L"SessionTimeout"), config.sessionTimeout);
    }
    
    bool ConfigManager::Reload() {
        NEXUS_INFO(L"Reloading configuration...");
        return LoadConfig();
    }
    
    void ConfigManager::StartFileWatcher() {
        if (!watcherThread_.joinable()) {
            stopWatcher_ = false;
            watcherThread_ = std::thread(&ConfigManager::FileWatcherThread, this);
        }
    }
    
    void ConfigManager::StopFileWatcher() {
        if (watcherThread_.joinable()) {
            stopWatcher_ = true;
            watcherThread_.join();
        }
    }
    
    void ConfigManager::FileWatcherThread() {
        while (!stopWatcher_) {
            if (HasFileChanged()) {
                NEXUS_INFO(L"Configuration file changed, reloading...");
                LoadConfig();
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(Constants::CONFIG_CHECK_INTERVAL_MS));
        }
    }
    
    bool ConfigManager::HasFileChanged() {
        WIN32_FILE_ATTRIBUTE_DATA fileData;
        if (GetFileAttributesExW(configFilePath_.c_str(), GetFileExInfoStandard, &fileData)) {
            return CompareFileTime(&fileData.ftLastWriteTime, &lastWriteTime_) != 0;
        }
        return false;
    }
    
    void ConfigManager::UpdateFileTime() {
        WIN32_FILE_ATTRIBUTE_DATA fileData;
        if (GetFileAttributesExW(configFilePath_.c_str(), GetFileExInfoStandard, &fileData)) {
            lastWriteTime_ = fileData.ftLastWriteTime;
        }
    }

    // ============================================================================
    // MISSING FUNCTION IMPLEMENTATIONS (STUBS)
    // ============================================================================

    ConfigManager::NetworkConfig ConfigManager::GetNetworkConfig() {
        // TODO: Implement network config getter
        return NetworkConfig();
    }

    ConfigManager::BugFixConfig ConfigManager::GetBugFixConfig() {
        // TODO: Implement bug fix config getter
        return BugFixConfig();
    }

    ConfigManager::EnhancementConfig ConfigManager::GetEnhancementConfig() {
        // TODO: Implement enhancement config getter
        return EnhancementConfig();
    }

    ConfigManager::MonitoringConfig ConfigManager::GetMonitoringConfig() {
        // TODO: Implement monitoring config getter
        return MonitoringConfig();
    }

    void ConfigManager::SetNetworkConfig(const NetworkConfig& config) {
        // TODO: Implement network config setter
        NEXUS_INFO(L"Network configuration updated (stub)");
    }

    void ConfigManager::SetBugFixConfig(const BugFixConfig& config) {
        // TODO: Implement bug fix config setter
        NEXUS_INFO(L"Bug fix configuration updated (stub)");
    }

    void ConfigManager::SetEnhancementConfig(const EnhancementConfig& config) {
        // TODO: Implement enhancement config setter
        NEXUS_INFO(L"Enhancement configuration updated (stub)");
    }

    void ConfigManager::SetMonitoringConfig(const MonitoringConfig& config) {
        // TODO: Implement monitoring config setter
        NEXUS_INFO(L"Monitoring configuration updated (stub)");
    }

    bool ConfigManager::HasKey(const std::wstring& key) {
        // TODO: Implement key existence check
        return configData_.find(key) != configData_.end();
    }

    void ConfigManager::RemoveKey(const std::wstring& key) {
        // TODO: Implement key removal
        configData_.erase(key);
        NEXUS_INFO(L"Key removed: " + key);
    }

    std::vector<std::wstring> ConfigManager::GetAllKeys() {
        // TODO: Implement key enumeration
        std::vector<std::wstring> keys;
        for (const auto& pair : configData_) {
            keys.push_back(pair.first);
        }
        return keys;
    }
}
