/*
 * Function: ?mgr_whisper@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BAE10
 */

char __fastcall CPlayer::mgr_whisper(CPlayer *this, char *pwszMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@9
  __int64 v6; // [sp+0h] [bp-58h]@1
  bool bFilter; // [sp+20h] [bp-38h]@9
  char *pwszMessage; // [sp+28h] [bp-30h]@9
  char byPvpGrade; // [sp+30h] [bp-28h]@9
  char *pwszSender; // [sp+38h] [bp-20h]@9
  int j; // [sp+40h] [bp-18h]@4
  char *v12; // [sp+48h] [bp-10h]@9
  CPlayer *v13; // [sp+60h] [bp+8h]@1
  char *v14; // [sp+68h] [bp+10h]@1

  v14 = pwszMsg;
  v13 = this;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 2532; ++j )
  {
    if ( g_UserDB[j].m_bActive )
    {
      if ( g_UserDB[j].m_byUserDgr >= 2 )
      {
        v12 = CPlayerDB::GetCharNameW(&v13->m_Param);
        v4 = CPlayerDB::GetRaceCode(&v13->m_Param);
        pwszSender = v12;
        byPvpGrade = v13->m_Param.m_byPvPGrade;
        pwszMessage = v14;
        bFilter = 0;
        CPlayer::SendData_ChatTrans(&g_Player + j, 5, v13->m_dwObjSerial, v4, 0, v14, byPvpGrade, v12);
      }
    }
  }
  return 1;
}
