/*
 * Function: ?_init_loggers@CashDbWorker@@AEAA_NXZ
 * Address: 0x1402F0E50
 */

char __fastcall CashDbWorker::_init_loggers(CashDbWorker *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  unsigned int v4; // eax@4
  __int64 v6; // [sp+0h] [bp-168h]@1
  char _Dest[256]; // [sp+40h] [bp-128h]@4
  unsigned __int64 v8; // [sp+150h] [bp-18h]@4
  CashDbWorker *v9; // [sp+170h] [bp+8h]@1

  v9 = this;
  v1 = &v6;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  CreateDirectoryA("..\\ZoneServerLog\\ServiceLog\\PartiallyPaid", 0i64);
  CreateDirectoryA("..\\ZoneServerLog\\SystemLog\\PartiallyPaid", 0i64);
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  v3 = GetKorLocalTime();
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\ServiceLog\\PartiallyPaid\\%d.his", v3);
  CLogFile::SetWriteLogFile(&v9->_kLogger[1], _Dest, 1, 0, 1, 1);
  memset_0(_Dest, 0, 0x100ui64);
  v4 = GetKorLocalTime();
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\SystemLog\\PartiallyPaid\\%d.sys", v4);
  CLogFile::SetWriteLogFile(v9->_kLogger, _Dest, 1, 0, 1, 1);
  return 1;
}
