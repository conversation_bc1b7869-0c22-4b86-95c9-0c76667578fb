/*
 * Function: ?mgr_tracing@CPlayer@@QEAA_N_N@Z
 * Address: 0x1400B86E0
 */

char __fastcall CPlayer::mgr_tracing(CPlayer *this, bool bOn)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1
  bool v7; // [sp+38h] [bp+10h]@1

  v7 = bOn;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_bObserver == bOn )
  {
    result = 0;
  }
  else
  {
    CPlayer::_AnimusReturn(v6, 1);
    CPlayer::SendMsg_Destroy(v6);
    v6->m_bObserver = v7;
    CPlayer::SendMsg_NewViewOther(v6, 0);
    result = 1;
  }
  return result;
}
