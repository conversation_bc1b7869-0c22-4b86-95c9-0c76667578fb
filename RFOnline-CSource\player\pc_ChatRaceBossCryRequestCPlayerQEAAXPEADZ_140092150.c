/*
 * Function: ?pc_ChatRaceBossCryRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x140092150
 */

void __fastcall CPlayer::pc_ChatRaceBossCryRequest(CPlayer *this, char *pwszChatData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@5
  unsigned int v5; // eax@5
  char *v6; // rax@6
  int v7; // eax@10
  __int64 v8; // [sp+0h] [bp-1C8h]@1
  _announ_message_receipt_udp Dst; // [sp+40h] [bp-188h]@6
  char pbyType; // [sp+174h] [bp-54h]@6
  char v11; // [sp+175h] [bp-53h]@6
  int v12; // [sp+184h] [bp-44h]@6
  int j; // [sp+188h] [bp-40h]@6
  CPlayer *v14; // [sp+190h] [bp-38h]@9
  int v15; // [sp+1A0h] [bp-28h]@5
  unsigned int v16; // [sp+1A4h] [bp-24h]@5
  int v17; // [sp+1A8h] [bp-20h]@10
  unsigned __int64 v18; // [sp+1B0h] [bp-18h]@4
  CPlayer *v19; // [sp+1D0h] [bp+8h]@1
  const char *Str; // [sp+1D8h] [bp+10h]@1

  Str = pwszChatData;
  v19 = this;
  v2 = &v8;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v18 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( v19->m_pUserDB )
  {
    v15 = CPlayerDB::GetRaceCode(&v19->m_Param);
    v4 = CPvpUserAndGuildRankingSystem::Instance();
    v16 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v15, 0);
    v5 = CPlayerDB::GetCharSerial(&v19->m_Param);
    if ( v16 == v5 )
    {
      _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
      Dst.byMessageType = 18;
      Dst.bySenderRace = CPlayerDB::GetRaceCode(&v19->m_Param);
      v6 = CPlayerDB::GetCharNameW(&v19->m_Param);
      strcpy_0(Dst.wszSenderName, v6);
      Dst.bySize = strlen_0(Str);
      memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
      Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
      Dst.dwSenderSerial = v19->m_dwObjSerial;
      Dst.byPvpGrade = -1;
      pbyType = 2;
      v11 = 11;
      v12 = _announ_message_receipt_udp::size(&Dst);
      for ( j = 0; j < 2532; ++j )
      {
        v14 = &g_Player + j;
        if ( v14->m_bOper )
        {
          v17 = CPlayerDB::GetRaceCode(&v14->m_Param);
          v7 = CPlayerDB::GetRaceCode(&v19->m_Param);
          if ( v17 == v7 && v14->m_pCurMap == v19->m_pCurMap && v14->m_wMapLayerIndex == v19->m_wMapLayerIndex )
            CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v12);
        }
      }
    }
  }
}
