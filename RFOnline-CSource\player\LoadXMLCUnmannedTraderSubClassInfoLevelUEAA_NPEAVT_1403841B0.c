/*
 * Function: ?LoadXML@CUnmannedTraderSubClassInfoLevel@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@KK@Z
 * Address: 0x1403841B0
 */

char __fastcall CUnmannedTraderSubClassInfoLevel::LoadXML(CUnmannedTraderSubClassInfoLevel *this, TiXmlElement *elemSubClass, CLogFile *kLogger, unsigned int dwDivisionID, unsigned int dwClassID)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v8; // [sp+0h] [bp-88h]@1
  unsigned int v9; // [sp+20h] [bp-68h]@7
  int v10; // [sp+28h] [bp-60h]@7
  int v11; // [sp+30h] [bp-58h]@7
  int v12; // [sp+44h] [bp-44h]@4
  int v13; // [sp+64h] [bp-24h]@4
  CUnmannedTraderSubClassInfoLevel *v14; // [sp+90h] [bp+8h]@1
  TiXmlElement *v15; // [sp+98h] [bp+10h]@1
  CLogFile *v16; // [sp+A0h] [bp+18h]@1
  unsigned int v17; // [sp+A8h] [bp+20h]@1

  v17 = dwDivisionID;
  v16 = kLogger;
  v15 = elemSubClass;
  v14 = this;
  v5 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v12 = 0;
  v13 = 0;
  if ( TiXmlElement::Attribute(elemSubClass, "min", &v12) && v12 >= 0 && v12 <= 255 )
  {
    if ( TiXmlElement::Attribute(v15, "max", &v13) && v13 >= 0 && v13 <= 255 )
    {
      if ( v13 >= v12 )
      {
        v14->m_byMin = v12;
        v14->m_byMax = v13;
        result = 1;
      }
      else
      {
        v11 = v12;
        v10 = v13;
        v9 = v14->m_dwID;
        CLogFile::Write(
          v16,
          "CUnmannedTraderSubClassInfoCode::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger, DWORD dwDivisionID, DW"
          "ORD dwClassID )\r\n"
          "\t\tDivisionID(%u), ClassID(%u) iMax(%d) < iMin(%d)!\r\n",
          v17,
          dwClassID);
        result = 0;
      }
    }
    else
    {
      v11 = v13;
      v10 = v13;
      v9 = v14->m_dwID;
      CLogFile::Write(
        v16,
        "CUnmannedTraderSubClassInfoCode::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger, DWORD dwDivisionID, DWOR"
        "D dwClassID )\r\n"
        "\t\tDivisionID(%u), ClassID(%u) 0 == elemSubClass->Attribute( max, &iMax ) || 0 > iMax(%d) || 255 < iMax(%d)!\r\n",
        v17,
        dwClassID);
      result = 0;
    }
  }
  else
  {
    v11 = v12;
    v10 = v12;
    v9 = v14->m_dwID;
    CLogFile::Write(
      v16,
      "CUnmannedTraderSubClassInfoCode::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger, DWORD dwDivisionID, DWORD "
      "dwClassID )\r\n"
      "\t\tDivisionID(%u), ClassID(%u) 0 == elemSubClass->Attribute( min, &iMin ) || 0 > iMin(%d) || 255 < iMin(%d)!\r\n",
      v17,
      dwClassID);
    result = 0;
  }
  return result;
}
