/*
 * Function: ?mgr_TrunkInit@CPlayer@@QEAA_NXZ
 * Address: 0x1400B8EA0
 */

char __fastcall CPlayer::mgr_TrunkInit(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@6
  char v5; // al@11
  char v6; // al@13
  char v7; // al@16
  __int64 v8; // [sp+0h] [bp-48h]@1
  int j; // [sp+30h] [bp-18h]@4
  int k; // [sp+34h] [bp-14h]@11
  CPlayer *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; ; ++j )
  {
    v3 = CPlayerDB::GetTrunkSlotNum(&v11->m_Param);
    if ( j >= (unsigned __int8)v3 )
      break;
    if ( v11->m_Param.m_pStoragePtr[5]->m_pStorageList[j].m_bLoad
      && !CPlayer::Emb_DelStorage(v11, 5, j, 0, 0, "CPlayer::mgr_TrunkInit()") )
    {
      return 0;
    }
  }
  v11->m_Param.m_byTrunkSlotNum = 0;
  _STORAGE_LIST::SetUseListNum((_STORAGE_LIST *)&v11->m_Param.m_dbTrunk.m_nListNum, 0);
  v5 = CPlayerDB::GetTrunkSlotNum(&v11->m_Param);
  CUserDB::Update_TrunkSlotNum(v11->m_pUserDB, v5);
  for ( k = 0; ; ++k )
  {
    v6 = CPlayerDB::GetExtTrunkSlotNum(&v11->m_Param);
    if ( k >= (unsigned __int8)v6 )
      break;
    if ( v11->m_Param.m_pStoragePtr[7]->m_pStorageList[k].m_bLoad )
      CPlayer::Emb_DelStorage(v11, 7, k, 0, 0, "CPlayer::mgr_TrunkInit()");
  }
  v11->m_Param.m_byExtTrunkSlotNum = 0;
  _STORAGE_LIST::SetUseListNum((_STORAGE_LIST *)&v11->m_Param.m_dbExtTrunk.m_nListNum, 0);
  v7 = CPlayerDB::GetExtTrunkSlotNum(&v11->m_Param);
  CUserDB::Update_ExtTrunkSlotNum(v11->m_pUserDB, v7);
  return 1;
}
