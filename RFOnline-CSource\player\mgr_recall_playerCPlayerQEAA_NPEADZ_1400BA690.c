/*
 * Function: ?mgr_recall_player@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BA690
 */

char __fastcall CPlayer::mgr_recall_player(CPlayer *this, char *pwszCharName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-38h]@1
  CUserDB *v7; // [sp+20h] [bp-18h]@6
  CPlayer *v8; // [sp+28h] [bp-10h]@7
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_pCurMap->m_pMapSet->m_nMapType )
  {
    result = 0;
  }
  else
  {
    v7 = SearchAvatorWithName(g_UserDB, 2532, pwszCharName);
    if ( v7 )
    {
      v8 = &g_Player + v7->m_idWorld.wIndex;
      if ( v8->m_bLive )
      {
        v5 = CPlayerDB::GetCharNameW(&v9->m_Param);
        CPlayer::pc_GotoAvatorRequest(v8, v5);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
