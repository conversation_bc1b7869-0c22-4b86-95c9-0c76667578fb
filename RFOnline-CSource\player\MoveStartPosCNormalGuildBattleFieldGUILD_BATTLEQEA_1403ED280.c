/*
 * Function: ?MoveStartPos@CNormalGuildBattleField@GUILD_BATTLE@@QEAA_NEEPEAVCPlayer@@@Z
 * Address: 0x1403ED280
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::MoveStartPos(GUILD_BATTLE::CNormalGuildBattleField *this, char byStartPos, char byMapOutType, CPlayer *pkPlayer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v7; // rax@14
  __int64 v8; // [sp+0h] [bp-78h]@1
  float *pfStartPos; // [sp+20h] [bp-58h]@14
  _dummy_position *pPos; // [sp+30h] [bp-48h]@6
  float pNewPos; // [sp+48h] [bp-30h]@12
  GUILD_BATTLE::CNormalGuildBattleField *v12; // [sp+80h] [bp+8h]@1
  char v13; // [sp+90h] [bp+18h]@1
  CPlayer *v14; // [sp+98h] [bp+20h]@1

  v14 = pkPlayer;
  v13 = byMapOutType;
  v12 = this;
  v4 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)byStartPos < 2 )
  {
    pPos = v12->m_pkStartPos[(unsigned __int8)byStartPos];
    if ( v12->m_pkMap && pkPlayer && pPos )
    {
      if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pkPlayer->m_id.wIndex) == 99 )
      {
        result = 0;
      }
      else
      {
        CPlayer::pc_Resurrect(v14, 0);
        if ( !CMapData::GetRandPosInDummy(v12->m_pkMap, pPos, &pNewPos, 1) )
          memcpy_0(&pNewPos, pPos->m_fCenterPos, 0xCui64);
        pfStartPos = &pNewPos;
        CPlayer::OutOfMap(v14, v12->m_pkMap, 0, v13, &pNewPos);
        v7 = (char *)v12->m_pkMap->m_pMapSet;
        LOBYTE(pfStartPos) = v13;
        CPlayer::SendMsg_GotoRecallResult(v14, 0, *v7, &pNewPos, v13);
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
