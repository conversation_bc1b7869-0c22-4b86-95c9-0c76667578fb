/*
 * Function: ?_db_Load_NpcQuest_History@CMainThread@@AEAAEKPEAU_QUEST_DB_BASE@@@Z
 * Address: 0x1401A80D0
 */

char __fastcall CMainThread::_db_Load_NpcQuest_History(CMainThread *this, unsigned int dwSerial, _QUEST_DB_BASE *pCon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-4B8h]@1
  _worlddb_npc_quest_complete_history pNpcQHis; // [sp+30h] [bp-488h]@4
  char v8; // [sp+494h] [bp-24h]@4
  int j; // [sp+498h] [bp-20h]@11
  unsigned __int64 v10; // [sp+4A8h] [bp-10h]@4
  CMainThread *v11; // [sp+4C0h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+4C8h] [bp+10h]@1
  _QUEST_DB_BASE *v13; // [sp+4D0h] [bp+18h]@1

  v13 = pCon;
  dwSeriala = dwSerial;
  v11 = this;
  v3 = &v6;
  for ( i = 300i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v6 ^ _security_cookie;
  _worlddb_npc_quest_complete_history::_worlddb_npc_quest_complete_history(&pNpcQHis);
  v8 = CRFWorldDatabase::Select_NpcQuest_History(v11->m_pWorldDB, dwSeriala, &pNpcQHis);
  if ( v8 == 1 )
    return 24;
  if ( v8 != 2 )
    goto LABEL_17;
  if ( !CRFWorldDatabase::Insert_NpcQuest_History(v11->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_NpcQuest_History(v11->m_pWorldDB, dwSeriala, &pNpcQHis) )
  {
    result = 24;
  }
  else
  {
LABEL_17:
    for ( j = 0; j < 70; ++j )
    {
      strcpy_0(v13->m_History[j].szQuestCode, (const char *)&pNpcQHis + 16 * j);
      v13->m_History[j].byLevel = pNpcQHis.List[j].byLevel;
      v13->m_History[j].dwEventEndTime = pNpcQHis.List[j].dwEventEndTime;
    }
    result = 0;
  }
  return result;
}
