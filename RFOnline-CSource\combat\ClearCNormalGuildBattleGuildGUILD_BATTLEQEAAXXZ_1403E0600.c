/*
 * Function: ?Clear@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E0600
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::Clear(GUILD_BATTLE::CNormalGuildBattleGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@7
  GUILD_BATTLE::CNormalGuildBattleGuild *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6->m_byColorInx = 0;
  v6->m_dwGoalCnt = 0;
  v6->m_dwScore = 0;
  v6->m_dwKillPoint = 0;
  v6->m_pkGuild = 0i64;
  for ( j = 0; j < 150; ++j )
    GUILD_BATTLE::CNormalGuildBattleGuildMember::Clear(&v6->m_kMember[j]);
  v6->m_byNotifyPositionMemberCnt = 0;
  for ( k = 0; k < 10; ++k )
    v6->m_pkNotifyPositionMember[k] = 0i64;
  v6->m_dwCurJoinMember = 0;
  v6->m_dwKillCountSum = 0;
  v6->m_dwMaxJoinMemberCnt = 0;
}
