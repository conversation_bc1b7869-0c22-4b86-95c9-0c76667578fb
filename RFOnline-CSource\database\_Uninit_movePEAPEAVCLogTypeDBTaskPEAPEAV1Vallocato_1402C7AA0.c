/*
 * Function: ??$_Uninit_move@PEAPEAVCLogTypeDBTask@@PEAPEAV1@V?$allocator@PEAVCLogTypeDBTask@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCLogTypeDBTask@@PEAPEAV1@00AEAV?$allocator@PEAVCLogTypeDBTask@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1402C7AA0
 */

CLogTypeDBTask **__fastcall std::_Uninit_move<CLogTypeDBTask * *,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>,std::_Undefined_move_tag>(CLogTypeDBTask **_First, CLogTypeDBTask **_Last, CLogTypeDBTask **_Dest, std::allocator<CLogTypeDBTask *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  CLogTypeDBTask **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<CLogTypeDBTask * *,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
