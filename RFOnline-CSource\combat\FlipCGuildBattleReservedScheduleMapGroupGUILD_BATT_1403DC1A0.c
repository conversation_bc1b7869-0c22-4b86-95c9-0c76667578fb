/*
 * Function: ?Flip@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403DC1A0
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Flip(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@5
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_ppkReservedSchedule )
  {
    for ( j = 0; j < v5->m_uiMapCnt; ++j )
    {
      if ( v5->m_ppkReservedSchedule[j] )
        GUILD_BATTLE::CGuildBattleReservedSchedule::Flip(v5->m_ppkReservedSchedule[j]);
    }
  }
}
