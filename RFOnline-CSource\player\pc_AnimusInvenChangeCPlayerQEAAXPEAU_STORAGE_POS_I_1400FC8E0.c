/*
 * Function: ?pc_AnimusInvenChange@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@G@Z
 * Address: 0x1400FC8E0
 */

void __fastcall CPlayer::pc_AnimusInvenChange(CPlayer *this, _STORAGE_POS_INDIV *pItem, unsigned __int16 wReplaceSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-178h]@1
  bool bDelete; // [sp+20h] [bp-158h]@60
  char *strErrorCodePos; // [sp+28h] [bp-150h]@60
  char v8; // [sp+30h] [bp-148h]@4
  void *Src; // [sp+38h] [bp-140h]@4
  void *v10; // [sp+40h] [bp-138h]@4
  _STORAGE_LIST *v11; // [sp+48h] [bp-130h]@4
  _STORAGE_LIST *v12; // [sp+50h] [bp-128h]@4
  _base_fld *v13; // [sp+58h] [bp-120h]@4
  int j; // [sp+60h] [bp-118h]@43
  char v15; // [sp+64h] [bp-114h]@37
  __int64 v16; // [sp+68h] [bp-110h]@45
  char *v17; // [sp+70h] [bp-108h]@55
  _STORAGE_LIST::_db_con Dst; // [sp+88h] [bp-F0h]@60
  _STORAGE_LIST::_db_con v19; // [sp+D8h] [bp-A0h]@63
  _STORAGE_LIST::_db_con v20; // [sp+128h] [bp-50h]@68
  CPlayer *v21; // [sp+180h] [bp+8h]@1
  _STORAGE_POS_INDIV *v22; // [sp+188h] [bp+10h]@1
  unsigned __int16 v23; // [sp+190h] [bp+18h]@1

  v23 = wReplaceSerial;
  v22 = pItem;
  v21 = this;
  v3 = &v5;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = 0;
  Src = 0i64;
  v10 = 0i64;
  v11 = v21->m_Param.m_pStoragePtr[pItem->byStorageCode];
  v12 = 0i64;
  v13 = 0i64;
  if ( pItem->byStorageCode )
  {
    if ( pItem->byStorageCode != 4 )
      return;
    v12 = v21->m_Param.m_pStoragePtr[0];
  }
  else
  {
    v12 = v21->m_Param.m_pStoragePtr[4];
  }
  Src = _STORAGE_LIST::GetPtrFromSerial(v11, pItem->wItemSerial);
  if ( Src )
  {
    if ( *((_BYTE *)Src + 1) == 24 )
    {
      if ( *((_BYTE *)Src + 19) )
      {
        v8 = 11;
      }
      else if ( Src == v21->m_pRecalledAnimusItem )
      {
        v8 = 6;
      }
      else
      {
        if ( v21->m_pCurMap->m_pMapSet->m_nMapType != 1 )
        {
          if ( v23 != 0xFFFF )
          {
            v10 = _STORAGE_LIST::GetPtrFromSerial(v12, v23);
            if ( !v10 )
            {
              v8 = 2;
              goto $RESULT_95;
            }
            if ( *((_BYTE *)v10 + 1) != 24 )
            {
              v8 = 2;
              goto $RESULT_95;
            }
            if ( *((_BYTE *)v10 + 19) )
            {
              v8 = 11;
              goto $RESULT_95;
            }
            if ( *(_WORD *)((char *)v10 + 3) != *(_WORD *)((char *)Src + 3) )
            {
              v8 = 3;
              goto $RESULT_95;
            }
            if ( v10 == v21->m_pRecalledAnimusItem )
            {
              v8 = 6;
              goto $RESULT_95;
            }
          }
          if ( v23 != 0xFFFF || _STORAGE_LIST::GetIndexEmptyCon(v12) != 255 )
          {
            if ( v23 == 0xFFFF && !v22->byStorageCode )
            {
              v13 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 24, *(_WORD *)((char *)Src + 3));
              if ( v13 )
              {
                if ( v21->m_bFreeSFByClass )
                  goto LABEL_77;
                v15 = 0;
                if ( *(_DWORD *)&v13[3].m_strCode[60] == 1 )
                {
                  if ( v21->m_Param.m_pClassHistory[0]
                    && v21->m_Param.m_pClassHistory[0]->m_nClass == 3
                    && v21->m_Param.m_pClassData->m_nClass == 3 )
                  {
                    v15 = 1;
                  }
                }
                else
                {
                  for ( j = 0; j < 4; ++j )
                  {
                    v16 = (__int64)*v21->m_Param.m_ppHistoryEffect[j];
                    if ( !v16 )
                      break;
                    if ( *(_DWORD *)(v16 + 1440) )
                    {
                      v15 = 1;
                      break;
                    }
                  }
                }
                if ( v15 )
                {
LABEL_77:
                  for ( j = 0; j < v12->m_nUsedNum; ++j )
                  {
                    v17 = &v12->m_pStorageList[j].m_bLoad;
                    if ( *v17 && *(_WORD *)(v17 + 3) == *(_WORD *)((char *)Src + 3) )
                    {
                      v8 = 4;
                      goto $RESULT_95;
                    }
                  }
                }
                else
                {
                  v8 = 7;
                }
              }
              else
              {
                v8 = 8;
              }
            }
          }
          else
          {
            v8 = 5;
          }
          goto $RESULT_95;
        }
        v8 = 12;
      }
    }
    else
    {
      v8 = 1;
    }
  }
  else
  {
    v8 = 1;
  }
$RESULT_95:
  if ( !v8 )
  {
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    memcpy_0(&Dst, Src, 0x32ui64);
    strErrorCodePos = "CPlayer::pc_AnimusInvenChange() -- 0";
    bDelete = 0;
    if ( !CPlayer::Emb_DelStorage(
            v21,
            v11->m_nListCode,
            *((_BYTE *)Src + 49),
            0,
            0,
            "CPlayer::pc_AnimusInvenChange() -- 0") )
    {
      CPlayer::SendMsg_AnimusInvenChange(v21, -1);
      return;
    }
    if ( v10 )
    {
      _STORAGE_LIST::_db_con::_db_con(&v19);
      memcpy_0(&v19, v10, 0x32ui64);
      bDelete = 0;
      if ( !CPlayer::Emb_AddStorage(v21, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&v19.m_bLoad, 1, 0) )
      {
        bDelete = 0;
        CPlayer::Emb_AddStorage(v21, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
        CPlayer::SendMsg_AnimusInvenChange(v21, -1);
        return;
      }
      strErrorCodePos = "CPlayer::pc_AnimusInvenChange() -- 1";
      bDelete = 0;
      if ( !CPlayer::Emb_DelStorage(
              v21,
              v12->m_nListCode,
              *((_BYTE *)v10 + 49),
              0,
              0,
              "CPlayer::pc_AnimusInvenChange() -- 1") )
      {
        strErrorCodePos = "CPlayer::pc_AnimusInvenChange() -- 1 Fail";
        bDelete = 0;
        CPlayer::Emb_DelStorage(
          v21,
          v11->m_nListCode,
          *((_BYTE *)v10 + 49),
          0,
          0,
          "CPlayer::pc_AnimusInvenChange() -- 1 Fail");
        bDelete = 0;
        CPlayer::Emb_AddStorage(v21, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
        CPlayer::SendMsg_AnimusInvenChange(v21, -1);
        return;
      }
    }
    if ( v10 )
    {
      _STORAGE_LIST::_db_con::_db_con(&v20);
      memcpy_0(&v20, v10, 0x32ui64);
      bDelete = 0;
      if ( !CPlayer::Emb_AddStorage(v21, v12->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0) )
      {
        bDelete = 0;
        CPlayer::Emb_AddStorage(v21, v12->m_nListCode, (_STORAGE_LIST::_storage_con *)&v20.m_bLoad, 1, 0);
        strErrorCodePos = "CPlayer::pc_AnimusInvenChange() -- 1 Fail";
        bDelete = 0;
        CPlayer::Emb_DelStorage(
          v21,
          v11->m_nListCode,
          *((_BYTE *)v10 + 49),
          0,
          0,
          "CPlayer::pc_AnimusInvenChange() -- 1 Fail");
        bDelete = 0;
        CPlayer::Emb_AddStorage(v21, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
        CPlayer::SendMsg_AnimusInvenChange(v21, -1);
        return;
      }
    }
    else
    {
      bDelete = 0;
      if ( !CPlayer::Emb_AddStorage(v21, v12->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0) )
      {
        bDelete = 0;
        CPlayer::Emb_AddStorage(v21, v11->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
        CPlayer::SendMsg_AnimusInvenChange(v21, -1);
        return;
      }
    }
    CPlayer::Emb_EquipLink(v21);
  }
  CPlayer::SendMsg_AnimusInvenChange(v21, v8);
}
