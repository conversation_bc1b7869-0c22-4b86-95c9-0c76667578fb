/*
 * Function: ?Flip@CGuildBattleController@@QEAAXXZ
 * Address: 0x1403D6260
 */

void __fastcall CGuildBattleController::Flip(CGuildBattleController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v3; // rax@4
  GUILD_BATTLE::CNormalGuildBattleManager *v4; // rax@4
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v5; // rax@4
  GUILD_BATTLE::CPossibleBattleGuildListManager *v6; // rax@4
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@5
  __int64 v8; // [sp+0h] [bp-28h]@1
  CGuildBattleController *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v1 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::Flip(v3);
  v4 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::DoDayChangedWork(v4);
  v5 = GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Instance();
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Flip(v5);
  v6 = GUILD_BATTLE::CPossibleBattleGuildListManager::Instance();
  GUILD_BATTLE::CPossibleBattleGuildListManager::DoDayChangedWork(v6);
  if ( !CGuildBattleController::SaveINI(v9) )
  {
    v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(v7, "CGuildBattleController::Flip() SaveINI() Fail!");
  }
}
