/*
 * Function: ?_db_Update_TimeLimitInfo@CMainThread@@AEAAEKPEAU_AVATOR_DATA@@0PEADH@Z
 * Address: 0x1401B7D60
 */

char __fastcall CMainThread::_db_Update_TimeLimitInfo(CMainThread *this, unsigned int dwAccSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szTimeLimitInfoQuery, int nSize)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v8; // eax@8
  size_t v9; // rax@8
  __int64 v11; // [sp+0h] [bp-D8h]@1
  char DstBuf; // [sp+30h] [bp-A8h]@4
  char v13; // [sp+31h] [bp-A7h]@4
  int v14; // [sp+B4h] [bp-24h]@4
  unsigned __int64 v15; // [sp+C0h] [bp-18h]@4
  unsigned int v16; // [sp+E8h] [bp+10h]@1
  _AVATOR_DATA *v17; // [sp+F0h] [bp+18h]@1
  _AVATOR_DATA *v18; // [sp+F8h] [bp+20h]@1

  v18 = pOldData;
  v17 = pNewData;
  v16 = dwAccSerial;
  v6 = &v11;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v15 = (unsigned __int64)&v11 ^ _security_cookie;
  DstBuf = 0;
  memset(&v13, 0, 0x7Fui64);
  sprintf_s(szTimeLimitInfoQuery, nSize, "UPDATE tbl_TimeLimitInfo Set ");
  v14 = strlen_0(szTimeLimitInfoQuery);
  if ( v18->dbTimeLimitInfo.dwFatigue != v17->dbTimeLimitInfo.dwFatigue )
  {
    sprintf_s(&DstBuf, 0x80ui64, "Fatigue=%d,", v17->dbTimeLimitInfo.dwFatigue);
    strcat_s(szTimeLimitInfoQuery, nSize, &DstBuf);
  }
  if ( v18->dbTimeLimitInfo.byTLStatus != v17->dbTimeLimitInfo.byTLStatus )
  {
    sprintf_s(&DstBuf, 0x80ui64, "TLStatus = %u,", v17->dbTimeLimitInfo.byTLStatus);
    strcat_s(szTimeLimitInfoQuery, nSize, &DstBuf);
  }
  v8 = GetKorLocalTime();
  sprintf_s(&DstBuf, 0x80ui64, "LastLogoutTime=%d,", v8);
  strcat_s(szTimeLimitInfoQuery, nSize, &DstBuf);
  v9 = strlen_0(szTimeLimitInfoQuery);
  if ( v9 <= v14 )
  {
    memset_0(szTimeLimitInfoQuery, 0, v14);
  }
  else
  {
    sprintf_s(&DstBuf, 0x80ui64, "WHERE AccountSerial = %d", v16);
    szTimeLimitInfoQuery[strlen_0(szTimeLimitInfoQuery) - 1] = 32;
    strcat_s(szTimeLimitInfoQuery, nSize, &DstBuf);
  }
  return 1;
}
