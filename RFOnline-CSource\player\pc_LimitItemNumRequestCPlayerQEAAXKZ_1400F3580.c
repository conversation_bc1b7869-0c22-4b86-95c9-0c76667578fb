/*
 * Function: ?pc_LimitItemNumRequest@CPlayer@@QEAAXK@Z
 * Address: 0x1400F3580
 */

void __fastcall CPlayer::pc_LimitItemNumRequest(CPlayer *this, unsigned int dwStoreIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v4; // rax@8
  CItemStoreManager *v5; // rax@9
  CItemStoreManager *v6; // rax@11
  __int64 v7; // [sp+0h] [bp-118h]@1
  CItemStoreManager *v8; // [sp+20h] [bp-F8h]@5
  CMapItemStoreList *v9; // [sp+28h] [bp-F0h]@6
  CItemStore *v10; // [sp+30h] [bp-E8h]@13
  _limit_amount_info pAmountInfo; // [sp+50h] [bp-C8h]@14
  CUserDB *v12; // [sp+E8h] [bp-30h]@8
  int n; // [sp+F0h] [bp-28h]@8
  CGuild *v14; // [sp+F8h] [bp-20h]@8
  CGuild *v15; // [sp+100h] [bp-18h]@9
  int nSerial; // [sp+108h] [bp-10h]@11
  CPlayer *v17; // [sp+120h] [bp+8h]@1
  unsigned int dwRecIndex; // [sp+128h] [bp+10h]@1

  dwRecIndex = dwStoreIndex;
  v17 = this;
  v2 = &v7;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v17->m_pCurMap )
  {
    v8 = CItemStoreManager::Instance();
    if ( dwRecIndex < CRecordData::GetRecordNum(&v8->m_tblItemStore) )
    {
      v9 = 0i64;
      if ( v17->m_pCurMap->m_pMapSet->m_nMapType == 1 )
      {
        if ( v17->m_Param.m_pGuild )
        {
          v12 = v17->m_pUserDB;
          n = v17->m_ObjID.m_wIndex;
          v14 = v17->m_Param.m_pGuild;
          v4 = CGuildRoomSystem::GetInstance();
          if ( CGuildRoomSystem::IsGuildRoomMemberIn(v4, v14->m_dwSerial, n, v12->m_dwSerial) )
          {
            v15 = v17->m_Param.m_pGuild;
            v5 = CItemStoreManager::Instance();
            v9 = CItemStoreManager::GetInstanceStoreListBySerial(v5, v15->m_dwSerial);
          }
        }
      }
      else
      {
        nSerial = (unsigned __int8)CMapData::GetMapCode(v17->m_pCurMap);
        v6 = CItemStoreManager::Instance();
        v9 = CItemStoreManager::GetMapItemStoreListBySerial(v6, nSerial);
      }
      if ( v9 )
      {
        v10 = CMapItemStoreList::GetItemStoreFromRecIndex(v9, dwRecIndex);
        if ( v10 )
        {
          _limit_amount_info::_limit_amount_info(&pAmountInfo);
          CItemStore::GetLimitItemAmount(v10, &pAmountInfo);
          CPlayer::SendMsg_StoreLimitItemAmountInfo(v17, v10->m_pRec->m_dwIndex, &pAmountInfo);
        }
      }
    }
  }
}
