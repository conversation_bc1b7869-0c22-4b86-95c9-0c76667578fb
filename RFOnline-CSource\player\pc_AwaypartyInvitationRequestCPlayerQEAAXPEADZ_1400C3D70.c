/*
 * Function: ?pc_AwaypartyInvitationRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x1400C3D70
 */

void __usercall CPlayer::pc_AwaypartyInvitationRequest(CPlayer *this@<rcx>, char *pwszCharName@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@10
  int v6; // eax@29
  __int64 v7; // [sp+0h] [bp-48h]@1
  char v8; // [sp+20h] [bp-28h]@4
  CPlayer *v9; // [sp+28h] [bp-20h]@6
  _STORAGE_LIST::_db_con *v10; // [sp+30h] [bp-18h]@34
  int v11; // [sp+38h] [bp-10h]@10
  float v12; // [sp+3Ch] [bp-Ch]@29
  CPlayer *v13; // [sp+50h] [bp+8h]@1
  char *wpszCharName; // [sp+58h] [bp+10h]@1

  wpszCharName = pwszCharName;
  v13 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = 0;
  if ( !CPlayer::IsPunished(v13, 2, 1) && TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v13->m_id.wIndex) != 99 )
  {
    v9 = (CPlayer *)CMainThread::GetCharW(&g_Main, wpszCharName);
    if ( !v9 )
    {
      v8 = 1;
      CPlayer::SendMsg_AwayPartyRequestResult(v13, 1);
      return;
    }
    if ( v9->m_dwObjSerial == v13->m_dwObjSerial )
    {
      v8 = 1;
      CPlayer::SendMsg_AwayPartyRequestResult(v13, 1);
      return;
    }
    v11 = CPlayerDB::GetRaceCode(&v9->m_Param);
    v5 = CPlayerDB::GetRaceCode(&v13->m_Param);
    if ( v11 != v5 )
    {
      v8 = 2;
      CPlayer::SendMsg_AwayPartyRequestResult(v13, 2);
      return;
    }
    if ( !v9->m_bOper || v9->m_bCorpse )
    {
      v8 = 3;
      CPlayer::SendMsg_AwayPartyRequestResult(v13, 3);
      return;
    }
    if ( !CPlayer::IsPunished(v9, 2, 0) )
    {
      if ( v9->m_byUserDgr )
      {
        if ( !v13->m_byUserDgr )
        {
          v8 = 3;
          CPlayer::SendMsg_AwayPartyRequestResult(v13, 3);
          return;
        }
      }
      else if ( v13->m_byUserDgr )
      {
        v8 = 3;
        CPlayer::SendMsg_AwayPartyRequestResult(v13, 3);
        return;
      }
      if ( CPartyPlayer::IsPartyMode(v9->m_pPartyMgr) )
      {
        v8 = 4;
        CPlayer::SendMsg_AwayPartyRequestResult(v13, 4);
      }
      else if ( !CPartyPlayer::IsPartyMode(v13->m_pPartyMgr) || CPartyPlayer::IsPartyBoss(v13->m_pPartyMgr) )
      {
        if ( v13->m_pPartyMgr->m_bLock )
        {
          v8 = 7;
          CPlayer::SendMsg_AwayPartyRequestResult(v13, 7);
        }
        else
        {
          _effect_parameter::GetEff_Have(&v9->m_EP, 53);
          v12 = a3;
          v6 = ((int (__fastcall *)(CPlayer *))v9->vfptr->GetLevel)(v9);
          if ( CPartyPlayer::IsJoinPartyLevel(v13->m_pPartyMgr, v6, v12) )
          {
            if ( v9->m_bBlockParty )
            {
              v8 = 6;
              CPlayer::SendMsg_AwayPartyRequestResult(v13, 6);
            }
            else if ( !unk_1799C5FFE
                   || (v10 = _STORAGE_LIST::GetPtrFromItemCode(
                               (_STORAGE_LIST *)&v9->m_Param.m_dbInven.m_nListNum,
                               byte_1799C5FFF)) != 0i64 )
            {
              if ( unk_1799C603F && nAmount > CPlayer::GetMoney(v9, 0) )
              {
                v8 = 11;
                CPlayer::SendMsg_AwayPartyRequestResult(v13, 11);
              }
              else
              {
                CPlayer::SendMsg_AwayPartyRequestResult(v13, v8);
                CPlayer::SendMsg_AwayPartyInvitationQuestion(v13, v9->m_ObjID.m_wIndex);
              }
            }
            else
            {
              v8 = 9;
              CPlayer::SendMsg_AwayPartyRequestResult(v13, 9);
            }
          }
          else
          {
            v8 = 5;
            CPlayer::SendMsg_AwayPartyRequestResult(v13, 5);
          }
        }
      }
      else
      {
        v8 = 8;
        CPlayer::SendMsg_AwayPartyRequestResult(v13, 8);
      }
    }
  }
}
