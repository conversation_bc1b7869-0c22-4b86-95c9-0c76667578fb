/*
 * Function: ?Check_Total_Selling@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402FB640
 */

void __fastcall CashItemRemoteStore::Check_Total_Selling(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CCashDBWorkManager *v3; // rax@6
  __int64 v4; // [sp+0h] [bp-48h]@1
  _param_cash_total_selling v5; // [sp+24h] [bp-24h]@6
  unsigned __int64 size; // [sp+38h] [bp-10h]@6
  CashItemRemoteStore *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_con_event.m_ini.m_bUseConEvent == 1 && !v7->m_con_event.m_bConEvent )
  {
    size = _param_cash_total_selling::size(&v5);
    v3 = CTSingleton<CCashDBWorkManager>::Instance();
    CCashDBWorkManager::PushTask(v3, 4, (char *)&v5, size);
  }
}
