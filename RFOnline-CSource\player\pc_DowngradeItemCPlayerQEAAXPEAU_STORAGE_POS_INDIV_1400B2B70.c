/*
 * Function: ?pc_DowngradeItem@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@00@Z
 * Address: 0x1400B2B70
 */

void __fastcall CPlayer::pc_DowngradeItem(CPlayer *this, _STORAGE_POS_INDIV *pposTalik, _STORAGE_POS_INDIV *pposToolItem, _STORAGE_POS_INDIV *pposUpgItem)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // ecx@41
  __int64 v7; // [sp+0h] [bp-198h]@1
  bool bUpdate[8]; // [sp+20h] [bp-178h]@35
  bool bSend[8]; // [sp+28h] [bp-170h]@35
  CMapData *pMap; // [sp+30h] [bp-168h]@40
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-160h]@40
  float *pStdPos; // [sp+40h] [bp-158h]@40
  bool bHide; // [sp+48h] [bp-150h]@40
  char v14; // [sp+50h] [bp-148h]@4
  _STORAGE_LIST *v15; // [sp+58h] [bp-140h]@4
  _STORAGE_LIST::_db_con *pTalik; // [sp+60h] [bp-138h]@4
  _STORAGE_LIST::_db_con *v17; // [sp+68h] [bp-130h]@4
  void *Src; // [sp+70h] [bp-128h]@4
  _ItemUpgrade_fld *v19; // [sp+78h] [bp-120h]@4
  __int64 v20; // [sp+80h] [bp-118h]@4
  _STORAGE_LIST::_db_con Dst; // [sp+98h] [bp-100h]@35
  _STORAGE_LIST::_db_con v22; // [sp+E8h] [bp-B0h]@35
  char v23; // [sp+124h] [bp-74h]@35
  char v24; // [sp+125h] [bp-73h]@35
  unsigned int dwGradeInfo; // [sp+128h] [bp-70h]@35
  int nIndex; // [sp+12Ch] [bp-6Ch]@35
  _STORAGE_LIST::_db_con pItem; // [sp+138h] [bp-60h]@36
  char *pszClause; // [sp+178h] [bp-20h]@39
  char *v29; // [sp+180h] [bp-18h]@40
  CPlayer *pOwner; // [sp+1A0h] [bp+8h]@1
  _STORAGE_POS_INDIV *v31; // [sp+1A8h] [bp+10h]@1
  _STORAGE_POS_INDIV *v32; // [sp+1B0h] [bp+18h]@1
  _STORAGE_POS_INDIV *v33; // [sp+1B8h] [bp+20h]@1

  v33 = pposUpgItem;
  v32 = pposToolItem;
  v31 = pposTalik;
  pOwner = this;
  v4 = &v7;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = 0;
  v15 = pOwner->m_Param.m_pStoragePtr[pposUpgItem->byStorageCode];
  pTalik = 0i64;
  v17 = 0i64;
  Src = 0i64;
  v19 = 0i64;
  v20 = 0i64;
  if ( _effect_parameter::GetEff_State(&pOwner->m_EP, 20) )
  {
    v14 = 7;
  }
  else if ( _effect_parameter::GetEff_State(&pOwner->m_EP, 28) )
  {
    v14 = 7;
  }
  else
  {
    Src = _STORAGE_LIST::GetPtrFromSerial(v15, v33->wItemSerial);
    if ( Src )
    {
      if ( *((_BYTE *)Src + 19) )
      {
        v14 = 13;
      }
      else if ( GetItemKindCode(*((_BYTE *)Src + 1)) )
      {
        v14 = 9;
      }
      else if ( GetDefItemUpgSocketNum(*((_BYTE *)Src + 1), *(_WORD *)((char *)Src + 3)) )
      {
        if ( GetItemUpgedLv(*(_DWORD *)((char *)Src + 13)) )
        {
          pTalik = _STORAGE_LIST::GetPtrFromSerial(
                     (_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum,
                     v31->wItemSerial);
          if ( pTalik )
          {
            if ( pTalik->m_byTableCode == 18 )
            {
              if ( pTalik->m_bLock )
              {
                v14 = 13;
              }
              else
              {
                v19 = CItemUpgradeTable::GetRecordFromRes(&stru_1799C69D8, pTalik->m_wItemIndex);
                if ( v19 )
                {
                  if ( v19->m_nDataEffect == 14 )
                  {
                    v17 = _STORAGE_LIST::GetPtrFromSerial(
                            (_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum,
                            v32->wItemSerial);
                    if ( v17 )
                    {
                      if ( v17->m_byTableCode == 11 )
                      {
                        if ( v17->m_bLock )
                          v14 = 13;
                      }
                      else
                      {
                        v14 = 4;
                      }
                    }
                    else
                    {
                      CPlayer::SendMsg_AdjustAmountInform(pOwner, 0, v32->wItemSerial, 0);
                      v14 = 3;
                    }
                  }
                  else
                  {
                    v14 = 2;
                  }
                }
                else
                {
                  v14 = 2;
                }
              }
            }
            else
            {
              v14 = 2;
            }
          }
          else
          {
            CPlayer::SendMsg_AdjustAmountInform(pOwner, 0, v31->wItemSerial, 0);
            v14 = 1;
          }
        }
        else
        {
          v14 = 12;
        }
      }
      else
      {
        v14 = 9;
      }
    }
    else
    {
      v14 = 5;
    }
  }
  if ( !v14 )
  {
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    _STORAGE_LIST::_db_con::_db_con(&v22);
    memcpy_0(&Dst, Src, 0x32ui64);
    memcpy_0(&v22, pTalik, 0x32ui64);
    bSend[0] = 0;
    bUpdate[0] = 0;
    CPlayer::Emb_AlterDurPoint(pOwner, 0, pTalik->m_byStorageIndex, -1, 0, 0);
    v23 = GetItemUpgedLv(*(_DWORD *)((char *)Src + 13));
    v24 = GetTalikFromSocket(*(_DWORD *)((char *)Src + 13), v23 - 1);
    dwGradeInfo = GetBitAfterDowngrade(*(_DWORD *)((char *)Src + 13), v23);
    *(_DWORD *)bUpdate = dwGradeInfo;
    CPlayer::Emb_ItemUpgrade(pOwner, 1, v15->m_nListCode, *((_BYTE *)Src + 49), dwGradeInfo);
    nIndex = FixTalikItemIndex(v24);
    if ( nIndex > 0 )
    {
      _STORAGE_LIST::_db_con::_db_con(&pItem);
      pItem.m_byTableCode = 18;
      pItem.m_wItemIndex = nIndex;
      pItem.m_dwDur = GetItemDurPoint(18, nIndex);
      pItem.m_dwLv = 0xFFFFFFF;
      if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum) == 255 )
      {
        bHide = 0;
        pStdPos = pOwner->m_fCurPos;
        wLayerIndex = pOwner->m_wMapLayerIndex;
        pMap = pOwner->m_pCurMap;
        CreateItemBox(&pItem, pOwner, 0xFFFFFFFF, 0, 0i64, 3, pMap, wLayerIndex, pOwner->m_fCurPos, 0);
        v29 = "GradeDown Ground Reward";
        CMgrAvatorItemHistory::reward_add_item(
          &CPlayer::s_MgrItemHistory,
          pOwner->m_ObjID.m_wIndex,
          "GradeDown Ground Reward",
          &pItem,
          pOwner->m_szItemHistoryFileName);
      }
      else
      {
        pItem.m_wSerial = CPlayerDB::GetNewItemSerial(&pOwner->m_Param);
        if ( !CPlayer::Emb_AddStorage(pOwner, 0, (_STORAGE_LIST::_storage_con *)&pItem.m_bLoad, 0, 1) )
        {
          CPlayer::SendMsg_ItemDowngrade(pOwner, -1);
          CMgrAvatorItemHistory::add_storage_fail(
            &CPlayer::s_MgrItemHistory,
            pOwner->m_ObjID.m_wIndex,
            &pItem,
            "CPlayer::pc_DowngradeItem - Emb_AddStorage() Fail",
            pOwner->m_szItemHistoryFileName);
          return;
        }
        CPlayer::SendMsg_RewardAddItem(pOwner, &pItem, 0);
        pszClause = "GradeDown Reward";
        CMgrAvatorItemHistory::reward_add_item(
          &CPlayer::s_MgrItemHistory,
          pOwner->m_ObjID.m_wIndex,
          "GradeDown Reward",
          &pItem,
          pOwner->m_szItemHistoryFileName);
      }
    }
    v6 = pOwner->m_ObjID.m_wIndex;
    *(_QWORD *)bSend = (char *)pOwner + 50608;
    *(_DWORD *)bUpdate = *(_DWORD *)((char *)Src + 13);
    CMgrAvatorItemHistory::grade_down_item(
      &CPlayer::s_MgrItemHistory,
      v6,
      &Dst,
      pTalik,
      *(unsigned int *)bUpdate,
      pOwner->m_szItemHistoryFileName);
  }
  CPlayer::SendMsg_ItemDowngrade(pOwner, v14);
}
