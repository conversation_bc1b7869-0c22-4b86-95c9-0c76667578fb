/*
 * Function: ?_db_Load_Inven@CMainThread@@AEAAEKHPEAU_INVEN_DB_BASE@@@Z
 * Address: 0x1401A75B0
 */

char __fastcall CMainThread::_db_Load_Inven(CMainThread *this, unsigned int dwSerial, int nBagNum, _INVEN_DB_BASE *pCon)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-CC8h]@1
  int Dst[2]; // [sp+30h] [bp-C98h]@4
  __int64 v9; // [sp+38h] [bp-C90h]@11
  int v10; // [sp+40h] [bp-C88h]@11
  int v11; // [sp+44h] [bp-C84h]@11
  __int64 v12[397]; // [sp+48h] [bp-C80h]@11
  char v13; // [sp+CB4h] [bp-14h]@4
  int j; // [sp+CB8h] [bp-10h]@8
  CMainThread *v15; // [sp+CD0h] [bp+8h]@1
  unsigned int dwCharacterSerial; // [sp+CD8h] [bp+10h]@1
  int v17; // [sp+CE0h] [bp+18h]@1
  _INVEN_DB_BASE *v18; // [sp+CE8h] [bp+20h]@1

  v18 = pCon;
  v17 = nBagNum;
  dwCharacterSerial = dwSerial;
  v15 = this;
  v4 = &v7;
  for ( i = 816i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset_0(Dst, 0, 0xC80ui64);
  v13 = CRFWorldDatabase::Select_Inven(v15->m_pWorldDB, dwCharacterSerial, v17, (_worlddb_inven_info *)Dst);
  if ( v13 == 1 )
  {
    result = 24;
  }
  else if ( v13 == 2 )
  {
    result = 37;
  }
  else
  {
    for ( j = 0; j < 100; ++j )
    {
      if ( 20 * v17 <= j )
      {
        _INVENKEY::SetRelease((_INVENKEY *)((char *)v18 + 37 * j));
      }
      else
      {
        _INVENKEY::LoadDBKey((_INVENKEY *)((char *)v18 + 37 * j), Dst[8 * j]);
        v18->m_List[j].dwDur = *(&v9 + 4 * j);
        v18->m_List[j].dwUpt = *(&v10 + 8 * j);
        v18->m_List[j].dwT = *(&v11 + 8 * j);
        v18->m_List[j].lnUID = v12[4 * j];
      }
    }
    result = 0;
  }
  return result;
}
