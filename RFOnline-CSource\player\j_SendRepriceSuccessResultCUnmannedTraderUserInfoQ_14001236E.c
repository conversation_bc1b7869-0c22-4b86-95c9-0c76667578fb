/*
 * Function: j_?SendRepriceSuccessResult@CUnmannedTraderUserInfo@@QEAAXPEAVCPlayer@@GKKK@Z
 * Address: 0x14001236E
 */

void __fastcall CUnmannedTraderUserInfo::SendRepriceSuccessResult(CUnmannedTraderUserInfo *this, CPlayer *pReceiver, unsigned __int16 wItemSerial, unsigned int dwNewPrice, unsigned int dwRegistSerial, unsigned int dwTax)
{
  CUnmannedTraderUserInfo::SendRepriceSuccessResult(this, pReceiver, wItemSerial, dwNewPrice, dwRegistSerial, dwTax);
}
