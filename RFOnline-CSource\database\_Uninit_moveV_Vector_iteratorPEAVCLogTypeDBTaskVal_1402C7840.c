/*
 * Function: ??$_Uninit_move@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@PEAPEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@2@U_Undefined_move_tag@2@@std@@YAPEAPEAVCLogTypeDBTask@@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@0@0PEAPEAV1@AEAV?$allocator@PEAVCLogTypeDBTask@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1402C7840
 */

CLogTypeDBTask **__fastcall std::_Uninit_move<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>,std::_Undefined_move_tag>(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_First, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Last, CLogTypeDBTask **_Dest, std::allocator<CLogTypeDBTask *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v8; // rax@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v9; // rax@4
  __int64 v11; // [sp+0h] [bp-98h]@1
  CLogTypeDBTask **v12; // [sp+20h] [bp-78h]@4
  char v13; // [sp+28h] [bp-70h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v14; // [sp+40h] [bp-58h]@4
  char v15; // [sp+48h] [bp-50h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v16; // [sp+60h] [bp-38h]@4
  __int64 v17; // [sp+68h] [bp-30h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v18; // [sp+70h] [bp-28h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v19; // [sp+78h] [bp-20h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v20; // [sp+80h] [bp-18h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v21; // [sp+A0h] [bp+8h]@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__that; // [sp+A8h] [bp+10h]@1
  CLogTypeDBTask **v23; // [sp+B0h] [bp+18h]@1
  std::allocator<CLogTypeDBTask *> *v24; // [sp+B8h] [bp+20h]@1

  v24 = _Al;
  v23 = _Dest;
  __that = _Last;
  v21 = _First;
  v6 = &v11;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v17 = -2i64;
  v14 = (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v13;
  v16 = (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v15;
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    (std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v13,
    _Last);
  v18 = v8;
  v19 = v8;
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    v16,
    v21);
  v20 = v9;
  v12 = stdext::unchecked_uninitialized_copy<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(
          v9,
          v19,
          v23,
          v24);
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(v21);
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(__that);
  return v12;
}
