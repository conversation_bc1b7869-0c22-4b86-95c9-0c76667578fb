/*
 * Function: ?CreateLogFile@CGuildBattleLogger@GUILD_BATTLE@@QEAAXPEAD@Z
 * Address: 0x1403CE9E0
 */

void __fastcall GUILD_BATTLE::CGuildBattleLogger::CreateLogFile(GUILD_BATTLE::CGuildBattleLogger *this, char *szLogName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-F8h]@1
  bool bDate; // [sp+20h] [bp-D8h]@7
  bool bAddCount; // [sp+28h] [bp-D0h]@7
  unsigned int v7; // [sp+30h] [bp-C8h]@4
  char _Dest[128]; // [sp+50h] [bp-A8h]@5
  unsigned __int64 v9; // [sp+E0h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleLogger *v10; // [sp+100h] [bp+8h]@1
  char *v11; // [sp+108h] [bp+10h]@1

  v11 = szLogName;
  v10 = this;
  v2 = &v4;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v4 ^ _security_cookie;
  v7 = GetKorLocalTime();
  if ( v11 )
    sprintf_s<128>((char (*)[128])_Dest, "..\\ZoneServerLog\\Systemlog\\GuildBattle\\GuildBattle%s%d.log", v11, v7);
  else
    sprintf_s<128>((char (*)[128])_Dest, "..\\ZoneServerLog\\Systemlog\\GuildBattle\\GuildBattle%d.log", v7);
  bAddCount = 1;
  bDate = 1;
  CLogFile::SetWriteLogFile(v10->m_pkLogger, _Dest, 1, 0, 1, 1);
}
