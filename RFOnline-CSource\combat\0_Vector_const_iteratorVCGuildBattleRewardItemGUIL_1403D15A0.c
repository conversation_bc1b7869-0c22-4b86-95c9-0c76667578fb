/*
 * Function: ??0?$_Vector_const_iterator@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x1403D15A0
 */

void __fastcall std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this, std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v5; // [sp+30h] [bp+8h]@1
  std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *__thata; // [sp+38h] [bp+10h]@1

  __thata = __that;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &>::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &>(
    (std::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &> *)&v5->_Mycont,
    (std::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &> *)&__that->_Mycont);
  v5->_Myptr = __thata->_Myptr;
}
