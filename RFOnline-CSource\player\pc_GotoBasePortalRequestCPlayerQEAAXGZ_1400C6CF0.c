/*
 * Function: ?pc_GotoBasePortalRequest@CPlayer@@QEAAXG@Z
 * Address: 0x1400C6CF0
 */

void __usercall CPlayer::pc_GotoBasePortalRequest(CPlayer *this@<rcx>, unsigned __int16 wItemSerial@<dx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@44
  int v6; // eax@47
  char v7; // al@66
  __int64 v8; // [sp+0h] [bp-A8h]@1
  float *pfStartPos; // [sp+20h] [bp-88h]@70
  bool bSend; // [sp+28h] [bp-80h]@70
  char v11; // [sp+30h] [bp-78h]@4
  CMapData *pIntoMap; // [sp+38h] [bp-70h]@4
  __int64 v13; // [sp+40h] [bp-68h]@4
  _STORAGE_LIST::_db_con *v14; // [sp+48h] [bp-60h]@4
  _base_fld *v15; // [sp+50h] [bp-58h]@4
  float pNewPos; // [sp+68h] [bp-40h]@53
  int v17; // [sp+84h] [bp-24h]@4
  _dummy_position *pPos; // [sp+88h] [bp-20h]@51
  int nCashType; // [sp+90h] [bp-18h]@63
  CPlayer *v20; // [sp+B0h] [bp+8h]@1
  unsigned __int16 v21; // [sp+B8h] [bp+10h]@1

  v21 = wItemSerial;
  v20 = this;
  v3 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 0;
  pIntoMap = 0i64;
  v13 = 0i64;
  v14 = 0i64;
  v15 = 0i64;
  v17 = 0;
  if ( v20->m_bInGuildBattle )
  {
    v11 = 12;
  }
  else if ( CGameObject::GetCurSecNum((CGameObject *)&v20->vfptr) == -1 || v20->m_bMapLoading )
  {
    v11 = 5;
  }
  else if ( v20->m_bCorpse )
  {
    v11 = 1;
  }
  else if ( v20->m_pCurMap->m_pMapSet->m_nMapType )
  {
    v11 = 8;
  }
  else if ( v20->m_byStandType == 1 )
  {
    v11 = 8;
  }
  else if ( CPlayer::IsSiegeMode(v20) )
  {
    v11 = 8;
  }
  else if ( CPlayer::IsRidingUnit(v20) )
  {
    v11 = 8;
  }
  else if ( _effect_parameter::GetEff_State(&v20->m_EP, 20) )
  {
    v11 = 9;
  }
  else if ( _effect_parameter::GetEff_State(&v20->m_EP, 28) )
  {
    v11 = 9;
  }
  else if ( v21 == 0xFFFF )
  {
    v7 = CPlayerDB::GetRaceCode(&v20->m_Param);
    pIntoMap = CMapOperation::GetPosStartMap(&g_MapOper, v7, 0, &pNewPos);
    if ( !pIntoMap )
      v11 = 11;
  }
  else
  {
    v14 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v20->m_Param.m_dbInven.m_nListNum, v21);
    if ( v14 )
    {
      if ( v14->m_byTableCode == 22 )
      {
        if ( CHolyStoneSystem::IsUseReturnItem(&g_HolySys, v20->m_dwObjSerial) )
        {
          if ( v14->m_bLock )
          {
            v11 = 22;
          }
          else
          {
            v15 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 22, v14->m_wItemIndex);
            if ( v15 )
            {
              if ( v20->m_pCurMap->m_pMapSet->m_nMapType == 2 )
              {
                v11 = 11;
              }
              else if ( !v20->m_pCurMap->m_pMapSet->m_nMapClass
                     || *(_DWORD *)&v15[7].m_strCode[44]
                     || !strcmp_0(v20->m_pCurMap->m_pMapSet->m_strCode, "Elan")
                     && (strcmp_0(v20->m_pCurMap->m_pMapSet->m_strCode, "Platform01")
                      && strcmp_0(v20->m_pCurMap->m_pMapSet->m_strCode, "Medicallab")
                      || (_effect_parameter::GetEff_Have(&v20->m_EP, 55), a3 != 0.0))
                     || !strcmp_0(v15[4].m_strCode, v20->m_pCurMap->m_pMapSet->m_strCode) )
              {
                v5 = ((int (__fastcall *)(CPlayer *))v20->vfptr->GetLevel)(v20);
                if ( v5 >= *(_DWORD *)&v15[6].m_strCode[20] )
                {
                  if ( *(_DWORD *)&v15[6].m_strCode[24] == -1
                    || (v6 = ((int (__fastcall *)(CPlayer *))v20->vfptr->GetLevel)(v20),
                        v6 <= *(_DWORD *)&v15[6].m_strCode[24]) )
                  {
                    pIntoMap = CMapOperation::GetMap(&g_MapOper, v15[4].m_strCode);
                    if ( pIntoMap )
                    {
                      pPos = CMapData::GetDummyPostion(pIntoMap, (char *)&v15[5]);
                      if ( pPos )
                      {
                        if ( CMapData::GetRandPosInDummy(pIntoMap, pPos, &pNewPos, 1) )
                        {
                          if ( Major_Scroll_Item && v15 )
                          {
                            if ( !strcmp_0(v15[4].m_strCode, "Elan")
                              || !strcmp_0(v15[4].m_strCode, "NeutralA")
                              || !strcmp_0(v15[4].m_strCode, "NeutralB")
                              || !strcmp_0(v15[4].m_strCode, "NeutralC") )
                            {
                              v11 = 0;
                            }
                            else
                            {
                              v11 = 22;
                            }
                          }
                          else
                          {
                            nCashType = GetUsePcCashType(v14->m_byTableCode, v14->m_wItemIndex);
                            if ( !CPlayer::IsUsableAccountType(v20, nCashType) )
                            {
                              CPlayer::SendMsg_PremiumCashItemUse(v20, 0xFFFFu);
                              v11 = 22;
                            }
                          }
                        }
                        else
                        {
                          v11 = 7;
                        }
                      }
                      else
                      {
                        v11 = 2;
                      }
                    }
                    else
                    {
                      v11 = 11;
                    }
                  }
                  else
                  {
                    v11 = 15;
                  }
                }
                else
                {
                  v11 = 15;
                }
              }
              else
              {
                v11 = 11;
              }
            }
            else
            {
              v11 = 10;
            }
          }
        }
        else
        {
          v11 = 13;
        }
      }
      else
      {
        v11 = 10;
      }
    }
    else
    {
      CPlayer::SendMsg_AdjustAmountInform(v20, 0, v21, 0);
      v11 = 10;
    }
  }
  if ( !v11 )
  {
    CPlayer::OutOfMap(v20, pIntoMap, 0, 3, &pNewPos);
    if ( v21 != 0xFFFF )
    {
      bSend = 0;
      LOBYTE(pfStartPos) = 0;
      CPlayer::Emb_AlterDurPoint(v20, 0, v14->m_byStorageIndex, -1, 0, 0);
    }
    CPlayer::Emb_CheckActForQuest(v20, 16, v15->m_strCode, 1u, 0);
  }
  CPlayer::SendMsg_GotoBasePortalResult(v20, v11);
}
