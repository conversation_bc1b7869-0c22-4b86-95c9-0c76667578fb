/*
 * Function: ?Clear@CGuildBattleRankManager@GUILD_BATTLE@@IEAAXE@Z
 * Address: 0x1403CB530
 */

void __fastcall GUILD_BATTLE::CGuildBattleRankManager::Clear(GUILD_BATTLE::CGuildBattleRankManager *this, char byRace)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@7
  GUILD_BATTLE::CGuildBattleRankManager *v6; // [sp+40h] [bp+8h]@1
  char v7; // [sp+48h] [bp+10h]@1

  v7 = byRace;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    memset_0(v6->m_dwGuildSerial[(unsigned __int8)byRace], 0, 0x4B0ui64);
    if ( v6->m_ppkList )
    {
      if ( v6->m_ppkList[(unsigned __int8)v7] )
      {
        for ( j = 0; j < 10; ++j )
        {
          v6->m_ppkList[(unsigned __int8)v7][j].dwCurVer = v6->m_dwVer[(unsigned __int8)v7];
          v6->m_ppkList[(unsigned __int8)v7][j].byRace = v7;
          v6->m_ppkList[(unsigned __int8)v7][j].byCurPage = j;
          v6->m_ppkList[(unsigned __int8)v7][j].byMaxPage = 0;
          v6->m_ppkList[(unsigned __int8)v7][j].byExistSelfGuildInfo = -1;
          v6->m_ppkList[(unsigned __int8)v7][j].byCnt = 0;
        }
      }
    }
  }
}
