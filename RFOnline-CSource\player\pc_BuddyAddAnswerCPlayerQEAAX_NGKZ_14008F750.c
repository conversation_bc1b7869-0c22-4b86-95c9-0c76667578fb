/*
 * Function: ?pc_BuddyAddAnswer@CPlayer@@QEAAX_NGK@Z
 * Address: 0x14008F750
 */

void __fastcall CPlayer::pc_BuddyAddAnswer(CPlayer *this, bool bAccept, unsigned __int16 wAskerIndex, unsigned int dwAskerSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@14
  char *v7; // rax@24
  char *v8; // rax@25
  char *v9; // rax@26
  char *v10; // rax@27
  __int64 v11; // [sp+0h] [bp-68h]@1
  unsigned __int16 wIndex; // [sp+20h] [bp-48h]@28
  unsigned int v13; // [sp+28h] [bp-40h]@28
  char *pwszCharName; // [sp+30h] [bp-38h]@28
  char v15; // [sp+40h] [bp-28h]@4
  CPlayer *v16; // [sp+48h] [bp-20h]@4
  CPlayer *pPtr; // [sp+50h] [bp-18h]@4
  int v18; // [sp+58h] [bp-10h]@24
  int v19; // [sp+5Ch] [bp-Ch]@14
  CPlayer *v20; // [sp+70h] [bp+8h]@1
  bool v21; // [sp+78h] [bp+10h]@1
  unsigned __int16 v22; // [sp+80h] [bp+18h]@1
  unsigned int dwSerial; // [sp+88h] [bp+20h]@1

  dwSerial = dwAskerSerial;
  v22 = wAskerIndex;
  v21 = bAccept;
  v20 = this;
  v4 = &v11;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = 0;
  v16 = 0i64;
  pPtr = 0i64;
  if ( !bAccept || _BUDDY_LIST::GetEmptyData(&v20->m_pmBuddy) || _BUDDY_LIST::IsBuddy(&v20->m_pmBuddy, dwSerial) )
  {
    v16 = &g_Player + v22;
    if ( v16->m_bLive )
    {
      if ( v16->m_dwObjSerial == dwSerial )
      {
        if ( _BUDDY_LIST::IsPushLastApply(&v16->m_pmBuddy, v20->m_dwObjSerial) )
        {
          pPtr = v16;
          v19 = CPlayerDB::GetRaceCode(&v16->m_Param);
          v6 = CPlayerDB::GetRaceCode(&v20->m_Param);
          if ( v19 == v6 )
          {
            if ( pPtr == v20 )
            {
              v15 = 3;
            }
            else if ( v21
                   && !_BUDDY_LIST::GetEmptyData(&pPtr->m_pmBuddy)
                   && !_BUDDY_LIST::IsBuddy(&pPtr->m_pmBuddy, v20->m_dwObjSerial) )
            {
              v15 = 4;
            }
          }
          else
          {
            v15 = 3;
          }
        }
        else
        {
          v15 = 5;
        }
      }
      else
      {
        v15 = 3;
      }
    }
    else
    {
      v15 = 3;
    }
  }
  else
  {
    v15 = 1;
  }
  if ( v15 )
  {
    CPlayer::SendMsg_BuddyAddAnswerResult(v20, v15, v21, dwSerial, 0xFFFFu, 0xFFFFFFFF, "fail");
    if ( pPtr )
      CPlayer::SendMsg_BuddyAddAnswerResult(pPtr, v15, v21, dwSerial, 0xFFFFu, 0xFFFFFFFF, "fail");
  }
  else
  {
    if ( v21 )
    {
      v7 = CPlayerDB::GetCharNameW(&pPtr->m_Param);
      v18 = _BUDDY_LIST::PushBuddy(&v20->m_pmBuddy, pPtr->m_dwObjSerial, v7, pPtr);
      if ( v18 != -1 )
      {
        v8 = CPlayerDB::GetCharNameW(&pPtr->m_Param);
        CUserDB::Update_AddBuddy(v20->m_pUserDB, v18, pPtr->m_dwObjSerial, v8);
      }
      v9 = CPlayerDB::GetCharNameW(&v20->m_Param);
      v18 = _BUDDY_LIST::PushBuddy(&pPtr->m_pmBuddy, v20->m_dwObjSerial, v9, v20);
      if ( v18 != -1 )
      {
        v10 = CPlayerDB::GetCharNameW(&v20->m_Param);
        CUserDB::Update_AddBuddy(pPtr->m_pUserDB, v18, v20->m_dwObjSerial, v10);
      }
    }
    pwszCharName = CPlayerDB::GetCharNameW(&pPtr->m_Param);
    v13 = pPtr->m_dwObjSerial;
    wIndex = pPtr->m_ObjID.m_wIndex;
    CPlayer::SendMsg_BuddyAddAnswerResult(v20, v15, v21, dwSerial, wIndex, v13, pwszCharName);
    CPlayer::SendMsg_BuddyPosInform(v20, pPtr->m_dwObjSerial, pPtr->m_wRegionMapIndex, pPtr->m_wRegionIndex);
    pwszCharName = CPlayerDB::GetCharNameW(&v20->m_Param);
    v13 = v20->m_dwObjSerial;
    wIndex = v20->m_ObjID.m_wIndex;
    CPlayer::SendMsg_BuddyAddAnswerResult(pPtr, v15, v21, dwSerial, wIndex, v13, pwszCharName);
    CPlayer::SendMsg_BuddyPosInform(pPtr, v20->m_dwObjSerial, v20->m_wRegionMapIndex, v20->m_wRegionIndex);
    _BUDDY_LIST::PopLastApplyTemp(&pPtr->m_pmBuddy, v20->m_dwObjSerial);
  }
}
