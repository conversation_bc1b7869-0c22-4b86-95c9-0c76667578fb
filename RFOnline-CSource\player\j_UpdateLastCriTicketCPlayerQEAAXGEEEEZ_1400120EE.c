/*
 * Function: j_?UpdateLastCriTicket@CPlayer@@QEAAXGEEEE@Z
 * Address: 0x1400120EE
 */

void __fastcall CPlayer::UpdateLastCriTicket(CPlayer *this, unsigned __int16 byCurrentYear, char byCurrent<PERSON><PERSON><PERSON>, char byCurrentDay, char byCurrent<PERSON><PERSON>, char byNumOfTime)
{
  CPlayer::UpdateLastCriTicket(this, byCurrentYear, byCurrentMonth, byCurrentDay, byCurrentHour, byNumOfTime);
}
