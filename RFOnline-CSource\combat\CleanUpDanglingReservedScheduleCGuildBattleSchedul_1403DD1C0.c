/*
 * Function: ?CleanUpDanglingReservedSchedule@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403DD1C0
 */

bool __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::CleanUpDanglingReservedSchedule(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::CleanUpDanglingReservedSchedule(v5->m_pkTodaySchedule) )
    result = GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::CleanUpDanglingReservedSchedule(v5->m_pkTomorrowSchedule) != 0;
  else
    result = 0;
  return result;
}
