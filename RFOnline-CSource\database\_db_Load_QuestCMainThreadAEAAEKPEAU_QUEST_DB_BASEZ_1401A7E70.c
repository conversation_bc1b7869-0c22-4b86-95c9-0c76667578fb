/*
 * Function: ?_db_<PERSON><PERSON>_Quest@CMainThread@@AEAAEKPEAU_QUEST_DB_BASE@@@Z
 * Address: 0x1401A7E70
 */

char __fastcall CMainThread::_db_Load_Quest(CMainThread *this, unsigned int dwSerial, _QUEST_DB_BASE *pCon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-238h]@1
  char Dst[2]; // [sp+30h] [bp-208h]@4
  __int16 v8; // [sp+32h] [bp-206h]@13
  __int16 v9; // [sp+34h] [bp-204h]@13
  __int16 v10; // [sp+36h] [bp-202h]@13
  __int16 v11[2]; // [sp+38h] [bp-200h]@13
  int v12[118]; // [sp+3Ch] [bp-1FCh]@13
  char v13; // [sp+214h] [bp-24h]@4
  int j; // [sp+218h] [bp-20h]@11
  unsigned __int64 v15; // [sp+228h] [bp-10h]@4
  CMainThread *v16; // [sp+240h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+248h] [bp+10h]@1
  _QUEST_DB_BASE *v18; // [sp+250h] [bp+18h]@1

  v18 = pCon;
  dwSeriala = dwSerial;
  v16 = this;
  v3 = &v6;
  for ( i = 140i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v6 ^ _security_cookie;
  memset_0(Dst, 0, 0x1E0ui64);
  v13 = CRFWorldDatabase::Select_Quest(v16->m_pWorldDB, dwSeriala, (_worlddb_quest_array *)Dst);
  if ( v13 == 1 )
    return 24;
  if ( v13 != 2 )
    goto LABEL_17;
  if ( !CRFWorldDatabase::Insert_Quest(v16->m_pWorldDB, dwSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_Quest(v16->m_pWorldDB, dwSeriala, (_worlddb_quest_array *)Dst) )
  {
    result = 24;
  }
  else
  {
LABEL_17:
    for ( j = 0; j < 30; ++j )
    {
      v18->m_List[j].byQuestType = Dst[16 * j];
      v18->m_List[j].wIndex = *(&v8 + 8 * j);
      v18->m_List[j].dwPassSec = v12[4 * j];
      v18->m_List[j].wNum[0] = *(&v9 + 8 * j);
      v18->m_List[j].wNum[1] = *(&v10 + 8 * j);
      v18->m_List[j].wNum[2] = v11[8 * j];
    }
    result = 0;
  }
  return result;
}
