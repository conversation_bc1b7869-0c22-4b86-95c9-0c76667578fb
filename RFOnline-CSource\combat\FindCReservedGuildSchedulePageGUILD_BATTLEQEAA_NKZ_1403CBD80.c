/*
 * Function: ?Find@CReservedGuildSchedulePage@GUILD_BATTLE@@QEAA_NK@Z
 * Address: 0x1403CBD80
 */

char __fastcall GUILD_BATTLE::CReservedGuildSchedulePage::Find(GUILD_BATTLE::CReservedGuildSchedulePage *this, unsigned int dwGuildSerial)
{
  char *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int8 j; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CReservedGuildSchedulePage *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (char *)&j;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 += 4;
  }
  for ( j = 0; (signed int)j < 4; ++j )
  {
    if ( v6->m_dw1PGuildSerial[j] == dwGuildSerial || v6->m_dw2PGuildSerial[j] == dwGuildSerial )
    {
      v6->m_pkList->bySelfScheduleInx = j;
      return 1;
    }
  }
  v6->m_pkList->bySelfScheduleInx = -1;
  return 0;
}
