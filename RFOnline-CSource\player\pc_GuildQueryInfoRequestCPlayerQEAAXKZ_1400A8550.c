/*
 * Function: ?pc_GuildQueryInfoRequest@CPlayer@@QEAAXK@Z
 * Address: 0x1400A8550
 */

void __fastcall CPlayer::pc_GuildQueryInfoRequest(CPlayer *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPlayer *v6; // [sp+40h] [bp+8h]@1
  unsigned int v7; // [sp+48h] [bp+10h]@1

  v7 = dwGuildSerial;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 500; ++j )
  {
    if ( CGuild::IsFill(&g_Guild[j]) && g_Guild[j].m_dwSerial == v7 )
    {
      CGuild::SendMsg_QueryPacket_Info(&g_Guild[j], v6->m_ObjID.m_wIndex);
      return;
    }
  }
}
