/*
 * Function: ?Add@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAEIKKPEAPEAVCGuildBattleSchedule@2@AEAI@Z
 * Address: 0x1403DC440
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Add(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, GUILD_BATTLE::CGuildBattleSchedule **ppkSchedule, unsigned int *uiSLID)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v9; // [sp+0h] [bp-38h]@1
  char v10; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v11; // [sp+40h] [bp+8h]@1
  unsigned int uiFieldInxa; // [sp+48h] [bp+10h]@1
  unsigned int dwStartTimeInxa; // [sp+50h] [bp+18h]@1
  unsigned int dwElapseTimeCnta; // [sp+58h] [bp+20h]@1

  dwElapseTimeCnta = dwElapseTimeCnt;
  dwStartTimeInxa = dwStartTimeInx;
  uiFieldInxa = uiFieldInx;
  v11 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::IsEmptyTime(
          v11,
          uiFieldInx,
          dwStartTimeInx,
          dwElapseTimeCnt);
  if ( v10 )
  {
    result = v10;
  }
  else
  {
    *uiSLID = GUILD_BATTLE::CGuildBattleReservedSchedule::GetID(v11->m_ppkReservedSchedule[uiFieldInxa]);
    result = GUILD_BATTLE::CGuildBattleReservedSchedule::Add(
               v11->m_ppkReservedSchedule[uiFieldInxa],
               dwStartTimeInxa,
               dwElapseTimeCnta,
               ppkSchedule);
  }
  return result;
}
