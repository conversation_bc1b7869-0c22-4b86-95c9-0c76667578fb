/*
 * Function: luaK_exp2anyreg
 * Address: 0x140548280
 */

__int64 __fastcall luaK_exp2anyreg(__int64 a1, __int64 a2)
{
  __int64 v2; // rbx@1
  __int64 v3; // rdi@1
  int v4; // er8@3

  v2 = a2;
  v3 = a1;
  luaK_dischargevars(a1, a2);
  if ( *(_DWORD *)v2 != 12 )
  {
LABEL_5:
    luaK_exp2nextreg(v3, v2);
    return *(_DWORD *)(v2 + 8);
  }
  if ( *(_DWORD *)(v2 + 16) != *(_DWORD *)(v2 + 20) )
  {
    v4 = *(_DWORD *)(v2 + 8);
    if ( v4 >= *(_BYTE *)(v3 + 74) )
    {
      sub_140548110(v3, v2, v4);
      return *(_DWORD *)(v2 + 8);
    }
    goto LABEL_5;
  }
  return *(_DWORD *)(v2 + 8);
}
