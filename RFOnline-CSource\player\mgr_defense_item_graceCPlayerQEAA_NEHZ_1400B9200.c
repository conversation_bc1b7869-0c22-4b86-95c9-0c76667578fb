/*
 * Function: ?mgr_defense_item_grace@CPlayer@@QEAA_NEH@Z
 * Address: 0x1400B9200
 */

char __fastcall CPlayer::mgr_defense_item_grace(CPlayer *this, char byItemCode, int nLv)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@9
  __int64 v7; // [sp+0h] [bp-58h]@1
  int j; // [sp+30h] [bp-28h]@7
  _STORAGE_LIST::_db_con *pItem; // [sp+38h] [bp-20h]@7
  unsigned __int8 v10; // [sp+40h] [bp-18h]@22
  unsigned int dwCurBit; // [sp+44h] [bp-14h]@23
  int k; // [sp+48h] [bp-10h]@23
  CPlayer *v13; // [sp+60h] [bp+8h]@1
  char v14; // [sp+68h] [bp+10h]@1
  int v15; // [sp+70h] [bp+18h]@1

  v15 = nLv;
  v14 = byItemCode;
  v13 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( nLv > 7 || nLv < 0 )
    return 0;
  pItem = 0i64;
  for ( j = 0; ; ++j )
  {
    v6 = CPlayerDB::GetBagNum(&v13->m_Param);
    if ( j >= 20 * (unsigned __int8)v6 )
      break;
    pItem = &v13->m_Param.m_dbInven.m_pStorageList[j];
    if ( pItem && pItem->m_bLoad )
    {
      if ( v14 == 37 )
      {
        if ( pItem->m_byTableCode
          && pItem->m_byTableCode != 1
          && pItem->m_byTableCode != 2
          && pItem->m_byTableCode != 3
          && pItem->m_byTableCode != 4
          && pItem->m_byTableCode != 5
          && pItem->m_byTableCode != 7 )
        {
          continue;
        }
      }
      else if ( pItem->m_byTableCode != (unsigned __int8)v14 )
      {
        continue;
      }
      v10 = GetItemUpgLimSocket(pItem->m_dwLv);
      if ( v10 >= v15 )
      {
        dwCurBit = GetBitAfterSetLimSocket(v10);
        for ( k = 0; k < v15; ++k )
          dwCurBit = GetBitAfterUpgrade(dwCurBit, 5u, k);
        CPlayer::Emb_ItemUpgrade(v13, 0, 0, j, dwCurBit);
        CPlayer::SendMsg_DeleteStorageInform(v13, 0, pItem->m_wSerial);
        CPlayer::SendMsg_RewardAddItem(v13, pItem, 0);
      }
    }
  }
  return 1;
}
