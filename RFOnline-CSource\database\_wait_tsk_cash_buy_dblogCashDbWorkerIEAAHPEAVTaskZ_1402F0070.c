/*
 * Function: ?_wait_tsk_cash_buy_dblog@CashDbWorker@@IEAAHPEAVTask@@@Z
 * Address: 0x1402F0070
 */

__int64 __fastcall CashDbWorker::_wait_tsk_cash_buy_dblog(CashDbWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@6
  __int64 v6; // [sp+0h] [bp-78h]@1
  char *szItemName; // [sp+20h] [bp-58h]@6
  char byNum; // [sp+28h] [bp-50h]@6
  unsigned int dwCost; // [sp+30h] [bp-48h]@6
  char *v10; // [sp+40h] [bp-38h]@4
  int j; // [sp+48h] [bp-30h]@4
  _base_fld *v12; // [sp+50h] [bp-28h]@6
  __int64 v13; // [sp+58h] [bp-20h]@6
  __int64 v14; // [sp+60h] [bp-18h]@6
  CashDbWorker *v15; // [sp+80h] [bp+8h]@1

  v15 = this;
  v2 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = Task::GetTaskBuf(pkTsk);
  for ( j = 0; j < *((_DWORD *)v10 + 8); ++j )
  {
    v12 = CRecordData::GetRecord(
            (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v10[16 * j + 37],
            *(_WORD *)&v10[16 * j + 38]);
    v13 = 16i64 * j;
    v14 = 16i64 * j;
    v4 = GetItemKorName((unsigned __int8)v10[16 * j + 37], *(_WORD *)&v10[16 * j + 38]);
    dwCost = *(_DWORD *)&v10[v13 + 44];
    byNum = v10[v14 + 40];
    szItemName = v4;
    v10[16 * j + 36] = !CRFCashItemDatabase::CallProc_InsertCashItemLog(
                          v15->_pkDb,
                          *((_DWORD *)v10 + 1),
                          v10[29],
                          v12->m_strCode,
                          v4,
                          byNum,
                          dwCost);
  }
  return 0i64;
}
