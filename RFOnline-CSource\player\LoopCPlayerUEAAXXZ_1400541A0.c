/*
 * Function: ?Loop@CPlayer@@UEAAXXZ
 * Address: 0x1400541A0
 */

void __fastcall CPlayer::Loop(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  LendItemMng *v3; // rax@12
  char *v4; // rax@15
  bool v5; // al@71
  CBillingManager *v6; // rax@77
  CNationSettingManager *v7; // rax@79
  unsigned __int16 v8; // ax@79
  PatriarchElectProcessor *v9; // rax@86
  char *v10; // rax@91
  CUserDB *v11; // rcx@91
  TimeLimitJadeMng *v12; // rax@95
  CItemStoreManager *v13; // rax@116
  char v14; // al@122
  GUILD_BATTLE::CNormalGuildBattleManager *v15; // rax@130
  GUILD_BATTLE::CNormalGuildBattleManager *v16; // rax@132
  GUILD_BATTLE::CNormalGuildBattleManager *v17; // rax@135
  AutominePersonalMgr *v18; // rax@138
  AutominePersonalMgr *v19; // rax@139
  AutominePersonalMgr *v20; // rax@139
  CActionPointSystemMgr *v21; // rax@143
  CActionPointSystemMgr *v22; // rax@144
  CActionPointSystemMgr *v23; // rax@146
  CActionPointSystemMgr *v24; // rax@147
  char v25; // al@152
  int v26; // eax@152
  __int64 v27; // [sp+0h] [bp-188h]@1
  char *pszCause; // [sp+20h] [bp-168h]@81
  int v29; // [sp+28h] [bp-160h]@91
  char *v30; // [sp+30h] [bp-158h]@91
  unsigned int dwCurTime; // [sp+40h] [bp-148h]@12
  LendItemSheet *v32; // [sp+48h] [bp-140h]@12
  int v33; // [sp+50h] [bp-138h]@12
  int j; // [sp+54h] [bp-134h]@17
  _STORAGE_LIST::_storage_con *pCon; // [sp+58h] [bp-130h]@22
  int k; // [sp+60h] [bp-128h]@32
  _STORAGE_LIST::_storage_con *pItem; // [sp+68h] [bp-120h]@37
  __int16 Buf2; // [sp+78h] [bp-110h]@56
  __int16 v39; // [sp+7Ah] [bp-10Eh]@56
  __int16 v40; // [sp+7Ch] [bp-10Ch]@56
  __int16 v41; // [sp+7Eh] [bp-10Ah]@56
  char v42; // [sp+84h] [bp-104h]@74
  char pdata; // [sp+94h] [bp-F4h]@86
  TimeLimitJade *v44; // [sp+A8h] [bp-E0h]@95
  int v45; // [sp+B0h] [bp-D8h]@95
  int v46; // [sp+B4h] [bp-D4h]@95
  _SYSTEMTIME SystemTime; // [sp+C8h] [bp-C0h]@106
  unsigned int dwAccPlayTime; // [sp+E4h] [bp-A4h]@106
  bool *v49; // [sp+E8h] [bp-A0h]@109
  unsigned __int8 v50; // [sp+F0h] [bp-98h]@109
  CMapItemStoreList *v51; // [sp+F8h] [bp-90h]@116
  int l; // [sp+100h] [bp-88h]@117
  CMapData *pIntoMap; // [sp+108h] [bp-80h]@121
  float pfoutPos; // [sp+118h] [bp-70h]@121
  char v55; // [sp+11Ch] [bp-6Ch]@121
  GUILD_BATTLE::CNormalGuildBattle *v56; // [sp+138h] [bp-50h]@130
  int m; // [sp+140h] [bp-48h]@140
  char *v58; // [sp+150h] [bp-38h]@91
  int v59; // [sp+158h] [bp-30h]@109
  int nSerial; // [sp+15Ch] [bp-2Ch]@116
  unsigned int dwGuildSerial; // [sp+160h] [bp-28h]@130
  int n; // [sp+164h] [bp-24h]@138
  int v63; // [sp+168h] [bp-20h]@139
  int v64; // [sp+16Ch] [bp-1Ch]@139
  bool v65; // [sp+170h] [bp-18h]@152
  unsigned __int64 v66; // [sp+178h] [bp-10h]@4
  CPlayer *v67; // [sp+190h] [bp+8h]@1

  v67 = this;
  v1 = &v27;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v66 = (unsigned __int64)&v27 ^ _security_cookie;
  if ( v67->m_bOper )
  {
    if ( CCharacter::GetStealth((CCharacter *)&v67->vfptr, 1) && CPlayer::IsRecallAnimus(v67) )
    {
      if ( CMainThread::IsReleaseServiceMode(&g_Main) )
      {
        if ( !v67->m_byUserDgr )
          CPlayer::_AnimusReturn(v67, 2);
      }
      else
      {
        CPlayer::_AnimusReturn(v67, 2);
      }
    }
    dwCurTime = timeGetTime();
    v3 = LendItemMng::Instance();
    v32 = LendItemMng::GetSheet(v3, v67->m_ObjID.m_wIndex);
    v33 = 0;
    if ( v32 )
      v33 = LendItemSheet::CheckTime(v32);
    if ( v33 )
    {
      v4 = CPlayerDB::GetCharNameW(&v67->m_Param);
      CLogFile::Write(&stru_1799C8E78, "Lend item error : %s >> CheckTime(%d)", v4, (unsigned int)v33);
    }
    CCharacter::UpdateSFCont((CCharacter *)&v67->vfptr);
    CPotionMgr::UpdatePotionContEffect(&g_PotionMgr, v67);
    if ( v67->m_bUpCheckEquipEffect )
    {
      for ( j = 0; j < 15; ++j )
      {
        if ( v67->m_byEffectEquipCode[j] == 2 )
        {
          if ( j >= 8 )
            pCon = (_STORAGE_LIST::_storage_con *)&v67->m_Param.m_dbEmbellish.m_pStorageList[j - 8].m_bLoad;
          else
            pCon = (_STORAGE_LIST::_storage_con *)&v67->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
          if ( pCon->m_bLoad )
          {
            if ( CPlayer::IsEffectableEquip(v67, pCon) )
            {
              CPlayer::SetEquipEffect(v67, pCon, 1);
              v67->m_byEffectEquipCode[j] = 1;
              if ( pCon->m_byTableCode == 6 && !CPlayer::IsRidingUnit(v67) )
                _WEAPON_PARAM::FixWeapon(&v67->m_pmWpn, (_STORAGE_LIST::_db_con *)pCon);
            }
          }
        }
      }
      v67->m_bUpCheckEquipEffect = 0;
    }
    if ( v67->m_bDownCheckEquipEffect )
    {
      for ( k = 0; k < 15; ++k )
      {
        if ( v67->m_byEffectEquipCode[k] == 1 )
        {
          pItem = (_STORAGE_LIST::_storage_con *)(k >= 8 ? &v67->m_Param.m_dbEmbellish.m_pStorageList[k - 8] : &v67->m_Param.m_dbEquip.m_pStorageList[k]);
          if ( pItem->m_bLoad )
          {
            if ( !CPlayer::IsEffectableEquip(v67, pItem) )
            {
              CPlayer::SetEquipEffect(v67, pItem, 0);
              v67->m_byEffectEquipCode[k] = 2;
              if ( pItem->m_byTableCode == 6 && !CPlayer::IsRidingUnit(v67) )
                _WEAPON_PARAM::FixWeapon(&v67->m_pmWpn, 0i64);
            }
          }
        }
      }
      v67->m_bDownCheckEquipEffect = 0;
    }
    if ( v67->m_byNextRecallReturn != 255 )
    {
      CPlayer::_AnimusReturn(v67, v67->m_byNextRecallReturn);
      v67->m_byNextRecallReturn = -1;
    }
    if ( v67->m_dwSelfDestructionTime && v67->m_dwSelfDestructionTime < dwCurTime )
    {
      CPlayer::pc_PlayAttack_SelfDestruction(v67);
      v67->m_dwSelfDestructionTime = 0;
      LODWORD(v67->m_fSelfDestructionDamage) = 0;
    }
    if ( CMyTimer::CountingTimer(&v67->m_tmrIntervalSec) )
    {
      CPlayer::_CheckForcePullUnit(v67);
      if ( v67->m_pCurMap && v67->m_pUserDB && !v67->m_pCurMap->m_pMapSet->m_nMapType )
        CUserDB::Update_Map(v67->m_pUserDB, v67->m_pCurMap->m_pMapSet->m_dwIndex, v67->m_fCurPos);
      CPlayer::CheckPosInTown(v67);
      CPlayer::UpdatedMasteryWriteHistory(v67);
      CQuestMgr::Loop(&v67->m_QuestMgr);
      Buf2 = ((int (__fastcall *)(CPlayer *))v67->vfptr->GetFireTol)(v67);
      v39 = ((int (__fastcall *)(CPlayer *))v67->vfptr->GetWaterTol)(v67);
      v40 = ((int (__fastcall *)(CPlayer *))v67->vfptr->GetSoilTol)(v67);
      v41 = ((int (__fastcall *)(CPlayer *))v67->vfptr->GetWindTol)(v67);
      if ( memcmp_0(v67->m_zLastTol, &Buf2, 8ui64) )
      {
        memcpy_0(v67->m_zLastTol, &Buf2, 8ui64);
        CPlayer::SendMsg_AlterTol(v67);
      }
      v67->m_byDefMatCount = 0;
      CPlayer::_check_hp_send_party(v67);
    }
    CPlayer::UpdateChaosModeState(v67, dwCurTime);
    CPlayer::SenseState(v67);
    if ( v67->m_dwNextTimeDungeonDie && v67->m_dwNextTimeDungeonDie < dwCurTime )
    {
      if ( v67->m_bCorpse && v67->m_pCurMap->m_pMapSet->m_nMapType == 1 )
        CPlayer::pc_Revival(v67, 1);
      v67->m_dwNextTimeDungeonDie = 0;
    }
    if ( v67->m_bMineMode && GetLoopTime() > v67->m_dwMineNextTime )
      CPlayer::pc_MineComplete(v67);
    CPlayer::_check_target_object(v67);
    if ( v67->m_bMove && GetLoopTime() > v67->m_dwLastSetPointTime && GetLoopTime() - v67->m_dwLastSetPointTime > 0xBB8 )
    {
      v5 = CPlayer::IsOutExtraStopPos(v67, v67->m_fCurPos);
      CPlayer::SendMsg_Stop(v67, v5);
      CCharacter::Stop((CCharacter *)&v67->vfptr);
    }
    if ( CMyTimer::CountingTimer(&v67->m_tmrBilling) && !v67->m_pUserDB->m_byUserDgr )
    {
      v42 = 1;
      if ( !v67->m_pUserDB->m_BillingInfo.iType )
        v42 = 0;
      if ( v42 )
      {
        v6 = CTSingleton<CBillingManager>::Instance();
        CBillingManager::Alive(v6, v67->m_pUserDB);
      }
      if ( !v42 )
      {
        CUserDB::SetBillingNoLogout(v67->m_pUserDB, 1);
        v7 = CTSingleton<CNationSettingManager>::Instance();
        v8 = CNationSettingManager::GetBillingForceCloseDelay(v7);
        CPlayer::SendMsg_BillingExipreInform(v67, 0, v8);
        CPlayer::ReservationForceClose(v67);
      }
    }
    if ( (signed int)CPlayerDB::GetCurItemSerial(&v67->m_Param) > 60000 )
    {
      CMgrAvatorItemHistory::item_serial_full(
        &CPlayer::s_MgrItemHistory,
        v67->m_ObjID.m_wIndex,
        v67->m_szItemHistoryFileName);
      pszCause = "Item Serial Count Over";
      CUserDB::ForceCloseCommand(v67->m_pUserDB, 7, 0xFFFFFFFF, 1, "Item Serial Count Over");
    }
    if ( CMyTimer::CountingTimer(&v67->m_tmrGroupTargeting) )
    {
      CPlayer::_check_party_target_object(v67);
      CPlayer::_check_guild_target_object(v67);
      CPlayer::_check_race_target_object(v67);
    }
    if ( v67->m_byPatriarchAppointPropose != 255 && dwCurTime - v67->m_dwPatriarchAppointTime >= 0x493E0 )
    {
      pdata = 1;
      v9 = PatriarchElectProcessor::Instance();
      PatriarchElectProcessor::Doit(v9, _eRespAppoint, v67, &pdata);
    }
    if ( dwCurTime - v67->m_dwLastCheckRegionTime > 0x1388 )
    {
      if ( CGameObject::GetCurSecNum((CGameObject *)&v67->vfptr) != -1 )
        CPlayer::CheckPos_Region(v67);
      if ( v67->m_nCheckMovePacket > 17 )
      {
        v67->m_bCheckMovePacket = 1;
        v58 = v67->m_pCurMap->m_pMapSet->m_strCode;
        v10 = CPlayerDB::GetCharNameA(&v67->m_Param);
        v11 = v67->m_pUserDB;
        v30 = v58;
        v29 = v67->m_nCheckMovePacket;
        LODWORD(pszCause) = v67->m_dwObjSerial;
        CLogFile::Write(&stru_1799C9660, "id: %s >> %s ( %d ), Move Count : %d, map(%s)", v11->m_szAccountID, v10);
      }
      v67->m_nCheckMovePacket = 0;
    }
    CPlayer::CheckAlterMaxPoint(v67);
    CPvpOrderView::Loop(&v67->m_kPvpOrderView, v67->m_ObjID.m_wIndex);
    CPlayer::CheckBattleMode(v67);
    if ( CPlayer::IsApplyPcbangPrimium(v67) )
      CCouponMgr::Loop(&v67->m_kPcBangCoupon, v67->m_ObjID.m_wIndex);
    v12 = TimeLimitJadeMng::Instance();
    v44 = TimeLimitJadeMng::GetSheet(v12, v67->m_ObjID.m_wIndex);
    v45 = 0;
    v46 = 0;
    if ( v44 && CMyTimer::CountingTimer(&v67->m_tmrEffectEndTime) )
    {
      v45 = TimeLimitJade::CheckStartTime(v44);
      v46 = TimeLimitJade::CheckEndTime(v44);
    }
    if ( CMyTimer::CountingTimer(&v67->m_tmrSiegeTime) )
    {
      v67->m_bIsSiegeActing = 0;
      CMyTimer::StopTimer(&v67->m_tmrSiegeTime);
    }
    if ( CExtPotionBuf::IsExtPotionUse(&v67->m_PotionBufUse) )
      CExtPotionBuf::CheckPotionTime(&v67->m_PotionBufUse, v67);
    if ( v67->m_Param.m_bTrunkOpen && !IsBeNearStore(v67, 10) )
      v67->m_Param.m_bTrunkOpen = 0;
    CPlayer::CheckNameChange(v67);
    if ( CMyTimer::CountingTimer(&v67->m_tmrAccumPlayingTime) )
    {
      GetLocalTime(&SystemTime);
      dwAccPlayTime = CPlayer::SumMinuteBetween(v67, &v67->m_tmCalcTime, &SystemTime);
      memcpy_s(&v67->m_tmCalcTime, 0x10ui64, &SystemTime, 0x10ui64);
      CUserDB::Update_UserPlayTime(v67->m_pUserDB, dwAccPlayTime);
    }
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v67->m_id.wIndex) == 99 )
    {
      if ( v67->m_pDHChannel )
      {
        v49 = 0i64;
        v50 = -1;
        v59 = CPlayerDB::GetRaceCode(&v67->m_Param);
        if ( v59 )
        {
          if ( v59 == 1 )
          {
            v50 = 1;
          }
          else if ( v59 == 2 )
          {
            v50 = 3;
          }
        }
        else
        {
          v50 = 0;
        }
        nSerial = v50;
        v13 = CItemStoreManager::Instance();
        v51 = CItemStoreManager::GetMapItemStoreListBySerial(v13, nSerial);
        if ( v51 )
        {
          for ( l = 0; l < v51->m_nItemStoreNum; ++l )
            v49 = &v51->m_ItemStore[l].m_bLive;
          if ( v67->m_bCorpse )
          {
            CPlayer::pc_Revival(v67, 1);
          }
          else
          {
            pIntoMap = 0i64;
            pfoutPos = 0.0;
            memset(&v55, 0, 8ui64);
            if ( v67->m_bLive )
            {
              v14 = CPlayerDB::GetRaceCode(&v67->m_Param);
              pIntoMap = CMapOperation::GetPosStartMap(&g_MapOper, v14, 0, &pfoutPos);
              if ( pIntoMap )
              {
                CPlayer::pc_Resurrect(v67, 0);
                CPlayer::ForcePullUnit(v67, 0);
                CPlayer::OutOfMap(v67, pIntoMap, 0, 3, &pfoutPos);
                CPlayer::SendMsg_GotoBasePortalResult(v67, 0);
              }
            }
          }
        }
        CPlayer::SendMsg_TLStatusPenalty(v67, 7);
      }
      if ( CPartyPlayer::IsPartyMode(v67->m_pPartyMgr) )
      {
        CPlayer::SendMsg_PartyLeaveSelfResult(v67, 0i64, 0);
        wa_PartySelfLeave(&v67->m_id);
        CPlayer::SendMsg_TLStatusPenalty(v67, 2);
      }
      if ( v67->m_bInGuildBattle )
      {
        v56 = 0i64;
        dwGuildSerial = CPlayerDB::GetGuildSerial(&v67->m_Param);
        v15 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
        v56 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v15, dwGuildSerial);
        if ( v56 && GUILD_BATTLE::CNormalGuildBattle::IsReadyOrCountState(v56) )
        {
          v16 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
          GUILD_BATTLE::CNormalGuildBattleManager::LeaveGuild(v16, v67);
          CPlayer::SendMsg_TLStatusPenalty(v67, 6);
        }
        if ( v56 && GUILD_BATTLE::CNormalGuildBattle::IsInBattle(v56) )
        {
          v17 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
          GUILD_BATTLE::CNormalGuildBattleManager::LeaveGuild(v17, v67);
          CPlayer::SendMsg_TLStatusPenalty(v67, 6);
        }
      }
      if ( v67->m_bMineMode )
      {
        v67->m_bMineMode = 0;
        v67->m_dwMineNextTime = -1;
        CPlayer::SendMsg_MineCancle(v67);
        CPlayer::SendMsg_TLStatusPenalty(v67, 5);
      }
      n = v67->m_id.wIndex;
      v18 = AutominePersonalMgr::instance();
      if ( AutominePersonalMgr::Is_MineRun(v18, n) )
      {
        v63 = v67->m_id.wIndex;
        v19 = AutominePersonalMgr::instance();
        AutominePersonalMgr::uninstall(v19, v63);
        v64 = v67->m_id.wIndex;
        v20 = AutominePersonalMgr::instance();
        AutominePersonalMgr::extract_battery(v20, v64);
        CPlayer::SendMsg_TLStatusPenalty(v67, 5);
      }
    }
    for ( m = 0; m < 3; ++m )
    {
      v21 = CActionPointSystemMgr::Instance();
      if ( CActionPointSystemMgr::IsSystemEnable(v21, m) )
      {
        v22 = CActionPointSystemMgr::Instance();
        if ( CActionPointSystemMgr::IsPointReset(v22, m) )
        {
          if ( CUserDB::GetActPoint(v67->m_pUserDB, m) )
          {
            v23 = CActionPointSystemMgr::Instance();
            if ( CActionPointSystemMgr::GetEventStatus(v23, m) == 3
              || (v24 = CActionPointSystemMgr::Instance(), !CActionPointSystemMgr::GetEventStatus(v24, m)) )
            {
              CUserDB::Update_User_Action_Point(v67->m_pUserDB, m, 0);
              CPlayer::SendMsg_Alter_Action_Point(v67, m, 0);
            }
          }
        }
      }
    }
    if ( CMyTimer::CountingTimer(&v67->m_tmrPremiumPVPInform) && CPlayer::IsApplyPcbangPrimium(v67) )
    {
      v65 = CPlayer::IsApplyPcbangPrimium(v67);
      v25 = CPlayerDB::GetLevel(&v67->m_Param);
      v26 = CPvpCashPoint::GetMaxTempPoint(&v67->m_kPvpCashPoint, v25, v65);
      CPlayer::SendMsg_MaxPvpPointInform(v67, v26);
    }
  }
}
