/*
 * Function: ?make_skill_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAMEPEAU_skill_fld@@HPEAU_db_con@_STORAGE_LIST@@MPEAU_attack_param@@3M@Z
 * Address: 0x140088340
 */

void __usercall CPlayer::make_skill_attack_param(CPlayer *this@<rcx>, CCharacter *pDst@<rdx>, float *pfAttackPos@<r8>, char byEffectCode@<r9b>, float a5@<xmm0>, _skill_fld *pSkillFld, int nAttType, _STORAGE_LIST::_db_con *pBulletItem, float fAddBulletFc, _attack_param *pAP, _STORAGE_LIST::_db_con *pEffBulletItem, float fAddEffBulletFc)
{
  __int64 *v12; // rdi@1
  signed __int64 i; // rcx@1
  double v14; // xmm0_8@13
  float v15; // xmm0_4@15
  int v16; // eax@15
  float v17; // xmm0_4@15
  int v18; // eax@15
  float v19; // xmm0_4@15
  float v20; // xmm0_4@16
  int v21; // eax@16
  float v22; // xmm0_4@16
  int v23; // eax@16
  float v24; // xmm0_4@20
  int v25; // eax@20
  float v26; // xmm0_4@20
  int v27; // eax@20
  float v28; // xmm0_4@21
  int v29; // eax@21
  float v30; // xmm0_4@21
  int v31; // eax@21
  float v32; // xmm0_4@23
  __int64 v33; // [sp+0h] [bp-98h]@1
  double v34; // [sp+20h] [bp-78h]@13
  _base_fld *v35; // [sp+30h] [bp-68h]@8
  _base_fld *v36; // [sp+38h] [bp-60h]@23
  double v37; // [sp+40h] [bp-58h]@13
  float v38; // [sp+48h] [bp-50h]@15
  float v39; // [sp+4Ch] [bp-4Ch]@15
  float v40; // [sp+50h] [bp-48h]@15
  float v41; // [sp+54h] [bp-44h]@15
  float v42; // [sp+58h] [bp-40h]@16
  float v43; // [sp+5Ch] [bp-3Ch]@16
  float v44; // [sp+60h] [bp-38h]@16
  float v45; // [sp+64h] [bp-34h]@16
  float v46; // [sp+68h] [bp-30h]@20
  float v47; // [sp+6Ch] [bp-2Ch]@20
  float v48; // [sp+70h] [bp-28h]@20
  float v49; // [sp+74h] [bp-24h]@20
  float v50; // [sp+78h] [bp-20h]@21
  float v51; // [sp+7Ch] [bp-1Ch]@21
  float v52; // [sp+80h] [bp-18h]@21
  float v53; // [sp+84h] [bp-14h]@21
  CPlayer *pTarget; // [sp+A0h] [bp+8h]@1
  CMonster *v55; // [sp+A8h] [bp+10h]@1
  float *Src; // [sp+B0h] [bp+18h]@1
  char v57; // [sp+B8h] [bp+20h]@1

  v57 = byEffectCode;
  Src = pfAttackPos;
  v55 = (CMonster *)pDst;
  pTarget = this;
  v12 = &v33;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v12 = -858993460;
    v12 = (__int64 *)((char *)v12 + 4);
  }
  pAP->pDst = pDst;
  if ( pDst )
    pAP->nPart = CCharacter::GetAttackRandomPart(pDst);
  else
    pAP->nPart = CCharacter::GetAttackRandomPart((CCharacter *)&pTarget->vfptr);
  if ( pBulletItem )
  {
    v35 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, pBulletItem->m_wItemIndex);
    pAP->nTol = *(_DWORD *)&v35[6].m_strCode[40];
  }
  else
  {
    pAP->nTol = pTarget->m_pmWpn.byAttTolType;
  }
  pAP->nClass = pTarget->m_pmWpn.byWpClass;
  if ( pTarget->m_pmWpn.nGaMaxAF < 0
    || (_effect_parameter::GetEff_Rate(&pTarget->m_EP, 32), a5 < 0.0)
    || fAddBulletFc < 0.0 )
  {
    v14 = fAddBulletFc;
    v37 = fAddBulletFc;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v34 = v37;
    CLogFile::Write(
      &stru_1799C8E78,
      "Skill Attack Warning : nGaMaxAF(%d), Potion_Inc_Fc(%f), fAddBulletFc(%f)",
      pTarget->m_pmWpn.nGaMaxAF,
      *(float *)&v14);
  }
  if ( pTarget->m_pmWpn.byWpType == 7 )
  {
    v42 = (float)pTarget->m_pmWpn.nGaMinAF;
    v20 = v42;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v43 = (float)(v42 * v20) * fAddBulletFc;
    v21 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 6, 0);
    pAP->nMinAF = (signed int)ffloor(v43 + (float)v21);
    v44 = (float)pTarget->m_pmWpn.nGaMaxAF;
    v22 = v44;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v45 = (float)(v44 * v22) * fAddBulletFc;
    v23 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 6, 0);
    v19 = v45 + (float)v23;
    pAP->nMaxAF = (signed int)ffloor(v19);
  }
  else
  {
    v38 = (float)pTarget->m_pmWpn.nGaMinAF;
    v15 = v38;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v39 = (float)(v38 * v15) * fAddBulletFc;
    v16 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 0, pTarget->m_pmWpn.byWpClass);
    pAP->nMinAF = (signed int)ffloor(v39 + (float)v16);
    v40 = (float)pTarget->m_pmWpn.nGaMaxAF;
    v17 = v40;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v41 = (float)(v40 * v17) * fAddBulletFc;
    v18 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 0, pTarget->m_pmWpn.byWpClass);
    v19 = v41 + (float)v18;
    pAP->nMaxAF = (signed int)ffloor(v19);
  }
  if ( strncmp(pTarget->m_pmWpn.strEffBulletType, "-1", 2ui64) && pEffBulletItem )
  {
    if ( pTarget->m_pmWpn.byWpType == 7 )
    {
      v50 = (float)pTarget->m_pmWpn.nGaMinAF;
      v28 = v50;
      _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
      v51 = (float)((float)(v50 * v28) * fAddBulletFc) * fAddEffBulletFc;
      v29 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 6, 0);
      pAP->nMinAFPlus = (signed int)ffloor(v51 + (float)v29);
      v52 = (float)pTarget->m_pmWpn.nGaMaxAF;
      v30 = v52;
      _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
      v53 = (float)((float)(v52 * v30) * fAddBulletFc) * fAddEffBulletFc;
      v31 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 6, 0);
      v19 = v53 + (float)v31;
      pAP->nMaxAFPlus = (signed int)ffloor(v19);
    }
    else
    {
      v46 = (float)pTarget->m_pmWpn.nGaMinAF;
      v24 = v46;
      _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
      v47 = (float)((float)(v46 * v24) * fAddBulletFc) * fAddEffBulletFc;
      v25 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 0, pTarget->m_pmWpn.byWpClass);
      pAP->nMinAFPlus = (signed int)ffloor(v47 + (float)v25);
      v48 = (float)pTarget->m_pmWpn.nGaMaxAF;
      v26 = v48;
      _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
      v49 = (float)((float)(v48 * v26) * fAddBulletFc) * fAddEffBulletFc;
      v27 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 0, pTarget->m_pmWpn.byWpClass);
      v19 = v49 + (float)v27;
      pAP->nMaxAFPlus = (signed int)ffloor(v19);
    }
  }
  if ( CPlayer::IsSiegeMode(pTarget) )
  {
    v36 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, pTarget->m_pSiegeItem->m_wItemIndex);
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 23);
    v32 = (float)pAP->nMinAF * (float)(*(float *)&v36[5].m_strCode[24] * v19);
    pAP->nMinAF = (signed int)ffloor(v32);
    if ( strncmp(pTarget->m_pmWpn.strEffBulletType, "-1", 2ui64) && pEffBulletItem )
    {
      _effect_parameter::GetEff_Rate(&pTarget->m_EP, 23);
      v32 = (float)pAP->nMinAFPlus * (float)(*(float *)&v36[5].m_strCode[24] * v32);
      pAP->nMinAFPlus = (signed int)ffloor(v32);
    }
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 23);
    v19 = (float)pAP->nMaxAF * (float)(*(float *)&v36[5].m_strCode[24] * v32);
    pAP->nMaxAF = (signed int)ffloor(v19);
    if ( strncmp(pTarget->m_pmWpn.strEffBulletType, "-1", 2ui64) && pEffBulletItem )
    {
      _effect_parameter::GetEff_Rate(&pTarget->m_EP, 23);
      v19 = (float)pAP->nMaxAFPlus * (float)(*(float *)&v36[5].m_strCode[24] * v19);
      pAP->nMaxAFPlus = (signed int)ffloor(v19);
    }
  }
  pAP->nMinSel = pTarget->m_pmWpn.byGaMinSel;
  pAP->nMaxSel = pTarget->m_pmWpn.byGaMaxSel;
  pAP->nExtentRange = 20;
  if ( pBulletItem )
  {
    pAP->nShotNum = pSkillFld->m_nAttNeedBt;
    if ( pBulletItem->m_dwDur < pSkillFld->m_nAttNeedBt )
      pAP->nShotNum = pBulletItem->m_dwDur;
  }
  if ( pEffBulletItem )
  {
    pAP->nEffShotNum = pSkillFld->m_nAttNeedBt;
    if ( pEffBulletItem->m_dwDur < pSkillFld->m_nAttNeedBt )
      pAP->nEffShotNum = pEffBulletItem->m_dwDur;
  }
  if ( nAttType == 3 )
  {
    v19 = (float)((int (__fastcall *)(CPlayer *))pTarget->vfptr->GetHP)(pTarget) * 0.89999998;
    pAP->nAddAttPnt = (signed int)ffloor(v19);
  }
  pAP->pFld = (_base_fld *)pSkillFld;
  pAP->byEffectCode = v57;
  if ( v57 )
  {
    pAP->nLevel = 1;
    pAP->nMastery = 99;
  }
  else
  {
    pAP->nLevel = _MASTERY_PARAM::GetSkillLv(&pTarget->m_pmMst, pSkillFld->m_dwIndex);
    _effect_parameter::GetEff_Plus(&pTarget->m_EP, 19);
    pAP->nLevel = (signed int)ffloor((float)pAP->nLevel + v19);
    if ( pAP->nLevel > 7 )
      pAP->nLevel = 7;
    pAP->nMastery = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 3, pSkillFld->m_nMastIndex);
  }
  memcpy_0(pAP->fArea, Src, 0xCui64);
  pAP->nMaxAttackPnt = pTarget->m_nMaxAttackPnt;
  if ( v55 && v55->m_ObjID.m_byKind == 1 && !CMonster::IsViewArea(v55, (CCharacter *)&pTarget->vfptr) )
    pAP->bBackAttack = 1;
}
