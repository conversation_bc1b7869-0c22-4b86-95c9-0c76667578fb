/*
 * Function: ?_Destroy@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAXPEAPEAVCLogTypeDBTask@@0@Z
 * Address: 0x1402C58D0
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Destroy(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask **_First, CLogTypeDBTask **_Last)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Destroy_range<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(_First, _Last, &v6->_Alval);
}
