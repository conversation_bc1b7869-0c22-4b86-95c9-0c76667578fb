/*
 * Function: _std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::_Umove_std::_Vector_iterator_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64______::_1_::dtor$2
 * Address: 0x1402C6D50
 */

void __fastcall std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::_Umove_std::_Vector_iterator_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64______::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(*(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > **)(a2 + 64));
}
