/*
 * Function: j_?SetPrivateExponent@?$DL_PrivateKeyImpl@V?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@@CryptoPP@@UEAAXAEBVInteger@2@@Z
 * Address: 0x140002DDD
 */

void __fastcall CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>::SetPrivateExponent(CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> > *this, CryptoPP::Integer *x)
{
  CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>::SetPrivateExponent(this, x);
}
