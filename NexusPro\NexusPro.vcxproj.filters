<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Core">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Headers">
      <UniqueIdentifier>{1FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Core\Sources">
      <UniqueIdentifier>{2FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Utils">
      <UniqueIdentifier>{3FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Utils\Headers">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Utils\Sources">
      <UniqueIdentifier>{5FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules">
      <UniqueIdentifier>{6FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules\Authentication">
      <UniqueIdentifier>{7FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules\BugFix">
      <UniqueIdentifier>{8FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration">
      <UniqueIdentifier>{9FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Documentation">
      <UniqueIdentifier>{AFC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Build">
      <UniqueIdentifier>{BFC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  
  <!-- Header Files -->
  <ItemGroup>
    <ClInclude Include="pch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  
  <!-- Core Headers -->
  <ItemGroup>
    <ClInclude Include="Core\HookManager.h">
      <Filter>Core\Headers</Filter>
    </ClInclude>
    <ClInclude Include="Core\MemoryPatcher.h">
      <Filter>Core\Headers</Filter>
    </ClInclude>
    <ClInclude Include="Core\ConfigManager.h">
      <Filter>Core\Headers</Filter>
    </ClInclude>
  </ItemGroup>
  
  <!-- Utils Headers -->
  <ItemGroup>
    <ClInclude Include="Utils\Logger.h">
      <Filter>Utils\Headers</Filter>
    </ClInclude>
    <ClInclude Include="Utils\AddressResolver.h">
      <Filter>Utils\Headers</Filter>
    </ClInclude>
    <ClInclude Include="Utils\Common.h">
      <Filter>Utils\Headers</Filter>
    </ClInclude>
    <ClInclude Include="Utils\RFTypes.h">
      <Filter>Utils\Headers</Filter>
    </ClInclude>
  </ItemGroup>
  
  <!-- Modules Headers -->
  <ItemGroup>
    <ClInclude Include="Modules\Authentication\AuthenticationModule.h">
      <Filter>Modules\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="Modules\BugFix\BugFixModule.h">
      <Filter>Modules\BugFix</Filter>
    </ClInclude>
  </ItemGroup>
  
  <!-- Source Files -->
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  
  <!-- Core Sources -->
  <ItemGroup>
    <ClCompile Include="Core\HookManager.cpp">
      <Filter>Core\Sources</Filter>
    </ClCompile>
    <ClCompile Include="Core\MemoryPatcher.cpp">
      <Filter>Core\Sources</Filter>
    </ClCompile>
    <ClCompile Include="Core\ConfigManager.cpp">
      <Filter>Core\Sources</Filter>
    </ClCompile>
  </ItemGroup>
  
  <!-- Utils Sources -->
  <ItemGroup>
    <ClCompile Include="Utils\Logger.cpp">
      <Filter>Utils\Sources</Filter>
    </ClCompile>
    <ClCompile Include="Utils\AddressResolver.cpp">
      <Filter>Utils\Sources</Filter>
    </ClCompile>
  </ItemGroup>

  <!-- Modules Sources -->
  <ItemGroup>
    <ClCompile Include="Modules\Authentication\AuthenticationModule.cpp">
      <Filter>Modules\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="Modules\BugFix\BugFixModule.cpp">
      <Filter>Modules\BugFix</Filter>
    </ClCompile>
  </ItemGroup>
  
  <!-- Configuration Files -->
  <ItemGroup>
    <None Include="Config\nexuspro.ini">
      <Filter>Configuration</Filter>
    </None>
    <None Include="README.md">
      <Filter>Documentation</Filter>
    </None>
    <None Include=".gitignore">
      <Filter>Configuration</Filter>
    </None>
  </ItemGroup>
  
  <!-- Documentation -->
  <ItemGroup>
    <None Include="Docs\README_DEVELOPMENT.md">
      <Filter>Documentation</Filter>
    </None>
    <None Include="Docs\RF_ANALYSIS_SUMMARY.md">
      <Filter>Documentation</Filter>
    </None>
  </ItemGroup>
  
  <!-- Build Scripts -->
  <ItemGroup>
    <None Include="Build\build.bat">
      <Filter>Build</Filter>
    </None>
  </ItemGroup>
</Project>
