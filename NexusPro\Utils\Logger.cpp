#include "pch.h"
#include "Logger.h"

namespace NexusPro {
    
    std::unique_ptr<Logger> Logger::instance_;
    std::mutex Logger::instanceMutex_;
    
    void Logger::Initialize(const std::wstring& logFileName) {
        std::lock_guard<std::mutex> lock(instanceMutex_);
        if (!instance_) {
            instance_ = std::unique_ptr<Logger>(new Logger());
            
            // Set up log file path
            instance_->logFilePath_ = Utils::GetModuleDirectory() + logFileName;
            
            // Open log file
            instance_->logFile_.open(instance_->logFilePath_, std::ios::out | std::ios::app);
            if (instance_->logFile_.is_open()) {
                instance_->logFile_.imbue(std::locale(""));
                instance_->Log(LogLevel::Info, L"=== NexusPro Logger Initialized ===");
            }
        }
    }
    
    void Logger::Shutdown() {
        std::lock_guard<std::mutex> lock(instanceMutex_);
        if (instance_) {
            instance_->Log(LogLevel::Info, L"=== NexusPro Logger Shutdown ===");
            if (instance_->logFile_.is_open()) {
                instance_->logFile_.close();
            }
            instance_.reset();
        }
    }
    
    Logger& Logger::GetInstance() {
        std::lock_guard<std::mutex> lock(instanceMutex_);
        if (!instance_) {
            Initialize();
        }
        return *instance_;
    }
    
    void Logger::Log(LogLevel level, const std::wstring& message) {
        if (level < currentLevel_) {
            return;
        }
        
        std::lock_guard<std::mutex> lock(logMutex_);
        
        std::wstring timestamp = Utils::GetCurrentTimeString();
        std::wstring levelStr = GetLevelString(level);
        std::wstring fullMessage = L"[" + timestamp + L"] [" + levelStr + L"] " + message;
        
        if (fileOutput_) {
            WriteToFile(fullMessage);
        }
        
        if (consoleOutput_) {
            WriteToConsole(fullMessage);
        }
    }
    
    std::wstring Logger::GetLevelString(LogLevel level) {
        switch (level) {
            case LogLevel::Trace:    return L"TRACE";
            case LogLevel::Debug:    return L"DEBUG";
            case LogLevel::Info:     return L"INFO ";
            case LogLevel::Warning:  return L"WARN ";
            case LogLevel::Error:    return L"ERROR";
            case LogLevel::Critical: return L"CRIT ";
            default:                 return L"UNKN ";
        }
    }
    
    void Logger::WriteToFile(const std::wstring& message) {
        if (logFile_.is_open()) {
            logFile_ << message << std::endl;
            logFile_.flush();
        }
    }
    
    void Logger::WriteToConsole(const std::wstring& message) {
        // Allocate console if not already allocated
        static bool consoleAllocated = false;
        if (!consoleAllocated) {
            if (AllocConsole()) {
                FILE* pCout;
                freopen_s(&pCout, "CONOUT$", "w", stdout);
                consoleAllocated = true;
            }
        }
        
        if (consoleAllocated) {
            std::wcout << message << std::endl;
        }
    }
    
    void Logger::FlushLog() {
        std::lock_guard<std::mutex> lock(logMutex_);
        if (logFile_.is_open()) {
            logFile_.flush();
        }
    }
}
