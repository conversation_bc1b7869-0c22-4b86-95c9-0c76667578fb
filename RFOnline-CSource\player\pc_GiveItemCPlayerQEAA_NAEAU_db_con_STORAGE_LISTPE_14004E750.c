/*
 * Function: ?pc_GiveItem@CPlayer@@QEAA_NAEAU_db_con@_STORAGE_LIST@@PEAD_N@Z
 * Address: 0x14004E750
 */

char __fastcall CPlayer::pc_GiveItem(CPlayer *this, _STORAGE_LIST::_db_con *kItem, char *szReason, bool bDrop)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-68h]@1
  CMapData *pMap; // [sp+30h] [bp-38h]@11
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-30h]@11
  float *pStdPos; // [sp+40h] [bp-28h]@11
  bool bHide; // [sp+48h] [bp-20h]@11
  const char *v12; // [sp+50h] [bp-18h]@4
  char v13; // [sp+58h] [bp-10h]@6
  CPlayer *pOwner; // [sp+70h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pCon; // [sp+78h] [bp+10h]@1
  char *pszClause; // [sp+80h] [bp+18h]@1
  bool v17; // [sp+88h] [bp+20h]@1

  v17 = bDrop;
  pszClause = szReason;
  pCon = kItem;
  pOwner = this;
  v4 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = "NONE";
  if ( !szReason )
    pszClause = (char *)v12;
  v13 = 1;
  if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum) == 255 )
  {
    if ( v17 )
    {
      bHide = 0;
      pStdPos = pOwner->m_fCurPos;
      wLayerIndex = pOwner->m_wMapLayerIndex;
      pMap = pOwner->m_pCurMap;
      if ( !CreateItemBox(pCon, pOwner, 0xFFFFFFFF, 0, 0i64, 3, pMap, wLayerIndex, pOwner->m_fCurPos, 0) )
        v13 = 0;
      CMgrAvatorItemHistory::reward_add_item(
        &CPlayer::s_MgrItemHistory,
        pOwner->m_ObjID.m_wIndex,
        pszClause,
        pCon,
        pOwner->m_szItemHistoryFileName);
    }
  }
  else
  {
    pCon->m_wSerial = CPlayerDB::GetNewItemSerial(&pOwner->m_Param);
    if ( !CPlayer::Emb_AddStorage(pOwner, 0, (_STORAGE_LIST::_storage_con *)&pCon->m_bLoad, 0, 1) )
      v13 = 0;
    CPlayer::SendMsg_RewardAddItem(pOwner, pCon, 4);
    CMgrAvatorItemHistory::reward_add_item(
      &CPlayer::s_MgrItemHistory,
      pOwner->m_ObjID.m_wIndex,
      pszClause,
      pCon,
      pOwner->m_szItemHistoryFileName);
  }
  return v13;
}
