/*
 * Function: ?Clear@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@QEAAXI@Z
 * Address: 0x1403CE220
 */

void __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::Clear(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this, unsigned int uiMapID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v5->m_bInit )
  {
    if ( v5->m_uiMapCnt > uiMapID )
    {
      v5->m_pbUpdate[uiMapID] = 0;
      memset_0(&v5->m_pkInfo[uiMapID], 0, 0x35ui64);
    }
  }
}
